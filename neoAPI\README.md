# Welcome to the Baumer neoAPI for Python

This package provides you with everything you need to program a Baumer camera with the Baumer neoAPI. If You are looking for a package for C++ or Microsoft C#, please download it separately from the [Baumer web site](https://www.baumer.com/c/333)

The Baumer neoAPI for Python is compatible to Python version 2.7 and 3.4 upwards. However, we strongly recommend using it with current Python version 3.9. Please be aware, that neoAPI does require a cPython implementation. So packages like winpython will not work.

## Prerequisites

- Download the package for your operating system and architecture [here](https://www.baumer.com/c/333)
- Have a suitable host system (64 bit Windows or Linux) or ARM board (AArch64 Linux) with at least one Gigabit Ethernet or USB3 port ready
- Have a Baumer GigE or USB camera including necessary cables and suitable power supply to play with
- Have a suitable Python development environment of your choice set up
- Optionally you can download and install the Baumer Camera Explorer, a graphical tool which will help you to understand and work with cameras
- Some provided examples require OpenCV (at least Version 3), if you want to build them you need to install it using pip

## Install the required drivers

- Windows
  - If USB cameras are used it is necessary to install the USB-driver provided with the package (see `/drivers/`)
  - For GigE cameras the Baumer filter-driver reduces the system-load compared to the Windows socket driver. We recommend installing and using the filter-driver provided with the package (see `/drivers/`)
  - For further information how to get the most performance out a GigE connection on a Windows system please refer to the application notes [AN201622](https://www.baumer.com/a/gigabit-ethernet-adapter-settings) and [AN201802](https://www.baumer.com/a/10-gige-and-baumer-gapi).
- Linux
  - If you are using Linux no driver needs to be installed.
  - To provide non root users access to usb devices copy the provided udev-rules (see `/drivers/udev_rules/`) in the drivers folder to `/etc/udev/rules.d/`.
  - Restart the system for the changed udev-rules to take effect
  - As the Linux kernel usually sets a 16 MB memory limit for the USB system, it might be necessary to raise the memory limit, especially for multi-camera systems. Please see the application note [AN201707](https://www.baumer.com/a/bgapi-sdk-for-usb-multi-camera-systems-with-linux) for details.
  - To prevent resend requests for lost pakets you should check udp and memory settings f.i. by `sysctl -a | grep mem`. To secure enough storage space add to /etc/sysctl.conf:
~~~
### TUNING NETWORK PERFORMANCE ###
# Maximum Socket Receive Buffer
net.core.rmem_max = 67108864
~~~

> __Attention__
>
> If you have trouble connecting to a Baumer GigE camera, it might be necessary to configure the network settings of the camera first. You'll find the network configuration tool `gevipconfig` in the tools folder of this package.

# Install the neoAPI Python wheel

## Step one — create virtual python environment (optional)

Creating a virtual environment for Python is not strictly necessary but highly recommended. It ensures, that you don't mix requirements from different projects.

Windows

- Download and install a python version of your choice
- Go to a folder where you want to create the virtual environment in
- Create an environment with a chosen name `python -m venv <venv_name>`, it will be created in a folder with the chosen name
- Activate the environment by calling `<venv_name>/Scripts/activate`
- Your command prompt should now be prefixed with `(<venv_name>)`
- You can deactivate the virtual environment again by calling `<venv_name>/Scripts/deactivate.bat`

Linux (Ubuntu)

- Install a python version of your choice using the package manager `sudo apt-get install python3`
- Install the virtualenv module `sudo apt-get install python3-virtualenv`
- Create an environment with a chosen name `virtualenv -p /usr/bin/python3 <venv_name>`, it will be created in a folder with the chosen name
- Activate the environment by calling `source <venv_name>/bin/activate`
- Your command prompt should now be prefixed with `(<venv_name>)`
- You can deactivate the virtual environment again by calling `deactivate`

## Step two — install the right wheel for your virtual python environment

- Activate your created virtual Python environment (see step one)
- Find out which Python version you have installed by running `python3 --version` in a command line window
- Change to the folder where you unpacked the neoAPI wheel files (*.whl)
- run `python3 -m pip install neoapi_<neoapi-version>-cp<python-version>-cp<python-version>m-<platform>.whl` to install the neoAPI

> __Attention__
>
> Don't use neoapi from PyPI! This is a different vendors API and not the Baumer package you downloaded!

Now you are ready to start programming!

# Try the examples

Once you activated the virtual environment created above and installed the neoAPI wheel you can run the provided examples. Some examples might require further pip-packages to be installed.

# License

Please see the /License file for licensing information.
