<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members - Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_b"></a>- b -</h3><ul>
<li>BalanceWhiteAuto()
: <a class="el" href="a00763.html#aa4acfab91685aab4b7ff67f133980d57">neoapi.FeatureAccess</a>
</li>
<li>BalanceWhiteAutoStatus()
: <a class="el" href="a00763.html#a5768fba88121a233aa37a2f04cd46b2f">neoapi.FeatureAccess</a>
</li>
<li>Baudrate()
: <a class="el" href="a00763.html#ade66343cb587a3cc65463693d65c5609">neoapi.FeatureAccess</a>
</li>
<li>BinningHorizontal()
: <a class="el" href="a00763.html#af1902deefb9e9ce9941d7a9e4926e7ed">neoapi.FeatureAccess</a>
</li>
<li>BinningHorizontalMode()
: <a class="el" href="a00763.html#afb6ed19e352cbfed7521fe40baea0199">neoapi.FeatureAccess</a>
</li>
<li>BinningRegion0()
: <a class="el" href="a00763.html#a92918e235305c1570f0ac7b1f001b7d5">neoapi.FeatureAccess</a>
</li>
<li>BinningSelector()
: <a class="el" href="a00763.html#a967434d4da377064d41dd72c08f37d08">neoapi.FeatureAccess</a>
</li>
<li>BinningSensor()
: <a class="el" href="a00763.html#a30890329d10b823d2390a4edeeec150a">neoapi.FeatureAccess</a>
</li>
<li>BinningVertical()
: <a class="el" href="a00763.html#a010da2a80ada126be5b79f345ef42720">neoapi.FeatureAccess</a>
</li>
<li>BinningVerticalMode()
: <a class="el" href="a00763.html#af7ef0b3275b172d8dd4c087f6138a2d7">neoapi.FeatureAccess</a>
</li>
<li>BitShift()
: <a class="el" href="a00763.html#aa58f5660a64c9b1ee4ef32efaf73f4da">neoapi.FeatureAccess</a>
</li>
<li>BlackLevel()
: <a class="el" href="a00763.html#a37808e5c6320702424431dbf69e004c9">neoapi.FeatureAccess</a>
</li>
<li>BlackLevelCorrectionEnable()
: <a class="el" href="a00763.html#a48ae68c95fde0bfc8f5bce2c7ea4b9fa">neoapi.FeatureAccess</a>
</li>
<li>BlackLevelCorrectionThreshold()
: <a class="el" href="a00763.html#a1893ec268a75b9124a9415aacb9d2af0">neoapi.FeatureAccess</a>
</li>
<li>BlackLevelRaw()
: <a class="el" href="a00763.html#addac8bbc14aef700bd78f7a6e0f59c7c">neoapi.FeatureAccess</a>
</li>
<li>BlackLevelSelector()
: <a class="el" href="a00763.html#a424bfef74aac7414c1472c4f229c738d">neoapi.FeatureAccess</a>
</li>
<li>BlackReferenceCorrectionEnable()
: <a class="el" href="a00763.html#a8c70d578349f9a621f5c386e0523bb7a">neoapi.FeatureAccess</a>
</li>
<li>BlackSunSuppression()
: <a class="el" href="a00763.html#ab002e73aee65ea7efd8dc49a8c7499c4">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationAngularAperture()
: <a class="el" href="a00763.html#a70270bf9616b9c9a343410ec09dde5dc">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationDataConfigurationMode()
: <a class="el" href="a00763.html#abb5c4c8325da16d93676f9794365a6cb">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationDataSave()
: <a class="el" href="a00763.html#a2a4efb56791923f35c7be5a9c452dcf3">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationDataVersion()
: <a class="el" href="a00763.html#a594e7eeb48f2986dfe999150aa2aa165">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationFocalLength()
: <a class="el" href="a00763.html#ab82edcf5b2d2152cf6f8f9a6200a9d93">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationMatrixSelector()
: <a class="el" href="a00763.html#a10d6db91cc8d6a2cfb8cbbc901fa2a3f">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationMatrixValue()
: <a class="el" href="a00763.html#abdb2368634bfb2f79c1e28fabf371022">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationMatrixValueSelector()
: <a class="el" href="a00763.html#aeea9886ec2cdfaa53254adbe5f3b8aec">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationVectorSelector()
: <a class="el" href="a00763.html#ae539b6f715d08f11862c3b37847f5543">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationVectorValue()
: <a class="el" href="a00763.html#ad660664855f1b712e4a776a378fc44c7">neoapi.FeatureAccess</a>
</li>
<li>boCalibrationVectorValueSelector()
: <a class="el" href="a00763.html#a634677bad7f56760b00b9a4fc93b10b6">neoapi.FeatureAccess</a>
</li>
<li>boGeometryDistortionValue()
: <a class="el" href="a00763.html#af5cfcfbdeaaf74569d3cdf8242d2fc93">neoapi.FeatureAccess</a>
</li>
<li>boGeometryDistortionValueSelector()
: <a class="el" href="a00763.html#a2dc9010da1f62adb1fbba586c2da0f99">neoapi.FeatureAccess</a>
</li>
<li>BOPFShift()
: <a class="el" href="a00763.html#a1bbf8996b02a8cea94ce5bb393e571d8">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerAbort()
: <a class="el" href="a00763.html#a4dca43a78b95ab69ffd1e03d83723340">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerBinningHorizontal()
: <a class="el" href="a00763.html#a93be677fa924c903185dac1df401f143">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerBinningVertical()
: <a class="el" href="a00763.html#ae09898c1000200b6307d282b16b52acc">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerEnable()
: <a class="el" href="a00763.html#a5bb9410abafd3e925bf7976bef5c55e8">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerExposure()
: <a class="el" href="a00763.html#a6aeae42eb341b8de3592cc5e0a58d923">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerFramesPerTrigger()
: <a class="el" href="a00763.html#af66541e00f75d3a6b43260430e57cb89">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerGain()
: <a class="el" href="a00763.html#af462c936729bc65c79f606cc6733e112">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerHeight()
: <a class="el" href="a00763.html#a7562885eaf0e437f4851333561da2606">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerIOSelector()
: <a class="el" href="a00763.html#a53de89c92f8e13cf024df99bf04d551f">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerIOStatus()
: <a class="el" href="a00763.html#a846b80c9346806a732df1de026e83b7a">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerIsRunning()
: <a class="el" href="a00763.html#a13e9c021b56baf6ed935b1c6dea107da">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerLoops()
: <a class="el" href="a00763.html#a1844cabcc10ec48098d85554d9c390fd">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerMode()
: <a class="el" href="a00763.html#a88a096742bea01865ee2c4bd5981cca2">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerNumberOfSets()
: <a class="el" href="a00763.html#a4493b8ad3025508df6e03b102af0e37c">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerOffsetX()
: <a class="el" href="a00763.html#a5e4cff2da6b6940018618cdb7c287e45">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerOffsetY()
: <a class="el" href="a00763.html#a5dfb525c57ceb1abd28ac1fb556d80f8">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerSensorDigitizationTaps()
: <a class="el" href="a00763.html#acf75372d48086f626f591627e8360e73">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerSetActive()
: <a class="el" href="a00763.html#a29f9e1d50762c23a06ee3775396f6b8f">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerSetNumberOfSets()
: <a class="el" href="a00763.html#a331ba02e3fc51d34efbddae1fe3a9b6e">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerSetReadOutTime()
: <a class="el" href="a00763.html#a865d7c5bf0d67c48244c197c8d152618">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerSetRepeats()
: <a class="el" href="a00763.html#a1b689f22720b5d92115bdf035651a68b">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerSetSelector()
: <a class="el" href="a00763.html#a6b2d9d9cb819a500cff94a7f424bf25f">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerStart()
: <a class="el" href="a00763.html#ad0b9d423f3eaf080c8f937f8562697a4">neoapi.FeatureAccess</a>
</li>
<li>BoSequencerWidth()
: <a class="el" href="a00763.html#a932608f0abe167b83eab4d221a9f8e94">neoapi.FeatureAccess</a>
</li>
<li>boSerialASCIIReadBuffer()
: <a class="el" href="a00763.html#a5bdc074b323ee6296935384e3ac3444e">neoapi.FeatureAccess</a>
</li>
<li>boSerialASCIIWriteBuffer()
: <a class="el" href="a00763.html#a17832e61dad869fe2e886ef270651652">neoapi.FeatureAccess</a>
</li>
<li>boSerialBinaryReadBuffer()
: <a class="el" href="a00763.html#a76ffff36ae94452ffa00b9d386a89a68">neoapi.FeatureAccess</a>
</li>
<li>boSerialBinaryWriteBuffer()
: <a class="el" href="a00763.html#a9be9a84fb88b99ae2272e3a39eacfdcf">neoapi.FeatureAccess</a>
</li>
<li>boSerialBytesAvailableForRead()
: <a class="el" href="a00763.html#a849f34ce5a9c0f9af80d317afd8b845d">neoapi.FeatureAccess</a>
</li>
<li>boSerialBytesRead()
: <a class="el" href="a00763.html#a74ebbd0e80757fd35265424104a845f2">neoapi.FeatureAccess</a>
</li>
<li>boSerialBytesToRead()
: <a class="el" href="a00763.html#af51a5753179ef17db1bcfdf16c06fc7e">neoapi.FeatureAccess</a>
</li>
<li>boSerialBytesToWrite()
: <a class="el" href="a00763.html#a168d8beb560867e605bb5c3c34a42e90">neoapi.FeatureAccess</a>
</li>
<li>boSerialBytesWritten()
: <a class="el" href="a00763.html#a5decdd2e1439e5b2bc829e11d48bd785">neoapi.FeatureAccess</a>
</li>
<li>boSerialConfigBaudRate()
: <a class="el" href="a00763.html#adb4954dbc3129842c300c20c6a236b40">neoapi.FeatureAccess</a>
</li>
<li>boSerialConfigDataBits()
: <a class="el" href="a00763.html#a70a451952e3290df6099688581385b9a">neoapi.FeatureAccess</a>
</li>
<li>boSerialConfigParity()
: <a class="el" href="a00763.html#a2cf53cd2781aa97cea3faeb72a70108c">neoapi.FeatureAccess</a>
</li>
<li>boSerialConfigStopBits()
: <a class="el" href="a00763.html#aab3f6de5bf85a4dd3fd7953fee75b255">neoapi.FeatureAccess</a>
</li>
<li>boSerialMode()
: <a class="el" href="a00763.html#a9b11a578c51cb3a27af245d51dac0329">neoapi.FeatureAccess</a>
</li>
<li>boSerialRead()
: <a class="el" href="a00763.html#aa757a61b1c1dfb8880dd5c557431ff83">neoapi.FeatureAccess</a>
</li>
<li>boSerialSelector()
: <a class="el" href="a00763.html#aef2e8104c013fdcd37ee7dd6a8003041">neoapi.FeatureAccess</a>
</li>
<li>boSerialStatus()
: <a class="el" href="a00763.html#a8bc236f15e5774f810e1de1a6a79f478">neoapi.FeatureAccess</a>
</li>
<li>boSerialWrite()
: <a class="el" href="a00763.html#a38ffa764d8e074cf803bdbb7228a090e">neoapi.FeatureAccess</a>
</li>
<li>BrightnessAutoNominalValue()
: <a class="el" href="a00763.html#abf4937b0c2076f9e3fd21ce24d449cf7">neoapi.FeatureAccess</a>
</li>
<li>BrightnessAutoPriority()
: <a class="el" href="a00763.html#a1c1adf284b5304a3858c5e93e299f2f0">neoapi.FeatureAccess</a>
</li>
<li>BrightnessCorrection()
: <a class="el" href="a00763.html#adfd14ef9708eb011be07022fe2cb2fbb">neoapi.FeatureAccess</a>
</li>
<li>BrightnessCorrectionFactor()
: <a class="el" href="a00763.html#aecf3ee6adc2ac4c0fbf16663cb63e5c5">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
