<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.ConverterSettings Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00799.html">ConverterSettings</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00796.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.ConverterSettings Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00086.html">Cam Interface Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> post processing settings This class provides methods to configure image conversions.  
 <a href="a00799.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.ConverterSettings:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00799.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aae1871c57f0fbd241612a73b6a59761a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#aae1871c57f0fbd241612a73b6a59761a">__init__</a> (self, *args)</td></tr>
<tr class="separator:aae1871c57f0fbd241612a73b6a59761a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a55273091ee46cfb3a92178007acba9"><td class="memItemLeft" align="right" valign="top">&quot;ConverterSettings&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a2a55273091ee46cfb3a92178007acba9">SetDebayerFormat</a> (self, &quot;str&quot; format)</td></tr>
<tr class="memdesc:a2a55273091ee46cfb3a92178007acba9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the target PixelFormat for a conversion A camera can stream images in different formats.  <a href="#a2a55273091ee46cfb3a92178007acba9">More...</a><br /></td></tr>
<tr class="separator:a2a55273091ee46cfb3a92178007acba9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8ecace113028dfb9de118d290c6ab773"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a8ecace113028dfb9de118d290c6ab773">GetDebayerFormat</a> (self)</td></tr>
<tr class="memdesc:a8ecace113028dfb9de118d290c6ab773"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the target PixelFormat for a conversion.  <a href="#a8ecace113028dfb9de118d290c6ab773">More...</a><br /></td></tr>
<tr class="separator:a8ecace113028dfb9de118d290c6ab773"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0357a33e15928d69aeaaba69f0051184"><td class="memItemLeft" align="right" valign="top">&quot;ConverterSettings&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a0357a33e15928d69aeaaba69f0051184">SetSharpeningMode</a> (self, &quot;int&quot; mode)</td></tr>
<tr class="memdesc:a0357a33e15928d69aeaaba69f0051184"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the target SharpeningMode for a conversion.  <a href="#a0357a33e15928d69aeaaba69f0051184">More...</a><br /></td></tr>
<tr class="separator:a0357a33e15928d69aeaaba69f0051184"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa1f29c2f30deed9d9bc001bbcf48187"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#afa1f29c2f30deed9d9bc001bbcf48187">GetSharpeningMode</a> (self)</td></tr>
<tr class="memdesc:afa1f29c2f30deed9d9bc001bbcf48187"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the target SharpeningMode for a conversion.  <a href="#afa1f29c2f30deed9d9bc001bbcf48187">More...</a><br /></td></tr>
<tr class="separator:afa1f29c2f30deed9d9bc001bbcf48187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a65ae9893b226ed4e6c44673f0eeed82b"><td class="memItemLeft" align="right" valign="top">&quot;ConverterSettings&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a65ae9893b226ed4e6c44673f0eeed82b">SetDemosaicingMethod</a> (self, &quot;int&quot; method)</td></tr>
<tr class="memdesc:a65ae9893b226ed4e6c44673f0eeed82b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the target DemosaicingMethod for a conversion.  <a href="#a65ae9893b226ed4e6c44673f0eeed82b">More...</a><br /></td></tr>
<tr class="separator:a65ae9893b226ed4e6c44673f0eeed82b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ff45bc0a71618600af8e20a0f6326cb"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a9ff45bc0a71618600af8e20a0f6326cb">GetDemosaicingMethod</a> (self)</td></tr>
<tr class="memdesc:a9ff45bc0a71618600af8e20a0f6326cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the target DemosaicingMethod for a conversion.  <a href="#a9ff45bc0a71618600af8e20a0f6326cb">More...</a><br /></td></tr>
<tr class="separator:a9ff45bc0a71618600af8e20a0f6326cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3ab541c2c53e4e4b3a5fb5e64ec222d"><td class="memItemLeft" align="right" valign="top">&quot;ConverterSettings&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#ab3ab541c2c53e4e4b3a5fb5e64ec222d">SetSharpeningFactor</a> (self, &quot;int&quot; factor)</td></tr>
<tr class="memdesc:ab3ab541c2c53e4e4b3a5fb5e64ec222d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the target SharpeningFactor for a conversion.  <a href="#ab3ab541c2c53e4e4b3a5fb5e64ec222d">More...</a><br /></td></tr>
<tr class="separator:ab3ab541c2c53e4e4b3a5fb5e64ec222d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a68d9109987c1181043cbd79a9e9685"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a9a68d9109987c1181043cbd79a9e9685">GetSharpeningFactor</a> (self)</td></tr>
<tr class="memdesc:a9a68d9109987c1181043cbd79a9e9685"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the target SharpeningFactor for a conversion.  <a href="#a9a68d9109987c1181043cbd79a9e9685">More...</a><br /></td></tr>
<tr class="separator:a9a68d9109987c1181043cbd79a9e9685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a392cb2ffe95118e605b5cc076a1802f3"><td class="memItemLeft" align="right" valign="top">&quot;ConverterSettings&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a392cb2ffe95118e605b5cc076a1802f3">SetSharpeningSensitivityThreshold</a> (self, &quot;int&quot; threshold)</td></tr>
<tr class="memdesc:a392cb2ffe95118e605b5cc076a1802f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the target SharpeningSensitivityThreshold for a conversion.  <a href="#a392cb2ffe95118e605b5cc076a1802f3">More...</a><br /></td></tr>
<tr class="separator:a392cb2ffe95118e605b5cc076a1802f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f9272dffefcf945aa1be073803e45c9"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a1f9272dffefcf945aa1be073803e45c9">GetSharpeningSensitivityThreshold</a> (self)</td></tr>
<tr class="memdesc:a1f9272dffefcf945aa1be073803e45c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the target SharpeningSensitivityThreshold for a conversion.  <a href="#a1f9272dffefcf945aa1be073803e45c9">More...</a><br /></td></tr>
<tr class="separator:a1f9272dffefcf945aa1be073803e45c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3712de0c8e33df788d575470c67fdcc2"><td class="memItemLeft" align="right" valign="top">&quot;ConverterSettings&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a3712de0c8e33df788d575470c67fdcc2">SetColorTransformationMatrix</a> (self, &quot;ColorMatrix&quot; matrix)</td></tr>
<tr class="memdesc:a3712de0c8e33df788d575470c67fdcc2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the target ColorTransformationMatrix for a conversion.  <a href="#a3712de0c8e33df788d575470c67fdcc2">More...</a><br /></td></tr>
<tr class="separator:a3712de0c8e33df788d575470c67fdcc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b1fbaabcc5bfcc439fa3b2cbe520cd5"><td class="memItemLeft" align="right" valign="top">&quot;ColorMatrix&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a9b1fbaabcc5bfcc439fa3b2cbe520cd5">GetColorTransformationMatrix</a> (self)</td></tr>
<tr class="memdesc:a9b1fbaabcc5bfcc439fa3b2cbe520cd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the target ColorTransformationMatrix for a conversion.  <a href="#a9b1fbaabcc5bfcc439fa3b2cbe520cd5">More...</a><br /></td></tr>
<tr class="separator:a9b1fbaabcc5bfcc439fa3b2cbe520cd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a6cd60e7d6666bdd45bd3820a0c7f8351"><td class="memItemLeft" align="right" valign="top"><a id="a6cd60e7d6666bdd45bd3820a0c7f8351"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Sharpening_Off</b> = _neoapi.ConverterSettings_Sharpening_Off</td></tr>
<tr class="separator:a6cd60e7d6666bdd45bd3820a0c7f8351"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a811b400078db687d4227d4ff677111fb"><td class="memItemLeft" align="right" valign="top"><a id="a811b400078db687d4227d4ff677111fb"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Sharpening_Global</b> = _neoapi.ConverterSettings_Sharpening_Global</td></tr>
<tr class="separator:a811b400078db687d4227d4ff677111fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad19741b85cd565d0d9bfcecb49300788"><td class="memItemLeft" align="right" valign="top"><a id="ad19741b85cd565d0d9bfcecb49300788"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Sharpening_Adaptive</b> = _neoapi.ConverterSettings_Sharpening_Adaptive</td></tr>
<tr class="separator:ad19741b85cd565d0d9bfcecb49300788"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb910812715e41cf8b5d1a9ee3b79fa6"><td class="memItemLeft" align="right" valign="top"><a id="acb910812715e41cf8b5d1a9ee3b79fa6"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Sharpening_ActiveNoiseReduction</b> = _neoapi.ConverterSettings_Sharpening_ActiveNoiseReduction</td></tr>
<tr class="separator:acb910812715e41cf8b5d1a9ee3b79fa6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad79a9b5748f8dec0fddfc5412c9bd5c5"><td class="memItemLeft" align="right" valign="top"><a id="ad79a9b5748f8dec0fddfc5412c9bd5c5"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Demosaicing_Bilinear3x3</b> = _neoapi.ConverterSettings_Demosaicing_Bilinear3x3</td></tr>
<tr class="separator:ad79a9b5748f8dec0fddfc5412c9bd5c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09342bd1f8a3e6609b51049dfa683f29"><td class="memItemLeft" align="right" valign="top"><a id="a09342bd1f8a3e6609b51049dfa683f29"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Demosaicing_Baumer5x5</b> = _neoapi.ConverterSettings_Demosaicing_Baumer5x5</td></tr>
<tr class="separator:a09342bd1f8a3e6609b51049dfa683f29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a340f2db4c2af14f2d97ddbdb3c08b2c0"><td class="memItemLeft" align="right" valign="top"><a id="a340f2db4c2af14f2d97ddbdb3c08b2c0"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Demosaicing_NearestNeighbor</b> = _neoapi.ConverterSettings_Demosaicing_NearestNeighbor</td></tr>
<tr class="separator:a340f2db4c2af14f2d97ddbdb3c08b2c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a23439d8bb5e3d744ef10cd429a4d5386"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00799.html#a23439d8bb5e3d744ef10cd429a4d5386">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:a23439d8bb5e3d744ef10cd429a4d5386"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#a23439d8bb5e3d744ef10cd429a4d5386">More...</a><br /></td></tr>
<tr class="separator:a23439d8bb5e3d744ef10cd429a4d5386"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> post processing settings This class provides methods to configure image conversions. </p>
<p>You can pass an instance to the <a class="el" href="a00855.html#af9fd5b5a5d619eb69e2c6c49e51c5e70">neoapi.Image.Convert</a> method to apply the settings to an image convertion. USAGE: </p><div class="fragment"><div class="line">image = <a class="code" href="a00855.html">neoapi.Image</a>()                                                     <span class="comment"># Create a Image object</span></div><div class="line">settings = <a class="code" href="a00799.html">neoapi.ConverterSettings</a>()                                      <span class="comment"># Create a ConverterSettings object</span></div><div class="line">settings.SetSharpeningMode(neoapi.ConverterSettings.Sharpening_Adaptive)   <span class="comment"># Configure sharpening</span></div><div class="line">settings.SetDebayerFormat(<span class="stringliteral">&quot;Mono8&quot;</span>)                                         <span class="comment"># Set destination PixelFormat</span></div><div class="line">image.Convert(settings)                                                    <span class="comment"># Convert the image using the provided settings</span></div></div><!-- fragment --> </div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="aae1871c57f0fbd241612a73b6a59761a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae1871c57f0fbd241612a73b6a59761a">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.ConverterSettings.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">   \brief     Constructor
</pre> <dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>object</b> (optional) A <a class="el" href="a00799.html" title="Image post processing settings This class provides methods to configure image conversions.">ConverterSettings</a> object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a2a55273091ee46cfb3a92178007acba9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a55273091ee46cfb3a92178007acba9">&#9670;&nbsp;</a></span>SetDebayerFormat()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;ConverterSettings&quot; neoapi.ConverterSettings.SetDebayerFormat </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;str&quot;&#160;</td>
          <td class="paramname"><em>format</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the target PixelFormat for a conversion A camera can stream images in different formats. </p>
<p>However this might not be the format you need to work with the image data. You can use this setting to convert an image from one format to another. (e.g. convert BayerRG8 to BGR to work with the image data using OpenCV). </p><dl class="section note"><dt>Note</dt><dd>Please see neoapi.PixelFormat for a list of possible formats </dd>
<dd>
Not all input formats can be converted to all output formats. You can use the <a class="el" href="a00855.html#ae418b72ce66230f42362b7226b731b1d" title="Get a list with available PixelFormats for Convert pixelformat must be a valid PixelFormat.">Image.GetAvailablePixelFormats()</a> or the Image.PixelFormatIsAvailable() to check if a conversion is possible </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">format</td><td>The target PixelFormat name </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00799.html" title="Image post processing settings This class provides methods to configure image conversions.">ConverterSettings</a> object </dd></dl>

</div>
</div>
<a id="a8ecace113028dfb9de118d290c6ab773"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8ecace113028dfb9de118d290c6ab773">&#9670;&nbsp;</a></span>GetDebayerFormat()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.ConverterSettings.GetDebayerFormat </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the target PixelFormat for a conversion. </p>
<dl class="section return"><dt>Returns</dt><dd>The target PixelFormat </dd></dl>

</div>
</div>
<a id="a0357a33e15928d69aeaaba69f0051184"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0357a33e15928d69aeaaba69f0051184">&#9670;&nbsp;</a></span>SetSharpeningMode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;ConverterSettings&quot; neoapi.ConverterSettings.SetSharpeningMode </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>mode</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the target SharpeningMode for a conversion. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">mode</td><td>The SharpeningMode for the converted image. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00799.html" title="Image post processing settings This class provides methods to configure image conversions.">ConverterSettings</a> object </dd></dl>

</div>
</div>
<a id="afa1f29c2f30deed9d9bc001bbcf48187"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afa1f29c2f30deed9d9bc001bbcf48187">&#9670;&nbsp;</a></span>GetSharpeningMode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ConverterSettings.GetSharpeningMode </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the target SharpeningMode for a conversion. </p>
<dl class="section return"><dt>Returns</dt><dd>The target SharpeningMode </dd></dl>

</div>
</div>
<a id="a65ae9893b226ed4e6c44673f0eeed82b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a65ae9893b226ed4e6c44673f0eeed82b">&#9670;&nbsp;</a></span>SetDemosaicingMethod()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;ConverterSettings&quot; neoapi.ConverterSettings.SetDemosaicingMethod </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>method</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the target DemosaicingMethod for a conversion. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">method</td><td>The DemosaicingMethod for the converted image. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00799.html" title="Image post processing settings This class provides methods to configure image conversions.">ConverterSettings</a> object </dd></dl>

</div>
</div>
<a id="a9ff45bc0a71618600af8e20a0f6326cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9ff45bc0a71618600af8e20a0f6326cb">&#9670;&nbsp;</a></span>GetDemosaicingMethod()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ConverterSettings.GetDemosaicingMethod </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the target DemosaicingMethod for a conversion. </p>
<dl class="section return"><dt>Returns</dt><dd>The target DemosaicingMethod </dd></dl>

</div>
</div>
<a id="ab3ab541c2c53e4e4b3a5fb5e64ec222d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab3ab541c2c53e4e4b3a5fb5e64ec222d">&#9670;&nbsp;</a></span>SetSharpeningFactor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;ConverterSettings&quot; neoapi.ConverterSettings.SetSharpeningFactor </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>factor</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the target SharpeningFactor for a conversion. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">factor</td><td>The SharpeningFactor for the converted image. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00799.html" title="Image post processing settings This class provides methods to configure image conversions.">ConverterSettings</a> object </dd></dl>

</div>
</div>
<a id="a9a68d9109987c1181043cbd79a9e9685"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9a68d9109987c1181043cbd79a9e9685">&#9670;&nbsp;</a></span>GetSharpeningFactor()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ConverterSettings.GetSharpeningFactor </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the target SharpeningFactor for a conversion. </p>
<dl class="section return"><dt>Returns</dt><dd>The target SharpeningFactor </dd></dl>

</div>
</div>
<a id="a392cb2ffe95118e605b5cc076a1802f3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a392cb2ffe95118e605b5cc076a1802f3">&#9670;&nbsp;</a></span>SetSharpeningSensitivityThreshold()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;ConverterSettings&quot; neoapi.ConverterSettings.SetSharpeningSensitivityThreshold </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>threshold</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the target SharpeningSensitivityThreshold for a conversion. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">threshold</td><td>The SharpeningSensitivityThreshold for the converted image. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00799.html" title="Image post processing settings This class provides methods to configure image conversions.">ConverterSettings</a> object </dd></dl>

</div>
</div>
<a id="a1f9272dffefcf945aa1be073803e45c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1f9272dffefcf945aa1be073803e45c9">&#9670;&nbsp;</a></span>GetSharpeningSensitivityThreshold()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ConverterSettings.GetSharpeningSensitivityThreshold </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the target SharpeningSensitivityThreshold for a conversion. </p>
<dl class="section return"><dt>Returns</dt><dd>The target SharpeningSensitivityThreshold </dd></dl>

</div>
</div>
<a id="a3712de0c8e33df788d575470c67fdcc2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3712de0c8e33df788d575470c67fdcc2">&#9670;&nbsp;</a></span>SetColorTransformationMatrix()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;ConverterSettings&quot; neoapi.ConverterSettings.SetColorTransformationMatrix </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;ColorMatrix&quot;&#160;</td>
          <td class="paramname"><em>matrix</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the target ColorTransformationMatrix for a conversion. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">matrix</td><td>The <a class="el" href="a00795.html" title="Class for the Color Transformation Matrix This class provides methods to configure the Color Matrix f...">ColorMatrix</a> for the converted image. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00799.html" title="Image post processing settings This class provides methods to configure image conversions.">ConverterSettings</a> object </dd></dl>

</div>
</div>
<a id="a9b1fbaabcc5bfcc439fa3b2cbe520cd5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b1fbaabcc5bfcc439fa3b2cbe520cd5">&#9670;&nbsp;</a></span>GetColorTransformationMatrix()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;ColorMatrix&quot; neoapi.ConverterSettings.GetColorTransformationMatrix </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the target ColorTransformationMatrix for a conversion. </p>
<dl class="section return"><dt>Returns</dt><dd>The target ColorTransformationMatrix </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a23439d8bb5e3d744ef10cd429a4d5386"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a23439d8bb5e3d744ef10cd429a4d5386">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.ConverterSettings.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
