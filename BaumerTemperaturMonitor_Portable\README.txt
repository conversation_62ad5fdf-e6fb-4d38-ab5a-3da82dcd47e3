# Baumer Temperatur Monitor - Portable Version

## 🚀 SOFORT EINSATZBEREIT!
Keine Installation erforderlich! Einfach BaumerTemperaturMonitor.exe starten.

## 📋 BEKANNTE BAUMER-KAMERAS:
Das Programm ist vorkonfiguriert für diese Kamera-IPs:
- *************** (VCXG-13M)
- ************** (VCXG-13M)
- ************** (VCXG-13M)
- ************** (VCXG-13M)

## 🎯 VERWENDUNG:
1. BaumerTemperaturMonitor.exe doppelklicken
2. "Alle bekannten Kameras testen" klicken
3. Erreichbare Kameras werden angezeigt
4. Kameras auswählen und "Verbinden" klicken
5. Temperatur-Überwachung startet automatisch

## 🔧 SCAN-OPTIONEN:
- **Alle bekannten Kameras testen** ⭐ (Empfohlen)
- **Gez<PERSON><PERSON>** (bekannte Bereiche)
- **<PERSON><PERSON><PERSON>an** (1-50)
- **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>** (1-254)

## 🌐 NETZWERK-KONFIGURATION:
- Ethernet 5 Verbindung zu Baumer-Kameras
- 169.254.x.x Netzwerk (APIPA/Link-Local)
- Keine Baumer-Treiber erforderlich
- Funktioniert auch ohne Baumer-Software

## 💻 SYSTEMANFORDERUNGEN:
- Windows 7/8/10/11 (64-bit)
- Ethernet-Verbindung zu den Kameras
- Keine Python-Installation erforderlich
- Keine zusätzliche Software erforderlich

## 📁 ORDNER-STRUKTUR:
- BaumerTemperaturMonitor.exe - Hauptprogramm
- Start.bat - Schnellstart-Datei
- logs/ - System-Logs und Fehlermeldungen
- data/ - Temperatur-Datenbank (SQLite)
- README.txt - Diese Anleitung

## 🔍 FEHLERBEHEBUNG:
1. **Kameras nicht gefunden:**
   - Prüfen Sie die Ethernet 5 Verbindung
   - Stellen Sie sicher, dass die Kameras eingeschaltet sind
   - Verwenden Sie "Gezielter Scan" für bekannte Bereiche

2. **Programm startet nicht:**
   - Rechtsklick auf .exe → "Als Administrator ausführen"
   - Windows Defender/Antivirus temporär deaktivieren
   - Prüfen Sie die logs/ für Fehlermeldungen

3. **Temperatur-Daten fehlen:**
   - Prüfen Sie die Kamera-Verbindung
   - Verwenden Sie "Temperatur testen" Button
   - Logs prüfen für API-Fehler

## 📊 FEATURES:
✅ Automatische Kamera-Erkennung
✅ Echtzeit-Temperatur-Überwachung  
✅ Historische Daten-Speicherung
✅ Grafische Benutzeroberfläche
✅ Portable - keine Installation
✅ Keine Treiber erforderlich

## 📞 SUPPORT:
Bei Problemen prüfen Sie:
1. logs/ethernet5_log_YYYYMMDD_HHMMSS.txt
2. System-Log im Programm (Log-Tab)
3. Windows Ereignisanzeige

## 📅 VERSION INFORMATION:
- Version: 1.0.5
- Build-Datum: 2025-08-21 15:18:56
- Plattform: Windows 64-bit
- Python: Eingebettet (keine Installation erforderlich)
- GUI: PyQt5 (eingebettet)

## 🎉 VIEL ERFOLG!
Das Programm ist bereit für die Überwachung Ihrer Baumer-Kameras!
