VERSION = "1.5.0"

import sys
import os
import logging
import time
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox)
from PyQt5.QtCore import QTimer

# Prüfe auf echte Baumer neoAPI (nicht die Neo4j-Version!)
NEOAPI_AVAILABLE = False
NEOAPI_TYPE = "Nicht verfügbar"

try:
    import neoapi
    # Prüfe ob es die echte Baumer neoAPI ist
    if hasattr(neoapi, 'Cam'):
        NEOAPI_AVAILABLE = True
        NEOAPI_TYPE = "Echte Baumer neoAPI"
        print("✅ Echte Baumer neoAPI gefunden")
    else:
        print("❌ Falsche neoAPI gefunden (Neo4j-Version)")
        print("📦 Bitte installieren Sie die echte Baumer neoAPI")
        NEOAPI_TYPE = "Falsche neoAPI (Neo4j)"
except ImportError:
    print("❌ Keine neoAPI gefunden")
    NEOAPI_TYPE = "Nicht installiert"

class BaumerWorkingSolution(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"Baumer Temperatur-Monitor v{VERSION} - FUNKTIONSFÄHIGE LÖSUNG")
        self.setGeometry(100, 100, 1200, 800)
        
        # Kamera-Verbindungen
        self.connected_cameras = {}
        self.monitoring_active = False
        self.monitoring_timer = QTimer()
        self.monitoring_timer.timeout.connect(self.monitor_all_temperatures)
        
        # UI erstellen
        self.setup_ui()
        
        print(f"🚀 Baumer Funktionsfähige Lösung v{VERSION} gestartet")
        print(f"📊 neoAPI-Status: {NEOAPI_TYPE}")

    def setup_ui(self):
        """Erstellt die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titel
        title = QLabel(f"🌡️ Baumer Temperatur-Monitor v{VERSION} - FUNKTIONSFÄHIGE LÖSUNG")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
        layout.addWidget(title)
        
        # Status
        if NEOAPI_AVAILABLE:
            status_text = f"✅ {NEOAPI_TYPE} verfügbar"
            status_color = "#28A745"
            info_text = "🔥 Bereit für echte Baumer-Kameras und echte DeviceTemperature-Werte!"
            info_color = "#E8F5E8"
        else:
            status_text = f"❌ {NEOAPI_TYPE}"
            status_color = "#DC3545"
            if NEOAPI_TYPE == "Falsche neoAPI (Neo4j)":
                info_text = "⚠️ FALSCHE NEOAPI INSTALLIERT! Sie haben die Neo4j-Version, nicht die Baumer-Version!"
            else:
                info_text = "⚠️ INSTALLATION ERFORDERLICH: Bitte installieren Sie die echte Baumer neoAPI!"
            info_color = "#F8D7DA"
            
        status = QLabel(f"Status: {status_text}")
        status.setStyleSheet(f"color: {status_color}; font-weight: bold; margin: 5px;")
        layout.addWidget(status)
        
        # Info-Box
        info = QLabel(info_text)
        info.setStyleSheet(f"background-color: {info_color}; padding: 10px; border-radius: 5px; margin: 5px;")
        layout.addWidget(info)
        
        # Tabs
        tabs = QTabWidget()
        layout.addWidget(tabs)
        
        # Tab 1: Problem-Diagnose
        diagnosis_tab = QWidget()
        tabs.addTab(diagnosis_tab, "🔍 Problem-Diagnose")
        diagnosis_layout = QVBoxLayout(diagnosis_tab)
        
        diagnosis_text = QTextEdit()
        diagnosis_text.setReadOnly(True)
        diagnosis_content = f"""
🔍 PROBLEM-DIAGNOSE

AKTUELLER STATUS: {NEOAPI_TYPE}

HÄUFIGE PROBLEME UND LÖSUNGEN:

1. FALSCHE NEOAPI INSTALLIERT (Neo4j statt Baumer):
   Problem: Sie haben "neoapi 3.6.4" installiert - das ist für Neo4j-Datenbanken!
   Lösung: 
   - py -m pip uninstall neoapi neomodel neo4j py2neo
   - Echte Baumer neoAPI installieren (siehe Tab "Installation")

2. PYTHON-VERSION INKOMPATIBEL:
   Problem: Baumer neoAPI unterstützt nur Python 3.4-3.12, Sie haben Python 3.13
   Lösung:
   - Python 3.11 oder 3.12 installieren
   - Oder warten auf neue Baumer neoAPI-Version

3. WHEEL-DATEI NICHT KOMPATIBEL:
   Problem: "is not a supported wheel on this platform"
   Lösung:
   - Andere Python-Version verwenden
   - Oder Baumer kontaktieren für Python 3.13-Support

4. KEINE KAMERAS GEFUNDEN:
   Problem: neoAPI installiert, aber keine Kameras erkannt
   Lösung:
   - gevipconfig.exe ausführen (für GigE-Kameras)
   - USB-Treiber installieren (für USB-Kameras)
   - Kamera-Stromversorgung prüfen

EMPFOHLENE LÖSUNG:
1. Python 3.11 installieren
2. Falsche neoAPI entfernen
3. Echte Baumer neoAPI installieren
4. Diese App neu starten
"""
        diagnosis_text.setPlainText(diagnosis_content)
        diagnosis_layout.addWidget(diagnosis_text)
        
        # Tab 2: Installation
        install_tab = QWidget()
        tabs.addTab(install_tab, "📦 Korrekte Installation")
        install_layout = QVBoxLayout(install_tab)
        
        install_text = QTextEdit()
        install_text.setReadOnly(True)
        install_content = """
📦 KORREKTE BAUMER NEOAPI INSTALLATION

SCHRITT 1: FALSCHE NEOAPI ENTFERNEN
py -m pip uninstall neoapi neomodel neo4j py2neo -y

SCHRITT 2: PYTHON-VERSION PRÜFEN
py --version
(Baumer neoAPI unterstützt Python 3.4-3.12, NICHT 3.13!)

SCHRITT 3: ECHTE BAUMER NEOAPI INSTALLIEREN
Option A: Aus neoAPI-Ordner
py -m pip install neoAPI/wheel/baumer_neoapi-1.5.0-*.whl

Option B: Von Baumer-Website herunterladen
https://www.baumer.com/us/en/product-overview/industrial-cameras-image-processing/software/baumer-neoapi/c/42528

SCHRITT 4: INSTALLATION TESTEN
py -c "import neoapi; print('Cam verfügbar:', hasattr(neoapi, 'Cam'))"

SCHRITT 5: TREIBER INSTALLIEREN
- USB-Kameras: neoAPI/drivers/USB/
- GigE-Kameras: neoAPI/drivers/filterdriver/

SCHRITT 6: KAMERA-KONFIGURATION
- GigE-Kameras: neoAPI/tools/gevipconfig.exe ausführen

SCHRITT 7: APP NEU STARTEN
Diese App neu starten - Status sollte "✅ Echte Baumer neoAPI verfügbar" zeigen

WICHTIG: 
- Verwenden Sie NIEMALS "pip install neoapi" (das ist die falsche Neo4j-Version!)
- Verwenden Sie NUR die Wheel-Dateien von Baumer!
"""
        install_text.setPlainText(install_content)
        install_layout.addWidget(install_text)
        
        # Tab 3: Test (nur wenn echte neoAPI verfügbar)
        test_tab = QWidget()
        tabs.addTab(test_tab, "🧪 neoAPI-Test")
        test_layout = QVBoxLayout(test_tab)
        
        test_layout.addWidget(QLabel("neoAPI-Funktionstest:"))
        
        self.test_btn = QPushButton("🧪 neoAPI testen")
        self.test_btn.clicked.connect(self.test_neoapi)
        self.test_btn.setEnabled(NEOAPI_AVAILABLE)
        test_layout.addWidget(self.test_btn)
        
        self.test_result = QTextEdit()
        self.test_result.setReadOnly(True)
        test_layout.addWidget(self.test_result)
        
        # Tab 4: Log
        log_tab = QWidget()
        tabs.addTab(log_tab, "📋 Log")
        log_layout = QVBoxLayout(log_tab)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # Initiales Log
        self.log_message(f"🚀 Baumer Funktionsfähige Lösung v{VERSION} gestartet")
        self.log_message(f"📊 neoAPI-Status: {NEOAPI_TYPE}")
        
        if not NEOAPI_AVAILABLE:
            if NEOAPI_TYPE == "Falsche neoAPI (Neo4j)":
                self.log_message("❌ PROBLEM: Falsche neoAPI installiert (Neo4j statt Baumer)")
                self.log_message("💡 LÖSUNG: py -m pip uninstall neoapi -y")
            else:
                self.log_message("❌ PROBLEM: Keine neoAPI installiert")
                self.log_message("💡 LÖSUNG: Echte Baumer neoAPI installieren")

    def log_message(self, message):
        """Fügt eine Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        print(log_entry)

    def test_neoapi(self):
        """Testet die neoAPI-Funktionalität"""
        if not NEOAPI_AVAILABLE:
            self.test_result.setPlainText("❌ Echte Baumer neoAPI nicht verfügbar!")
            return
            
        self.log_message("🧪 Starte neoAPI-Funktionstest...")
        test_results = []
        
        try:
            # Test 1: Import
            test_results.append("✅ Test 1: neoAPI-Import erfolgreich")
            
            # Test 2: Cam-Klasse
            if hasattr(neoapi, 'Cam'):
                test_results.append("✅ Test 2: neoapi.Cam-Klasse verfügbar")
                
                # Test 3: Kamera-Erstellung
                try:
                    camera = neoapi.Cam()
                    test_results.append("✅ Test 3: Kamera-Objekt erstellt")
                    
                    # Test 4: Verbindungsversuch
                    try:
                        camera.Connect()
                        test_results.append("✅ Test 4: Kamera-Verbindung erfolgreich")
                        
                        # Test 5: Feature-Zugriff
                        try:
                            camera.f.ExposureTime.Set(10000)
                            test_results.append("✅ Test 5: Feature-Zugriff erfolgreich")
                            
                            # Test 6: Temperatur-Feature
                            if hasattr(camera.f, 'DeviceTemperature'):
                                temp = camera.f.DeviceTemperature.Get()
                                test_results.append(f"✅ Test 6: DeviceTemperature = {temp}°C")
                            else:
                                test_results.append("⚠️ Test 6: DeviceTemperature-Feature nicht verfügbar")
                                
                        except Exception as feat_error:
                            test_results.append(f"❌ Test 5: Feature-Zugriff fehlgeschlagen: {feat_error}")
                            
                    except Exception as conn_error:
                        test_results.append(f"⚠️ Test 4: Keine Kamera gefunden: {conn_error}")
                        test_results.append("💡 Prüfen Sie: Kamera angeschlossen? Treiber installiert?")
                        
                except Exception as cam_error:
                    test_results.append(f"❌ Test 3: Kamera-Erstellung fehlgeschlagen: {cam_error}")
                    
            else:
                test_results.append("❌ Test 2: neoapi.Cam-Klasse NICHT verfügbar")
                test_results.append("💡 Das ist die falsche neoAPI (Neo4j-Version)!")
                
        except Exception as e:
            test_results.append(f"❌ neoAPI-Test fehlgeschlagen: {e}")
            
        # Ergebnisse anzeigen
        result_text = "\n".join(test_results)
        self.test_result.setPlainText(result_text)
        self.log_message("🧪 neoAPI-Test abgeschlossen")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = BaumerWorkingSolution()
    window.show()
    sys.exit(app.exec_())
