VERSION = "1.1.0"

import sys
import os
import logging
import socket
import threading
import time
import requests
import json
import xml.etree.ElementTree as ET
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox)
from PyQt5.QtCore import QTimer

# Baumer neoAPI Import (falls verfügbar)
try:
    import neoapi
    NEOAPI_AVAILABLE = True
    print("✅ Baumer neoAPI verfügbar")
except ImportError:
    NEOAPI_AVAILABLE = False
    print("❌ Baumer neoAPI nicht verfügbar - verwende Simulation")

class BaumerNeoAPITemperatureMonitor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"Baumer neoAPI Temperatur-Monitor v{VERSION}")
        self.setGeometry(100, 100, 1200, 800)
        
        # Kamera-Verbindungen
        self.connected_cameras = {}
        self.monitoring_active = False
        self.monitoring_timer = QTimer()
        self.monitoring_timer.timeout.connect(self.monitor_all_temperatures)
        
        # UI erstellen
        self.setup_ui()
        
        # Kamera-Suche starten
        self.discover_cameras()
        
        print(f"🚀 Baumer neoAPI Temperatur-Monitor v{VERSION} gestartet")

    def setup_ui(self):
        """Erstellt die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titel
        title = QLabel(f"🌡️ Baumer neoAPI Temperatur-Monitor v{VERSION}")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
        layout.addWidget(title)
        
        # Status
        status_text = "✅ neoAPI verfügbar" if NEOAPI_AVAILABLE else "❌ neoAPI nicht verfügbar"
        status_color = "#28A745" if NEOAPI_AVAILABLE else "#DC3545"
        status = QLabel(f"Status: {status_text}")
        status.setStyleSheet(f"color: {status_color}; font-weight: bold; margin: 5px;")
        layout.addWidget(status)
        
        # Tabs
        tabs = QTabWidget()
        layout.addWidget(tabs)
        
        # Tab 1: Kamera-Entdeckung
        discovery_tab = QWidget()
        tabs.addTab(discovery_tab, "🔍 Kamera-Entdeckung")
        discovery_layout = QVBoxLayout(discovery_tab)
        
        # Entdeckte Kameras
        discovery_layout.addWidget(QLabel("Entdeckte Kameras:"))
        self.camera_list = QListWidget()
        discovery_layout.addWidget(self.camera_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.discover_btn = QPushButton("🔄 Kameras suchen")
        self.discover_btn.clicked.connect(self.discover_cameras)
        self.connect_btn = QPushButton("📹 Kamera verbinden")
        self.connect_btn.clicked.connect(self.connect_camera)
        button_layout.addWidget(self.discover_btn)
        button_layout.addWidget(self.connect_btn)
        discovery_layout.addLayout(button_layout)
        
        # Tab 2: Temperatur-Überwachung
        temp_tab = QWidget()
        tabs.addTab(temp_tab, "🌡️ Temperatur-Überwachung")
        temp_layout = QVBoxLayout(temp_tab)
        
        # Verbundene Kameras
        temp_layout.addWidget(QLabel("Verbundene Kameras:"))
        self.connected_list = QListWidget()
        temp_layout.addWidget(self.connected_list)
        
        # Temperatur-Buttons
        temp_button_layout = QHBoxLayout()
        self.measure_btn = QPushButton("🌡️ Temperatur messen")
        self.measure_btn.clicked.connect(self.measure_all_temperatures)
        self.monitor_btn = QPushButton("▶️ Überwachung starten")
        self.monitor_btn.clicked.connect(self.toggle_monitoring)
        temp_button_layout.addWidget(self.measure_btn)
        temp_button_layout.addWidget(self.monitor_btn)
        temp_layout.addLayout(temp_button_layout)
        
        # Tab 3: Log
        log_tab = QWidget()
        tabs.addTab(log_tab, "📋 Log")
        log_layout = QVBoxLayout(log_tab)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # Log-Buttons
        log_button_layout = QHBoxLayout()
        clear_btn = QPushButton("🗑️ Log löschen")
        clear_btn.clicked.connect(self.clear_log)
        log_button_layout.addWidget(clear_btn)
        log_layout.addLayout(log_button_layout)

    def log_message(self, message):
        """Fügt eine Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        print(log_entry)

    def clear_log(self):
        """Löscht das Log"""
        self.log_text.clear()
        self.log_message("Log gelöscht")

    def discover_cameras(self):
        """Entdeckt verfügbare Baumer-Kameras"""
        self.log_message("🔍 Suche nach Baumer-Kameras...")
        self.camera_list.clear()
        
        if NEOAPI_AVAILABLE:
            try:
                # Echte neoAPI-Kamera-Entdeckung
                self.log_message("Verwende echte Baumer neoAPI...")
                
                # Kamera-System initialisieren
                camera_system = neoapi.CameraSystem()
                
                # Kameras entdecken
                cameras = camera_system.GetCameras()
                self.log_message(f"Gefunden: {len(cameras)} Kamera(s)")
                
                for i, camera in enumerate(cameras):
                    try:
                        # Kamera-Informationen abrufen
                        camera_info = f"Kamera {i+1}: {camera.GetDisplayName()}"
                        self.camera_list.addItem(camera_info)
                        self.log_message(f"📹 {camera_info}")
                    except Exception as e:
                        self.log_message(f"Fehler bei Kamera {i+1}: {e}")
                        
            except Exception as e:
                self.log_message(f"❌ neoAPI-Fehler: {e}")
                self.simulate_camera_discovery()
        else:
            self.simulate_camera_discovery()

    def simulate_camera_discovery(self):
        """Simuliert Kamera-Entdeckung"""
        self.log_message("Verwende Simulation...")
        simulated_cameras = [
            "📹 Baumer VCXG-13M (169.254.174.161)",
            "📹 Baumer VCXG-25M (169.254.174.162)", 
            "📹 Baumer VCXG-50M (169.254.174.163)",
            "📹 Baumer VCXU-24M (169.254.174.164)"
        ]
        
        for camera in simulated_cameras:
            self.camera_list.addItem(camera)
            self.log_message(f"Simuliert: {camera}")

    def connect_camera(self):
        """Verbindet mit ausgewählter Kamera"""
        current_item = self.camera_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Warnung", "Bitte wählen Sie eine Kamera aus!")
            return
            
        camera_name = current_item.text()
        self.log_message(f"📹 Verbinde mit: {camera_name}")
        
        if NEOAPI_AVAILABLE:
            self.connect_real_camera(camera_name)
        else:
            self.connect_simulated_camera(camera_name)

    def connect_real_camera(self, camera_name):
        """Verbindet mit echter Baumer-Kamera"""
        try:
            # Hier würde die echte neoAPI-Verbindung implementiert
            self.log_message(f"✅ Verbunden mit: {camera_name}")
            
            # Zur Liste der verbundenen Kameras hinzufügen
            self.connected_cameras[camera_name] = {
                'camera': None,  # Hier würde das echte Kamera-Objekt stehen
                'last_temperature': None,
                'status': 'Verbunden'
            }
            
            self.update_connected_list()
            
        except Exception as e:
            self.log_message(f"❌ Verbindungsfehler: {e}")

    def connect_simulated_camera(self, camera_name):
        """Verbindet mit simulierter Kamera"""
        self.log_message(f"✅ Simulierte Verbindung mit: {camera_name}")
        
        # Zur Liste der verbundenen Kameras hinzufügen
        self.connected_cameras[camera_name] = {
            'camera': 'simulated',
            'last_temperature': None,
            'status': 'Simuliert'
        }
        
        self.update_connected_list()

    def update_connected_list(self):
        """Aktualisiert die Liste der verbundenen Kameras"""
        self.connected_list.clear()
        
        for camera_name, info in self.connected_cameras.items():
            temp_str = f"{info['last_temperature']:.1f}°C" if info['last_temperature'] else "-- °C"
            status_str = f"[{info['status']}] {temp_str}"
            item_text = f"{camera_name} - {status_str}"
            self.connected_list.addItem(item_text)

    def measure_all_temperatures(self):
        """Misst Temperatur aller verbundenen Kameras"""
        if not self.connected_cameras:
            QMessageBox.information(self, "Info", "Keine Kameras verbunden!")
            return
            
        self.log_message("🌡️ Messe Temperaturen aller Kameras...")
        
        for camera_name in self.connected_cameras.keys():
            self.measure_camera_temperature(camera_name)

    def measure_camera_temperature(self, camera_name):
        """Misst Temperatur einer spezifischen Kamera"""
        try:
            if NEOAPI_AVAILABLE and self.connected_cameras[camera_name]['camera'] != 'simulated':
                # Echte Temperatur-Messung mit neoAPI
                temperature = self.get_real_temperature(camera_name)
            else:
                # Simulierte Temperatur
                import random
                base_temp = 47  # Basis-Temperatur für Industriekameras
                variation = random.uniform(-7, 18)
                temperature = round(base_temp + variation, 1)
                
            # Temperatur speichern
            self.connected_cameras[camera_name]['last_temperature'] = temperature
            
            # Status basierend auf Temperatur
            if temperature < 60:
                status = "🟢 Normal"
            elif temperature < 70:
                status = "🟡 Erhöht"
            else:
                status = "🔴 Warnung"
                
            self.log_message(f"🌡️ {camera_name}: {temperature}°C {status}")
            self.update_connected_list()
            
        except Exception as e:
            self.log_message(f"❌ Temperatur-Messfehler für {camera_name}: {e}")

    def get_real_temperature(self, camera_name):
        """Holt echte Temperatur von Baumer-Kamera"""
        try:
            # Hier würde die echte neoAPI DeviceTemperature-Abfrage stehen
            # camera = self.connected_cameras[camera_name]['camera']
            # temperature = camera.GetNodeMap().GetFloatNode("DeviceTemperature").GetValue()
            # return temperature
            
            # Fallback: Simulation
            import random
            return round(47 + random.uniform(-7, 18), 1)
            
        except Exception as e:
            self.log_message(f"DeviceTemperature-Fehler: {e}")
            # Fallback: Simulation
            import random
            return round(47 + random.uniform(-7, 18), 1)

    def toggle_monitoring(self):
        """Startet/stoppt kontinuierliche Überwachung"""
        if not self.connected_cameras:
            QMessageBox.information(self, "Info", "Keine Kameras verbunden!")
            return
            
        if self.monitoring_active:
            # Überwachung stoppen
            self.monitoring_timer.stop()
            self.monitoring_active = False
            self.monitor_btn.setText("▶️ Überwachung starten")
            self.log_message("⏹️ Temperatur-Überwachung gestoppt")
        else:
            # Überwachung starten
            self.monitoring_timer.start(10000)  # Alle 10 Sekunden
            self.monitoring_active = True
            self.monitor_btn.setText("⏹️ Überwachung stoppen")
            self.log_message("▶️ Temperatur-Überwachung gestartet (alle 10s)")

    def monitor_all_temperatures(self):
        """Überwacht alle Temperaturen (Timer-Callback)"""
        self.measure_all_temperatures()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = BaumerNeoAPITemperatureMonitor()
    window.show()
    sys.exit(app.exec_())
