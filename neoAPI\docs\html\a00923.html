<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Images and Buffers with neoAPI</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Images and Buffers with neoAPI </div>  </div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#autotoc_md78">Images and buffers</a><ul><li class="level2"><a href="#autotoc_md79">How are images transferred to the application?</a></li>
<li class="level2"><a href="#autotoc_md80">Buffer modes</a><ul><li class="level3"><a href="#autotoc_md81">Streaming buffer mode</a></li>
<li class="level3"><a href="#autotoc_md82">Cyclic buffer mode</a></li>
<li class="level3"><a href="#autotoc_md83">Queued buffer mode</a></li>
<li class="level3"><a href="#autotoc_md84">User buffer mode</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md85">Meta- and Chunk data</a></li>
<li class="level2"><a href="#autotoc_md86">GenDC transfer mode</a></li>
<li class="level2"><a href="#autotoc_md87">GenDC user Buffers</a></li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><h1><a class="anchor" id="autotoc_md78"></a>
Images and buffers</h1>
<p>Now that you have a camera connected and know how to change camera settings via the features, it is time to get some images. To get to your first image using neoAPI is straight forward.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    camera.f.ExposureTime = 20000  # set an exposure time</div><div class="line">    img = camera.GetImage()        # retrieve an image from the camera</div><div class="line">    print(img.GetWidth())</div><div class="line">    img.Save(&quot;MyImage&quot;)            # save the image to the current folder</div></div><!-- fragment --> <div class="caption">Example: Get and save an image</div><p><br />
</p>
<h2><a class="anchor" id="autotoc_md79"></a>
How are images transferred to the application?</h2>
<p>Firstly, there are two different modes a GenICam camera can be operated in. The camera can either be in “free-run” mode, where the camera will constantly supply images. Secondly, there is the trigger mode where you need to tell the camera to take an image either via software command feature “TriggerSoftware” or via a hardware trigger impulse where the source is set via the feature “TriggerSource”.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    camera.f.TriggerMode.value = neoapi.TriggerMode_On  # set camera to trigger mode</div><div class="line">    camera.f.TriggerSoftware.Execute()                  # execute a software trigger to get an image</div><div class="line">    img = camera.GetImage()                             # retrieve the image to work with it</div><div class="line"></div><div class="line">    camera.f.TriggerMode.value = neoapi.TriggerMode_Off # set camera to trigger mode, the camera starts streaming</div><div class="line">    camera.f.AcquisitionFrameRateEnable = True          # enable the frame rate control (optional)</div><div class="line">    camera.f.AcquisitionFrameRate = 24                  # set the frame rate to 24 fps (optional)</div><div class="line">    img = camera.GetImage()                             # retrieve the image to work with it, no trigger necessary</div></div><!-- fragment --> <div class="caption">Example: Change the camera behavior from triggered to free-run</div><p><br />
</p>
<p>The transfer of the images from the camera is the most critical process. It needs to be reliable so no image gets lost. It also needs to be really quick, as a single camera can already deliver up to 10 GBits of data per second to the host. Furthermore, the mechanics of the image transfer needs some flexibility as the host operating system is not a real-time OS, which means, we don't know exactly when the application will be scheduled next for work. And finally, to be able to process the data at high speeds, it is necessary to enable parallel execution of image processing.</p>
<p>To deal with those issues, a buffer queue is implemented. In the beginning, all those buffers are empty. Calling <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code> on an empty buffer queue will result in an empty image. For the camera to deliver an image to the host, an empty buffer must be lined up to receive an image. If no empty buffer is available, the camera will produce so called under-runs and will drop the image. Once the camera can deliver an image to a buffer, it will be placed in the queue of filled buffers. On calling <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code>, one of the filled buffers is given to the user.</p>
<p>At this point, the buffer (which is connected to the Image object) is delivered directly to you, to be used in your application without further copying (copying of large data blocks is slow!). The buffer is owned by you and is not available for the neoAPI to store a new image. Therefore, you need to ensure that you always have return buffers by destroying the image after usage.</p>
<p>By default, the neoAPI will use 10 buffers to store images. More buffers might be necessary, especially if working with high frame-rates. The method <code><a class="el" href="a00863.html#a2f19563bca5378be0b09e23dc1fd70d1" title="Set the number of internal used image buffers The number of internal image buffers is equal to the ma...">neoapi.Cam.SetImageBufferCount()</a></code> can be used to change the amount of buffers available. The example below will fail with an <code><a class="el" href="a00783.html" title="Requesting an image while holding all available image resources.">neoapi.NoImageBufferException</a></code> after 8 images as the example code never returns any buffer back to the neoAPI.</p>
<div class="fragment"><div class="line">import sys</div><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">camera.SetImageBufferCount(8)                        # set amount of buffers to 8</div><div class="line">img = []                                             # create array to store images</div><div class="line">for i in range(9):</div><div class="line">    if camera.IsConnected():</div><div class="line">        try:</div><div class="line">            img.append(camera.GetImage())            # exception expected after all buffers are used</div><div class="line">            img[i].Save(&quot;MyImage&quot;)</div><div class="line">        except (neoapi.NoImageBufferException) as exc:</div><div class="line">            print(sys.exc_info()[0])</div><div class="line">            print(&quot;NoImageBufferException: &quot;, exc)</div></div><!-- fragment --> <div class="caption">Example: Bad! You must return the buffer to the neoAPI</div><p><br />
</p>
<h2><a class="anchor" id="autotoc_md80"></a>
Buffer modes</h2>
<p>Depending on your use-case you can configure the neoAPI how the buffer-handling works. The configuration of the buffer handling can be changed using the following methods:</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">camera.SetImageBufferCount()       # set the size of the buffer queue</div><div class="line">camera.SetImageBufferCycleCount()  # sets how many of the buffers are used to cycle</div><div class="line">camera.SetUserBufferMode()         # used to provide external buffers to be used by neoAPI</div></div><!-- fragment --> <div class="caption">Example: The buffer configuration methods</div><p><br />
</p>
<h3><a class="anchor" id="autotoc_md81"></a>
Streaming buffer mode</h3>
<p>The default buffer mode for the neoAPI is the “streaming mode”. In streaming mode, the <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code> method will always return the latest (newest) image taken by the camera. So if the camera is taking images with 20 fps (free-running or triggered) and you only call <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code> once a second, you will get the most recent image, all other images are discarded.</p>
<p>A typical use-case for the streaming mode is an application where images should be displayed for observation purposes.</p>
<p>In theory, one buffer would be enough to handle this use-case. However, using a queue of buffers will allow to store the next image while you own the image buffer(s) to work with.</p>
<p>Setting the <code><a class="el" href="a00863.html#af2f0ac4b47d3afdb2f90c42891b8909e" title="Set the number of internal image buffers to be cycled automatically By changing the SetImageBufferCou...">neoapi.Cam.SetImageBufferCycleCount()</a></code>to one ensures that all buffers but one are given back to the neoAPI to be re-cycled and never given to the user by the <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code> method.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">camera.SetImageBufferCount(10)      # set the size of the buffer queue to 10</div><div class="line">camera.SetImageBufferCycleCount(1)  # sets the cycle count to 1</div></div><!-- fragment --> <div class="caption">Example: The buffer configuration for the streaming buffer mode</div><p><br />
</p>
<h3><a class="anchor" id="autotoc_md82"></a>
Cyclic buffer mode</h3>
<p>The cyclic buffer mode is very similar to the streaming buffer mode, however it differs that you can get a set amount of recent images instead of only the most recent. It is important to set the ImageBufferCycleCount to a value smaller than the ImageBufferCount. This ensures there are always buffers available to be cycled while you have buffers to be used in your application. The amount of buffers in the queue and the cycle depends on your use-case and application.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">camera.SetImageBufferCount(1000)      # set the size of the buffer queue to 1000</div><div class="line">camera.SetImageBufferCycleCount(950)  # sets how many of the buffers are used to cycle</div></div><!-- fragment --> <div class="caption">Example: The buffer configuration for the cyclic buffer mode</div><p><br />
</p>
<h3><a class="anchor" id="autotoc_md83"></a>
Queued buffer mode</h3>
<p>The queued buffer mode will not cycle any images and therefore never overwrite any image coming from the camera. In this mode the <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code> method will always return the oldest image in the queue. This is a typical requirement for machine vision applications where each image is triggered, for example by a light barrier to correspond to one product on a conveyer belt which should be inspected by an automated inspection systems.</p>
<p>It is important to understand that you can still loose images! It is your responsibility to ensure you application releases the image buffers so that the neoAPI never runs out of usable buffers. Otherwise, neoAPI will stop recording new images. This mode is ideal for applications where your image recognition task can run in parallel to work on many images simultaneously.</p>
<p>To switch into the queued buffer mode, the <code><a class="el" href="a00863.html#a2f19563bca5378be0b09e23dc1fd70d1" title="Set the number of internal used image buffers The number of internal image buffers is equal to the ma...">neoapi.Cam.SetImageBufferCount()</a></code> needs to be equal to the <code><a class="el" href="a00863.html#af2f0ac4b47d3afdb2f90c42891b8909e" title="Set the number of internal image buffers to be cycled automatically By changing the SetImageBufferCou...">neoapi.Cam.SetImageBufferCycleCount()</a></code>.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">camera.SetImageBufferCount(50)       # set the size of the buffer queue to 50</div><div class="line">camera.SetImageBufferCycleCount(50)  # and the cycle count as well</div></div><!-- fragment --> <div class="caption">Example: The buffer configuration for the queued buffer mode</div><p><br />
</p>
<h3><a class="anchor" id="autotoc_md84"></a>
User buffer mode</h3>
<p>Like described above a image buffer queue is provided by neoAPI. The default behaviour is to allocate the required memory internally. In some situations it will be important to have more control of the buffers. For example to increase the performance by reducing the amount of allocations or to reduce copy operations when using gpu accelareted functions with shared memory.</p>
<p>For these needs neoAPI provide the user buffer functionality. In a simple scenario you can allocate your own memory, create a <code><a class="el" href="a00879.html" title="Base class to derive from for use as user buffer.">neoapi.BufferBase</a></code> object, register your memory with <code><a class="el" href="a00879.html#accdcffb1da9a2438b3c3df5d1b2bd377" title="providing a continues memory block used for a whole buffer To obtain the required size Cam....">neoapi.BufferBase.RegisterMemory()</a></code> and add this buffer with <code><a class="el" href="a00859.html#ad62bf7e7209b986b3f0140301273b202" title="Add a user allocated memory for use as buffer in UserBufferMode A buffer is only used if the buffer h...">neoapi.Cam.AddUserBuffer()</a></code> to the image buffer queue.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">camera.f.Width.Set(8)</div><div class="line">camera.f.Height.Set(8)</div><div class="line"></div><div class="line"># create a buffer</div><div class="line">mem = bytearray(camera.f.PayloadSize.value)</div><div class="line">buf = neoapi.BufferBase()</div><div class="line">buf.RegisterMemory(mem, camera.f.PayloadSize.value)</div><div class="line"></div><div class="line">camera.AddUserBuffer(buf)</div><div class="line">camera.SetUserBufferMode()</div><div class="line">image = camera.GetImage()  # get an image from the camera</div></div><!-- fragment --><p>A more advanced use case is to inherit from <code>NeoAPI.BufferBase</code> and keep the allocated memory and other objects that are needed by this buffer together. After obtaining an image you can use <code>NeoAPI.Image.GetUserBuffer()</code> to access the user buffer object that belongs to this image. Please see the edgedetect examples where this is demonstrated with OpenCV/OpenCL and CUDA Buffers.</p>
<h2><a class="anchor" id="autotoc_md85"></a>
Meta- and Chunk data</h2>
<p>With an image you can acquire additional metadata. If the data is generated by the camera, this is called chunk data in the GenICam world. By default, all chunk data provided by the camera is disabled to save bandwidth on the often limiting interface (1 Gig Ethernet or USB3).</p>
<p>The neoAPI provides several methods to help configure the chunk data settings.</p>
<p>You can call <code><a class="el" href="a00859.html#a535fb59548f1b8613979a9d3cd261371" title="Query the list of the names of the existing chunk data To activate individual chunk data in a targete...">neoapi.Cam.GetAvailableChunks()</a></code> will return a tuple of available chunk information. You can then enable Chunks using <code><a class="el" href="a00863.html#abec16d1554a6f884755c3e6e88ebf1e9" title="Allow all or individual chunk data If name is empty all chunk data will processed.">neoapi.Cam.EnableChunk()</a></code>. If called without parameter, all Chunks will be enabled. You can provide the name of the Chunk as parameter to enable just this Chunk.</p>
<blockquote class="doxtable">
<p>Attention:</p>
<p>If you call <code><a class="el" href="a00863.html#ad681192391a74de7e565c0d27eda715d" title="Disallow all or individual chunk data If name is empty all chunk data will processed.">neoapi.Cam.DisableChunk()</a></code> you will also disable to receive the actual image data, you have to enable that again manually. </p>
</blockquote>
<div class="fragment"><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">chunks = camera.GetAvailableChunks()    # get a list of the available chunks from the camera</div><div class="line">print(chunks)</div><div class="line">camera.DisableChunk()                   # disable all chunks</div><div class="line">camera.EnableChunk(&#39;Image&#39;)             # enable the Image chunk to receive the data of the image</div><div class="line">camera.EnableChunk(&#39;ExposureTime&#39;)      # choose a Chunk to enable</div><div class="line">i = camera.GetImage()                   # get an image from the camera</div><div class="line">if not i.IsEmpty():</div><div class="line">    chunklist = i.GetChunkList()        # get the list of chunks for the image (behaves like feature list)</div><div class="line">    c = chunklist[&#39;ChunkExposureTime&#39;]  # get the Chunk element (prepend &quot;Chunk&quot; to not confuse it with the actual feature of the camera)</div><div class="line">    print(c.GetName())</div><div class="line">    print(c.GetValue())</div></div><!-- fragment --> <div class="caption">Example: Get Chunk data with your image </div><p><br />
</p>
<p>Some information about images is available without enabling the Chunk. You can retrieve them directly from the Image object.</p>
<div class="fragment"><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">i = camera.GetImage()                   # get an image from the camera</div><div class="line">if not i.IsEmpty():</div><div class="line">    print(i.GetBufferID())               # get the meta data from the image</div><div class="line">    print(i.GetImageIndex())</div><div class="line">    print(i.GetTimestamp())</div><div class="line">    print(i.GetSize())</div><div class="line">    print(i.GetPixelFormat())</div></div><!-- fragment --> <div class="caption">Example: Get Metadata directly from the image</div><p><br />
</p>
<h2><a class="anchor" id="autotoc_md86"></a>
GenDC transfer mode</h2>
<p>Some cameras can transmit GenDC buffers. These are self describing buffers able to hold more than a single image. For example a stereoscope camera can transmit a single buffer containing images from 2 sensors. This means one trigger event can produce multiple images.</p>
<p>To get all images grouped in one GenDC buffer <code><a class="el" href="a00859.html#a53107564ffb57385f19127eea6435f64" title="Return all images from the current buffer.">neoapi.Cam.GetImages()</a></code> should be used.</p>
<p>It is also possible to obtain the images one by one using <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code>. The first call to <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code> will return the first image in the buffer. The next call will return the next image. The buffer is claimed until all images are received. To identify the last image of a GenDC buffer you should use <code><a class="el" href="a00855.html#a2162ba8e37ec2f240183953c984a4d30" title="gives information if this image is the last image in a buffer">neoapi.Image.IsLastImage()</a></code>.</p>
<div class="fragment"><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line"></div><div class="line"># get all images grouped in one container (recommended)</div><div class="line">std.vector&lt;NeoAPI.Image&gt; imgs = camera.GetImages(); // get all images inside a multi image buffer</div><div class="line">std.cout &lt;&lt; &quot;Obtained images: &quot; &lt;&lt; imgs.size() &lt;&lt; std.endl;</div><div class="line"></div><div class="line"># access images one by one</div><div class="line">for (NeoAPI.Image image = camera.GetImage(); !image.IsLastImage();) {</div><div class="line">    std.cout &lt;&lt; image.GetImageIndex() &lt;&lt; std.endl;</div><div class="line">}</div></div><!-- fragment --> <div class="caption">Example: Enable and access multi image buffers</div><p><br />
</p>
<p>When connecting to a camera supporting GenDC buffers, NeoAPI will automatically use the GenDC transfer mode. To identify cameras that support GenDC transfer mode it is recommended to read the "GenDCStreamingStatus"-feature.</p>
<div class="fragment"><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()  # GenDC is enabled if supported</div><div class="line"></div><div class="line"># check if GenDC is supported and active</div><div class="line">if camera.f.GenDCStreamingStatus.IsReadable() and</div><div class="line">        (camera.f.GenDCStreamingStatus.Get() == neoapi.GenDCStreamingStatus.On):</div><div class="line">    std.cout &lt;&lt; &quot;Camera will use GenDC transfer mode&quot; &lt;&lt; std.endl</div><div class="line">print(&quot;Images per Buffer: &quot;, camera.GetImagesPerBuffer())</div></div><!-- fragment --> <div class="caption">Example: Check camera if GenDC transfer mode is supported and enabled</div><p><br />
</p>
<h2><a class="anchor" id="autotoc_md87"></a>
GenDC user Buffers</h2>
<p>A GenDC buffer can consist of multiple segments and each segment can consist of multiple components (e.g. images). Multiple components can exist in one segment but one component is never spreaded over multiple segments. The layout of the buffer is a camera specific implementation and is not directly changeable. But the buffer layout may change if camera features are changed.</p>
<div class="fragment"><div class="line">|-----------|-----------:-----------|-----------|-----------|</div><div class="line">| Segment 0 |        Segment 1      | Segment 2 | Segment 3 |</div><div class="line">|    Data   |  Image 0  :  Image 1  |  Image 2  |    Data   |</div><div class="line">|-----------|-----------:-----------|-----------|-----------|</div></div><!-- fragment --> <div class="caption">GenDC buffer with 4 segments where the images in segment 1 sharing their memory segment</div><p><br />
</p>
<p>To examine the GenDC buffer layout transmitted by a camera <code><a class="el" href="a00803.html" title="Provides an object to get access to image properties even before streaming The ImageInfo object give ...">neoapi.ImageInfo</a> GetImageInfo()</code> should be used. This returns a NeoAPI.ImageInfo object that gives basic informations about the buffer segments and its containing images. Attention: Each buffer segment containing an image can also contain further (non image) components.</p>
<div class="fragment"><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line"></div><div class="line">if camera.GetImagesPerBuffer() &gt;= 2:</div><div class="line">    info1 = camera.GetImageInfo(0)</div><div class="line">    info2 = camera.GetImageInfo(1)</div><div class="line">    if info1.IsSegmentShared():</div><div class="line">        if info1.GetSegmentID() == info2.GetSegmentID():</div><div class="line">            print(&quot;image 0 shares the buffer segment with image 1 and a total size of &quot;,</div><div class="line">                  info1.GetSegmentSize())</div><div class="line">        else:</div><div class="line">            print(&quot;image 0 shares the buffer segment with an unknown component and a total size of &quot;,</div><div class="line">                  info1.GetSegmentSize())</div><div class="line">    else:</div><div class="line">        print(&quot;image 0 have it&#39;s own buffer segment with size &quot;, info1.GetSegmentSize())</div></div><!-- fragment --> <div class="caption">Example: Examining the layout of a GenDC buffer</div><p><br />
</p>
<p>To prepare a UserBuffer that can hold a whole GenDC buffer you can register the memory like described <a href="#user-buffer-mode">here</a>. For more flexibility you can also provide memory for specific buffer segments. Then the data will automatically transferred to the provided memory during acquisition. Not assigned segments will be allocated automatically. This means it is possible to provide only the memory for the segment holding image data and ignore other buffer segments because they will handled by NeoAPI.</p>
<div class="fragment"><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">camera.SetUserBufferMode()</div><div class="line"></div><div class="line"># create a buffer that holds the whole GenDC buffer</div><div class="line">size = camera.GetPayloadSize()</div><div class="line">mem = bytearray(size)</div><div class="line">buf = neoapi.BufferBase()</div><div class="line">buf.RegisterMemory(mem, size)</div><div class="line">camera.AddUserBuffer(buf)</div><div class="line"></div><div class="line"># create a buffer that provide specific memory blocks for the segments</div><div class="line"># holding the first two images of a GenDC buffer,</div><div class="line"># when adding the buffer, allocations for not assigned segments happen automatically</div><div class="line">segbuf = NeoAPI.BufferBase()</div><div class="line">for i in range(2):</div><div class="line">    info = camera.GetImageInfo(i)</div><div class="line">    segsize = info.GetSegmentSize()</div><div class="line">    segmem = bytearray(segsize)</div><div class="line">    segbuf.RegisterMemory(segmem, segsize, info.GetSegmentID())</div><div class="line">camera.AddUserBuffer(segbuf)</div></div><!-- fragment --> <div class="caption">Example: Register memory for a GenDC buffer holding multiple images</div><p><br />
 </p>
</div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
