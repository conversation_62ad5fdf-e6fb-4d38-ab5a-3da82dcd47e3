VERSION = "1.0.0"

import sys
import os
import logging

# Füge src-Verzeichnis zum Python-Pfad hinzu
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication
from gui.main_window import MainWindow
from core.config import setup_logging

def main():
    """Hauptprogramm für Baumer Kamera Temperatur Monitor"""
    try:
        setup_logging()
        logging.info(f"Starte Baumer Temperatur Monitor v{VERSION}")

        app = QApplication(sys.argv)
        app.setApplicationName("Baumer Temperatur Monitor")
        app.setApplicationVersion(VERSION)

        # Exception Handler für Qt
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return

            logging.critical("Unbehandelte Exception:", exc_info=(exc_type, exc_value, exc_traceback))

        sys.excepthook = handle_exception

        window = MainWindow()
        window.show()

        logging.info("GUI gestartet, warte auf Benutzerinteraktion...")
        sys.exit(app.exec_())

    except Exception as e:
        logging.critical(f"Kritischer Fehler beim Programmstart: {e}")
        print(f"FEHLER: {e}")
        print("Siehe Log-Datei für Details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
