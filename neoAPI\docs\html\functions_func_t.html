<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members - Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_t"></a>- t -</h3><ul>
<li>TestEventGenerate()
: <a class="el" href="a00763.html#a29c8d028aabec2d1abe5eddd1f380bd0">neoapi.FeatureAccess</a>
</li>
<li>TestPattern()
: <a class="el" href="a00763.html#adc6b898943e75e3ec78f6bb5b1b3a5f1">neoapi.FeatureAccess</a>
</li>
<li>TestPatternGeneratorSelector()
: <a class="el" href="a00763.html#ab7825a5b8c140a8f6c92cf9798e71108">neoapi.FeatureAccess</a>
</li>
<li>TestPayloadFormatMode()
: <a class="el" href="a00763.html#a6ec8f5c5731b37a18d6294fb5b6fb34a">neoapi.FeatureAccess</a>
</li>
<li>TestPendingAck()
: <a class="el" href="a00763.html#a93cf8b870c9722c2067ca1a47e5922d5">neoapi.FeatureAccess</a>
</li>
<li>TimerDelay()
: <a class="el" href="a00763.html#a706058e0066b87686315f2e6b1720591">neoapi.FeatureAccess</a>
</li>
<li>TimerDuration()
: <a class="el" href="a00763.html#a2a5508796fd37491c23a5e3d61b298ee">neoapi.FeatureAccess</a>
</li>
<li>TimerSelector()
: <a class="el" href="a00763.html#a140cdf6847ac58392bb647f0a5f9b965">neoapi.FeatureAccess</a>
</li>
<li>TimerTriggerActivation()
: <a class="el" href="a00763.html#ad75b2994a0ec53aeb424f8f3c6422035">neoapi.FeatureAccess</a>
</li>
<li>TimerTriggerSource()
: <a class="el" href="a00763.html#ae276cbaa6c626e0427693636ac0a85e4">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatch()
: <a class="el" href="a00763.html#a016da032da455b4431107f46a1d838a4">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValue()
: <a class="el" href="a00763.html#a4d70222e7e48c16a259a193be8f9af75">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpDays()
: <a class="el" href="a00763.html#a8f0819127784a6a155a2333f14558161">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpHours()
: <a class="el" href="a00763.html#a092ef6bf1653c54743359bb9a7054182">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpMinutes()
: <a class="el" href="a00763.html#ac4ebe952008e94e52810d8b0d557e10e">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpNanoseconds()
: <a class="el" href="a00763.html#a073790309da81f57e6234e9f4d42f77a">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpSeconds()
: <a class="el" href="a00763.html#a712c59b3c84f41de58cd8998509e8ca3">neoapi.FeatureAccess</a>
</li>
<li>TimestampReset()
: <a class="el" href="a00763.html#a1c9935183470d6a040e17e52d695c8e7">neoapi.FeatureAccess</a>
</li>
<li>TLParamsLocked()
: <a class="el" href="a00763.html#a65e9eab9274311fadcba56d71c91ee30">neoapi.FeatureAccess</a>
</li>
<li>TransferControlMode()
: <a class="el" href="a00763.html#a49adeb4384f64015911e6201f30c9c63">neoapi.FeatureAccess</a>
</li>
<li>TransferOperationMode()
: <a class="el" href="a00763.html#a26a409268242d8ff3d564321e6d02c66">neoapi.FeatureAccess</a>
</li>
<li>TransferSelector()
: <a class="el" href="a00763.html#a56cef45ce54a3f9922af90ca9f27b5c9">neoapi.FeatureAccess</a>
</li>
<li>TransferStart()
: <a class="el" href="a00763.html#ab9d87bfa98941cf7f541edbac41212c9">neoapi.FeatureAccess</a>
</li>
<li>TransferStatus()
: <a class="el" href="a00763.html#ae1dfce4b6ae0a3495d140e8693639082">neoapi.FeatureAccess</a>
</li>
<li>TransferStatusSelector()
: <a class="el" href="a00763.html#a7be19377191b72d0b521a9f34cddb7ec">neoapi.FeatureAccess</a>
</li>
<li>TransferStop()
: <a class="el" href="a00763.html#a7bf751ad1f852f415e0ffa00f970d584">neoapi.FeatureAccess</a>
</li>
<li>TriggerActivation()
: <a class="el" href="a00763.html#a4fe0ebb6a3deddfd8a3b6bd511d8b6bf">neoapi.FeatureAccess</a>
</li>
<li>TriggerCounterLatch()
: <a class="el" href="a00763.html#a3d7ed37032c84f413d1f529946f26131">neoapi.FeatureAccess</a>
</li>
<li>TriggerCounterLatchValue()
: <a class="el" href="a00763.html#a0a5755c95ce0e0b3b6ebf121613b3684">neoapi.FeatureAccess</a>
</li>
<li>TriggerCounterReset()
: <a class="el" href="a00763.html#af8789c9c36b14d196d0659037e92c041">neoapi.FeatureAccess</a>
</li>
<li>TriggerDelay()
: <a class="el" href="a00763.html#af8b488294009d5dee67aebb01f8df73b">neoapi.FeatureAccess</a>
</li>
<li>TriggerEventTest()
: <a class="el" href="a00763.html#a6283f00329c2e6f780486366af8140cf">neoapi.FeatureAccess</a>
</li>
<li>TriggerMode()
: <a class="el" href="a00763.html#a4bd16b2979ce53224fdbc4f207e63916">neoapi.FeatureAccess</a>
</li>
<li>TriggerOverlap()
: <a class="el" href="a00763.html#aaf308978b92e00608ee338ba063ca832">neoapi.FeatureAccess</a>
</li>
<li>TriggerSelector()
: <a class="el" href="a00763.html#a577bbd602d63d7b8af8b76d79350bef8">neoapi.FeatureAccess</a>
</li>
<li>TriggerSoftware()
: <a class="el" href="a00763.html#a5b43d9d90f277e7b915123a6962794ff">neoapi.FeatureAccess</a>
</li>
<li>TriggerSource()
: <a class="el" href="a00763.html#a46fba3e81121fb46d0a6ffa30f57e155">neoapi.FeatureAccess</a>
</li>
<li>TxByteDelay()
: <a class="el" href="a00763.html#aebf5a7be0d96f8487b1b266c7d2a80cc">neoapi.FeatureAccess</a>
</li>
<li>TxByteDelayNormalized()
: <a class="el" href="a00763.html#afb5518132bcc02a835b394960458ed24">neoapi.FeatureAccess</a>
</li>
<li>TxCommandoLength()
: <a class="el" href="a00763.html#a7c94a038e12a4fad5df5dd96af204ed1">neoapi.FeatureAccess</a>
</li>
<li>TxDiscardedMessages()
: <a class="el" href="a00763.html#aea972219521528e7bd904ec350f981b7">neoapi.FeatureAccess</a>
</li>
<li>TxFiFo()
: <a class="el" href="a00763.html#a520b61e2ab6a0b94d8db1834ae8cbea5">neoapi.FeatureAccess</a>
</li>
<li>TxFiFoFreeBufferCount()
: <a class="el" href="a00763.html#ad0109ec42fea7929e6f5c3425d3db589">neoapi.FeatureAccess</a>
</li>
<li>TxMessageDelay()
: <a class="el" href="a00763.html#a7bd404c0e9fb5fbf20624a8f17e05832">neoapi.FeatureAccess</a>
</li>
<li>TxMessageDelayNormalized()
: <a class="el" href="a00763.html#abed07b9837cd31ff77c8d95b2809ae73">neoapi.FeatureAccess</a>
</li>
<li>TxRetryCount()
: <a class="el" href="a00763.html#a160a52ff4552a7cfee378fc1c0f4a386">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
