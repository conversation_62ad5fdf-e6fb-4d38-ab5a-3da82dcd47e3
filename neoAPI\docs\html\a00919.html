<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Connect a Camera using neoAPI</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Connect a Camera using neoAPI </div>  </div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#autotoc_md49">Physically connect the camera to the system</a><ul><li class="level2"><a href="#autotoc_md50">U3V(USB) cameras</a></li>
<li class="level2"><a href="#autotoc_md51">GigE(Ethernet) cameras</a></li>
</ul>
</li>
<li class="level1"><a href="#autotoc_md52">Connect the neoAPI to a physical camera</a></li>
<li class="level1"><a href="#autotoc_md53">Retrieve information about cameras attached to the system</a></li>
</ul>
</div>
<div class="textblock"><h1><a class="anchor" id="autotoc_md49"></a>
Physically connect the camera to the system</h1>
<p>Obviously, the first step to work with a camera is to physically connect it to the data interface and a power source.</p>
<h2><a class="anchor" id="autotoc_md50"></a>
U3V(USB) cameras</h2>
<p>All Baumer U3V cameras need to be connected to a free USB3 port on your host system. The USB3 port delivers the necessary power for the camera and provides the data interface. For Windows to recognize the camera it is necessary to install the Baumer USB driver provided in the neoAPI package. On Linux systems the libusb package is used and no further driver is necessary, however the udev-rules need to be configured to raise the available memory for the USB-system.</p>
<p>When connecting several cameras to one host, it is necessary to think about the USB architecture of your host system. Typically, a host system has several USB3 controllers. Each controller provides the specified bandwidth for USB3, however if more than one device is connected to one controller they have to share the available bandwidth. So if you need the full USB3 bandwidth, you need to ensure each camera is connected to its own USB3 controller.</p>
<p>Troubleshooting tips:</p>
<ul>
<li>Use high-quality USB3 cables as a low quality cable can influence the maximum transfer rate of the camera. Low quality cable might also not be able to deliver the necessary power for the camera.</li>
<li>Ensure, that the chosen USB-port does actually deliver the required power of your camera (see the data-sheet of your camera for details on power consumption).</li>
<li>When using a USB-hub, ensure that it is specified for the required data speed and power consumption of your camera.</li>
</ul>
<h2><a class="anchor" id="autotoc_md51"></a>
GigE(Ethernet) cameras</h2>
<p>Baumer GigE cameras are connected through standard ethernet ports. Standard GigE cameras require a free Gigabit Ethernet port to achieve their full framerate. 10GigE cameras like the LXT-series will need a free 10 Gigabit Ethernet port. On Windows systems, the Baumer filter-driver provided with the package can be installed to reduce the processor load caused by the data-transfer.</p>
<p>When connecting multiple cameras to a host system, it is important to think about the ethernet network topology. When working with switches, it needs to be ensured that the uplink can support the bandwidth requirements of multiple cameras.</p>
<p>Depending on your application, the cameras can be powered via PoE or via an external power supply. Please see the data-sheet of your camera for details.</p>
<p>You need to ensure that the camera is configured to suit your particular network settings (IP, subnet, DHCP). You'll find the <code>gevipconfig</code> tool in the tools folder of this package (learn more about it in the <a class="el" href="a00918.html">gevipconfig tool documentation</a>).</p>
<p>Troubleshooting tips:</p>
<ul>
<li>Use high-quality Ethernet cables as a low quality cable can influence the maximum transfer rate of the camera. Low quality cable might also not be able to deliver the necessary power for the camera.</li>
<li>Ensure, that the chosen power supply does actually deliver the required power of your camera (see the data-sheet of your camera for details on power consumption).</li>
<li>When using a switch, ensure that it is specified for the required data speed.</li>
</ul>
<h1><a class="anchor" id="autotoc_md52"></a>
Connect the neoAPI to a physical camera</h1>
<p>The first thing necessary to control a camera is connecting it, neoAPI makes it really easy to find and connect a camera. The code below shows how the <code><a class="el" href="a00863.html#af9342490a56163f394c07abf61064880" title="Connect a GenICam camera device to work with it The Connect method is called to establish a connectio...">neoapi.Cam.Connect()</a></code> method is used to connect to a camera.</p>
<p>In case you have just want to open an unspecified camera connected to the host, calling <code><a class="el" href="a00863.html#af9342490a56163f394c07abf61064880" title="Connect a GenICam camera device to work with it The Connect method is called to establish a connectio...">neoapi.Cam.Connect()</a></code> without any parameter will search for a connected camera and connect one if possible. You can also provide a search string to search for a specific camera. If a camera fitting the search string is found, it will be connected.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line"></div><div class="line">camera.Connect()                   # connect to any camera on the host</div><div class="line">#or</div><div class="line">camera.Connect(&quot;GigE&quot;)             # connect to a GigE camera on the host</div><div class="line">#or</div><div class="line">camera.Connect(&quot;VCXU-23M&quot;)         # connect to a camera with the name VCXU-23M</div><div class="line">#or</div><div class="line">camera.Connect(&quot;700004105902&quot;)     # connect to the camera with the specified serial number</div><div class="line">#or</div><div class="line">camera.Connect(&quot;P10-2&quot;)            # connect to the camera on the specified USB port</div></div><!-- fragment --> <div class="caption">Example: Possible search-strings for the Connect() method</div><blockquote class="doxtable">
<p>Please remember that a camera is always connected exclusively to one Cam object. That means you can only connect to a camera after all previous connections (also from other programs) are disconnected. </p>
</blockquote>
<p>Once you have your camera connected, you can work with it. However, the camera being a physical object can for reasons outside the control of the software be disconnected at any point of time (e.g. cable unplugged). So before using the camera it makes sense, to check if the camera is still connected using the <code><a class="el" href="a00859.html#ad5d532323d90fcb245a0c2e9b4079d26" title="Checks if a camera is connected Before using a camera it needs to be connected and initialized.">neoapi.Cam.IsConnected()</a></code> method. If not checked, any method called on the disconnected camera object will throw a <code><a class="el" href="a00771.html" title="No camera connected Exception.">neoapi.NotConnectedException</a></code> in case the camera is not connected anymore.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()                        # connect to any camera</div><div class="line">if camera.IsConnected():                # if the camera is connected</div><div class="line">    print(camera.f.ExposureTime.value)  # do something with the camera</div></div><!-- fragment --> <div class="caption">Example: Connect a camera</div><h1><a class="anchor" id="autotoc_md53"></a>
Retrieve information about cameras attached to the system</h1>
<p>Sometimes, it is not known beforehand which camera(s) might be attached to the system and weather they are available to be used. With the <code>NeoAPI.CamInfoList</code> and <code>NeoAPI.CamInfo</code> classes we provide an easy way to find out. The cameras are not connected to get this information and can even be in use by another program. As the CamInfoList is representing the current status of the system, there shouldn't be more than one object, therefore it is implemented as a singleton.</p>
<p>The <code>NeoAPI.CamInfoList.EnablePnPEventCallback()</code> and <code>NeoAPI.CamInfoList.GetPnPEvent()</code> Methods can be used to get notifications if a camera was connected or disconnected from the system. Please see details on the Events and <a class="el" href="a00921.html">Plug and Play Concept page</a>.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">infolist = neoapi.CamInfoList.Get()  # Get the info list</div><div class="line">infolist.Refresh()                   # Refresh the list to reflect the current status</div><div class="line"></div><div class="line">model = &quot;&quot;</div><div class="line">for info in infolist:</div><div class="line">    model = info.GetModelName()</div><div class="line">    print(info.GetModelName(), info.IsConnectable(), sep=&quot; :: &quot;) # print a list of all connected cameras with its connection status</div><div class="line"></div><div class="line"></div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect(model)              # Connect the last camera found by it&#39;s model name</div><div class="line"></div><div class="line">print(&quot;Camera connected?  &quot;, camera.IsConnected())</div></div><!-- fragment --> <div class="caption">Example: Find out which cameras are attached</div> </div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
