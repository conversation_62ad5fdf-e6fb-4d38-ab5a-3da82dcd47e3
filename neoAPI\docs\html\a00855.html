<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.Image Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00855.html">Image</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00852.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.Image Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00086.html">Cam Interface Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides an object to get access to image data and its properties The <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> object is used to retrieve, store and convert images obtained from a camera.  
 <a href="a00855.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.Image:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00855.png" usemap="#neoapi.Image_map" alt=""/>
  <map id="neoapi.Image_map" name="neoapi.Image_map">
<area href="a00803.html" title="Provides an object to get access to image properties even before streaming The ImageInfo object give ..." alt="neoapi.ImageInfo" shape="rect" coords="0,56,108,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9c163a4c5d6c5372acfb297c2c62ff08"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a9c163a4c5d6c5372acfb297c2c62ff08">__init__</a> (self, *args)</td></tr>
<tr class="memdesc:a9c163a4c5d6c5372acfb297c2c62ff08"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#a9c163a4c5d6c5372acfb297c2c62ff08">More...</a><br /></td></tr>
<tr class="separator:a9c163a4c5d6c5372acfb297c2c62ff08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1b97c1b3863997a4f2274ace6f8d68fa"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a1b97c1b3863997a4f2274ace6f8d68fa">IsEmpty</a> (self)</td></tr>
<tr class="memdesc:a1b97c1b3863997a4f2274ace6f8d68fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Check if the image is empty or filled with data.  <a href="#a1b97c1b3863997a4f2274ace6f8d68fa">More...</a><br /></td></tr>
<tr class="separator:a1b97c1b3863997a4f2274ace6f8d68fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2162ba8e37ec2f240183953c984a4d30"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a2162ba8e37ec2f240183953c984a4d30">IsLastImage</a> (self)</td></tr>
<tr class="memdesc:a2162ba8e37ec2f240183953c984a4d30"><td class="mdescLeft">&#160;</td><td class="mdescRight">gives information if this image is the last image in a buffer  <a href="#a2162ba8e37ec2f240183953c984a4d30">More...</a><br /></td></tr>
<tr class="separator:a2162ba8e37ec2f240183953c984a4d30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae418b72ce66230f42362b7226b731b1d"><td class="memItemLeft" align="right" valign="top">&quot;tuple&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#ae418b72ce66230f42362b7226b731b1d">GetAvailablePixelFormats</a> (self)</td></tr>
<tr class="memdesc:ae418b72ce66230f42362b7226b731b1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a list with available PixelFormats for Convert pixelformat must be a valid PixelFormat.  <a href="#ae418b72ce66230f42362b7226b731b1d">More...</a><br /></td></tr>
<tr class="separator:ae418b72ce66230f42362b7226b731b1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f2d9ca68a7bdc6726483914453285e3"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a8f2d9ca68a7bdc6726483914453285e3">IsPixelFormatAvailable</a> (self, &quot;str&quot; pixelformat)</td></tr>
<tr class="memdesc:a8f2d9ca68a7bdc6726483914453285e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicates whether the PixelFormat is available for conversion.  <a href="#a8f2d9ca68a7bdc6726483914453285e3">More...</a><br /></td></tr>
<tr class="separator:a8f2d9ca68a7bdc6726483914453285e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88cc2674ac9d27c44c5ad4879ee60774"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a88cc2674ac9d27c44c5ad4879ee60774">GetImageIndex</a> (self)</td></tr>
<tr class="memdesc:a88cc2674ac9d27c44c5ad4879ee60774"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the Index of the image inside the buffer, returns zero as long the image is not filled with data Only when the image belongs to a GenDC buffer this value will be greater than one.  <a href="#a88cc2674ac9d27c44c5ad4879ee60774">More...</a><br /></td></tr>
<tr class="separator:a88cc2674ac9d27c44c5ad4879ee60774"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac665b2b3f7443eaf890b3e9afa71fda7"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#ac665b2b3f7443eaf890b3e9afa71fda7">GetBufferID</a> (self)</td></tr>
<tr class="memdesc:ac665b2b3f7443eaf890b3e9afa71fda7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the ID of the buffer, returns zero as long the image is not filled with data The buffer ID allows to track which buffers have been received and if there is a gap which might show that an buffer was missed and not processed.  <a href="#ac665b2b3f7443eaf890b3e9afa71fda7">More...</a><br /></td></tr>
<tr class="separator:ac665b2b3f7443eaf890b3e9afa71fda7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7bdb17944ef845188c485610b474251e"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a7bdb17944ef845188c485610b474251e">GetTimestamp</a> (self)</td></tr>
<tr class="memdesc:a7bdb17944ef845188c485610b474251e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the timestamp of the image, returns zero as long the image is not filled with data The image timestamp allows to track when images have been recorded and if there is a gap which might show that an image was missed and not transfered.  <a href="#a7bdb17944ef845188c485610b474251e">More...</a><br /></td></tr>
<tr class="separator:a7bdb17944ef845188c485610b474251e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44768b042b6a974c18bc90197a761084"><td class="memItemLeft" align="right" valign="top">&quot;FeatureList&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a44768b042b6a974c18bc90197a761084">GetChunkList</a> (self)</td></tr>
<tr class="memdesc:a44768b042b6a974c18bc90197a761084"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a list of all available chunk features of the image.  <a href="#a44768b042b6a974c18bc90197a761084">More...</a><br /></td></tr>
<tr class="separator:a44768b042b6a974c18bc90197a761084"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0688ec4d1bb9e85d943512fdf0fa6c54"><td class="memItemLeft" align="right" valign="top">&quot;Image&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a0688ec4d1bb9e85d943512fdf0fa6c54">Copy</a> (self)</td></tr>
<tr class="memdesc:a0688ec4d1bb9e85d943512fdf0fa6c54"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copies the <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> object and its image data.  <a href="#a0688ec4d1bb9e85d943512fdf0fa6c54">More...</a><br /></td></tr>
<tr class="separator:a0688ec4d1bb9e85d943512fdf0fa6c54"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7281f446abf2c472c043334bb25e6fdc"><td class="memItemLeft" align="right" valign="top">&quot;None&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a7281f446abf2c472c043334bb25e6fdc">Save</a> (self, &quot;str&quot; filename)</td></tr>
<tr class="memdesc:a7281f446abf2c472c043334bb25e6fdc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Save the image in bmp format at the given path If no path is given, the image will be stored where the executable is started.  <a href="#a7281f446abf2c472c043334bb25e6fdc">More...</a><br /></td></tr>
<tr class="separator:a7281f446abf2c472c043334bb25e6fdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af9fd5b5a5d619eb69e2c6c49e51c5e70"><td class="memItemLeft" align="right" valign="top">&quot;Image&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#af9fd5b5a5d619eb69e2c6c49e51c5e70">Convert</a> (self, *args)</td></tr>
<tr class="separator:af9fd5b5a5d619eb69e2c6c49e51c5e70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ad7ae4cf7596593572ddd5eb30fcf10"><td class="memItemLeft" align="right" valign="top">&quot;PyObject&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#a4ad7ae4cf7596593572ddd5eb30fcf10">GetImageData</a> (self)</td></tr>
<tr class="memdesc:a4ad7ae4cf7596593572ddd5eb30fcf10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a pointer to the image data, returns nullptr as long as the image is not filled with data.  <a href="#a4ad7ae4cf7596593572ddd5eb30fcf10">More...</a><br /></td></tr>
<tr class="separator:a4ad7ae4cf7596593572ddd5eb30fcf10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3d59ea6282dcbb17551891f80b3b667"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#ab3d59ea6282dcbb17551891f80b3b667">GetNPArray</a> (self)</td></tr>
<tr class="memdesc:ab3d59ea6282dcbb17551891f80b3b667"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the image data as a numpy array, returns an empty array if the image is empty.  <a href="#ab3d59ea6282dcbb17551891f80b3b667">More...</a><br /></td></tr>
<tr class="separator:ab3d59ea6282dcbb17551891f80b3b667"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aea3dfa8dcd210d1475ee8ed0d9b79e16"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#aea3dfa8dcd210d1475ee8ed0d9b79e16">GetUserBuffer</a> (self)</td></tr>
<tr class="memdesc:aea3dfa8dcd210d1475ee8ed0d9b79e16"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get access to a object inherited by <a class="el" href="a00879.html" title="Base class to derive from for use as user buffer.">neoapi.BufferBase</a> and used by this image.  <a href="#aea3dfa8dcd210d1475ee8ed0d9b79e16">More...</a><br /></td></tr>
<tr class="separator:aea3dfa8dcd210d1475ee8ed0d9b79e16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2d5b973f50c053e96ecc7edfe9da635"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ae2d5b973f50c053e96ecc7edfe9da635">GetHeight</a> (self)</td></tr>
<tr class="memdesc:ae2d5b973f50c053e96ecc7edfe9da635"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the height of the image in pixel, returns zero as long the image is not filled with data.  <a href="#ae2d5b973f50c053e96ecc7edfe9da635">More...</a><br /></td></tr>
<tr class="separator:ae2d5b973f50c053e96ecc7edfe9da635"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74ea98a5251ad92bcfc39dffb7e46ced"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a74ea98a5251ad92bcfc39dffb7e46ced">GetYOffset</a> (self)</td></tr>
<tr class="memdesc:a74ea98a5251ad92bcfc39dffb7e46ced"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the y offset in pixel.  <a href="#a74ea98a5251ad92bcfc39dffb7e46ced">More...</a><br /></td></tr>
<tr class="separator:a74ea98a5251ad92bcfc39dffb7e46ced"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa68a32ae42c15e79caace60e346a4ed6"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#aa68a32ae42c15e79caace60e346a4ed6">GetYPadding</a> (self)</td></tr>
<tr class="memdesc:aa68a32ae42c15e79caace60e346a4ed6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the number of extra bytes transmitted at the end of the image.  <a href="#aa68a32ae42c15e79caace60e346a4ed6">More...</a><br /></td></tr>
<tr class="separator:aa68a32ae42c15e79caace60e346a4ed6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae70432b723d9c832f6abb5cae6030bd5"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ae70432b723d9c832f6abb5cae6030bd5">GetWidth</a> (self)</td></tr>
<tr class="memdesc:ae70432b723d9c832f6abb5cae6030bd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the width of the image in pixel, returns zero as long the image is not filled with data.  <a href="#ae70432b723d9c832f6abb5cae6030bd5">More...</a><br /></td></tr>
<tr class="separator:ae70432b723d9c832f6abb5cae6030bd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27e12c31a0a6257a2f34c5549412341a"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a27e12c31a0a6257a2f34c5549412341a">GetXOffset</a> (self)</td></tr>
<tr class="memdesc:a27e12c31a0a6257a2f34c5549412341a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the x offset in pixel.  <a href="#a27e12c31a0a6257a2f34c5549412341a">More...</a><br /></td></tr>
<tr class="separator:a27e12c31a0a6257a2f34c5549412341a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae264d4741c2e3768699e8305f4502bbe"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ae264d4741c2e3768699e8305f4502bbe">GetXPadding</a> (self)</td></tr>
<tr class="memdesc:ae264d4741c2e3768699e8305f4502bbe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the the number of extra bytes transmitted at the end of each line.  <a href="#ae264d4741c2e3768699e8305f4502bbe">More...</a><br /></td></tr>
<tr class="separator:ae264d4741c2e3768699e8305f4502bbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66e5a1a4759118139478186645dcde8e"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a66e5a1a4759118139478186645dcde8e">GetPixelFormat</a> (self)</td></tr>
<tr class="memdesc:a66e5a1a4759118139478186645dcde8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the pixelformat of the image, returns an empty string as long the image is not filled with data The GenICam SFNC defines many different PixelFormats which are used to transfer image data from a camera such as RGB, YUV and BayerRG.  <a href="#a66e5a1a4759118139478186645dcde8e">More...</a><br /></td></tr>
<tr class="separator:a66e5a1a4759118139478186645dcde8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a4b22523f4c2591f18a92aa6672cb3b"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a2a4b22523f4c2591f18a92aa6672cb3b">GetSize</a> (self)</td></tr>
<tr class="memdesc:a2a4b22523f4c2591f18a92aa6672cb3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the whole size of the image in bytes.  <a href="#a2a4b22523f4c2591f18a92aa6672cb3b">More...</a><br /></td></tr>
<tr class="separator:a2a4b22523f4c2591f18a92aa6672cb3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d5595e748e4efee67fb72bca5d12d51"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a4d5595e748e4efee67fb72bca5d12d51">GetGroupID</a> (self)</td></tr>
<tr class="memdesc:a4d5595e748e4efee67fb72bca5d12d51"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the group id of the image.  <a href="#a4d5595e748e4efee67fb72bca5d12d51">More...</a><br /></td></tr>
<tr class="separator:a4d5595e748e4efee67fb72bca5d12d51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c62cef40e061aa4b5648e15f6d45739"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a4c62cef40e061aa4b5648e15f6d45739">GetSourceID</a> (self)</td></tr>
<tr class="memdesc:a4c62cef40e061aa4b5648e15f6d45739"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the source id of the image.  <a href="#a4c62cef40e061aa4b5648e15f6d45739">More...</a><br /></td></tr>
<tr class="separator:a4c62cef40e061aa4b5648e15f6d45739"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1dd62ce238c06939313a60842098db1b"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a1dd62ce238c06939313a60842098db1b">GetRegionID</a> (self)</td></tr>
<tr class="memdesc:a1dd62ce238c06939313a60842098db1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the region id of the image.  <a href="#a1dd62ce238c06939313a60842098db1b">More...</a><br /></td></tr>
<tr class="separator:a1dd62ce238c06939313a60842098db1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a501b21913ccb8e7ad6836707f96511e9"><td class="memItemLeft" align="right" valign="top">&quot;NeoImageCompression&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a501b21913ccb8e7ad6836707f96511e9">GetCompression</a> (self)</td></tr>
<tr class="memdesc:a501b21913ccb8e7ad6836707f96511e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get compression method of the image.  <a href="#a501b21913ccb8e7ad6836707f96511e9">More...</a><br /></td></tr>
<tr class="separator:a501b21913ccb8e7ad6836707f96511e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0f6176d9aba067023fc07d32cd374d7"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ab0f6176d9aba067023fc07d32cd374d7">IsSegmentShared</a> (self)</td></tr>
<tr class="memdesc:ab0f6176d9aba067023fc07d32cd374d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Give information if the memory segment in a buffer is shared with other components.  <a href="#ab0f6176d9aba067023fc07d32cd374d7">More...</a><br /></td></tr>
<tr class="separator:ab0f6176d9aba067023fc07d32cd374d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0026a34eafc3b9412a7bc3cb58c4db6d"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a0026a34eafc3b9412a7bc3cb58c4db6d">GetSegmentOffset</a> (self)</td></tr>
<tr class="memdesc:a0026a34eafc3b9412a7bc3cb58c4db6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the image data offset related to the begin of the memory segment in the buffer.  <a href="#a0026a34eafc3b9412a7bc3cb58c4db6d">More...</a><br /></td></tr>
<tr class="separator:a0026a34eafc3b9412a7bc3cb58c4db6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00db0b522111679788d2624d5c4e32bb"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a00db0b522111679788d2624d5c4e32bb">GetSegmentSize</a> (self)</td></tr>
<tr class="memdesc:a00db0b522111679788d2624d5c4e32bb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the whole size of the memory segment in the buffer.  <a href="#a00db0b522111679788d2624d5c4e32bb">More...</a><br /></td></tr>
<tr class="separator:a00db0b522111679788d2624d5c4e32bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab40fa50855bca84a534c90af65c32230"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ab40fa50855bca84a534c90af65c32230">GetSegmentIndex</a> (self)</td></tr>
<tr class="memdesc:ab40fa50855bca84a534c90af65c32230"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the index of the memory segment in the buffer.  <a href="#ab40fa50855bca84a534c90af65c32230">More...</a><br /></td></tr>
<tr class="separator:ab40fa50855bca84a534c90af65c32230"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:aea66aea381f903a8ae7cc7d4f6685822"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00855.html#aea66aea381f903a8ae7cc7d4f6685822">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:aea66aea381f903a8ae7cc7d4f6685822"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#aea66aea381f903a8ae7cc7d4f6685822">More...</a><br /></td></tr>
<tr class="separator:aea66aea381f903a8ae7cc7d4f6685822"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Provides an object to get access to image data and its properties The <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> object is used to retrieve, store and convert images obtained from a camera. </p>
<p><a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> provides a set of methodes to get information about, and work with an image (such as pixelformat, size and offset). When retrieving an image from a camera, the <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> object will reference an image-buffer without copying it's data for performance reasons. If you copy the <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> object using the "=" operator the image data will still not be copied but referenced. If you want to copy image data please use the <a class="el" href="a00855.html#a0688ec4d1bb9e85d943512fdf0fa6c54" title="Copies the Image object and its image data.">Image.Copy()</a> or <a class="el" href="a00855.html#af9fd5b5a5d619eb69e2c6c49e51c5e70">Image.Convert()</a> methods </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a9c163a4c5d6c5372acfb297c2c62ff08"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9c163a4c5d6c5372acfb297c2c62ff08">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.Image.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructor. </p>

<p>Reimplemented from <a class="el" href="a00803.html#a8b9c3ab561324333874160df5007205f">neoapi.ImageInfo</a>.</p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a1b97c1b3863997a4f2274ace6f8d68fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1b97c1b3863997a4f2274ace6f8d68fa">&#9670;&nbsp;</a></span>IsEmpty()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.Image.IsEmpty </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Check if the image is empty or filled with data. </p>
<dl class="section return"><dt>Returns</dt><dd>False if the image is filled with data, otherwise true </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">CamBase.GetImage()</a> </dd></dl>

</div>
</div>
<a id="a2162ba8e37ec2f240183953c984a4d30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2162ba8e37ec2f240183953c984a4d30">&#9670;&nbsp;</a></span>IsLastImage()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.Image.IsLastImage </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>gives information if this image is the last image in a buffer </p>
<dl class="section note"><dt>Note</dt><dd>calling this on a common single image buffer returns true </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>true if image is the last image in a buffer, otherwise false </dd></dl>

</div>
</div>
<a id="ae418b72ce66230f42362b7226b731b1d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae418b72ce66230f42362b7226b731b1d">&#9670;&nbsp;</a></span>GetAvailablePixelFormats()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;tuple&quot; neoapi.Image.GetAvailablePixelFormats </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get a list with available PixelFormats for Convert pixelformat must be a valid PixelFormat. </p>
<dl class="section return"><dt>Returns</dt><dd>list of available PixelFormats </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">CamBase.GetImage()</a> </dd></dl>

</div>
</div>
<a id="a8f2d9ca68a7bdc6726483914453285e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8f2d9ca68a7bdc6726483914453285e3">&#9670;&nbsp;</a></span>IsPixelFormatAvailable()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.Image.IsPixelFormatAvailable </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;str&quot;&#160;</td>
          <td class="paramname"><em>pixelformat</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Indicates whether the PixelFormat is available for conversion. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">pixelformat</td><td>The target PixelFormat name </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>True if the pixelformat is available to work with, otherwise false </dd></dl>

</div>
</div>
<a id="a88cc2674ac9d27c44c5ad4879ee60774"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a88cc2674ac9d27c44c5ad4879ee60774">&#9670;&nbsp;</a></span>GetImageIndex()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.Image.GetImageIndex </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the Index of the image inside the buffer, returns zero as long the image is not filled with data Only when the image belongs to a GenDC buffer this value will be greater than one. </p>
<dl class="section return"><dt>Returns</dt><dd>The Index of the image </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">CamBase.GetImage()</a> </dd></dl>

</div>
</div>
<a id="ac665b2b3f7443eaf890b3e9afa71fda7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac665b2b3f7443eaf890b3e9afa71fda7">&#9670;&nbsp;</a></span>GetBufferID()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.Image.GetBufferID </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the ID of the buffer, returns zero as long the image is not filled with data The buffer ID allows to track which buffers have been received and if there is a gap which might show that an buffer was missed and not processed. </p>
<dl class="section return"><dt>Returns</dt><dd>The ID of the buffer holding the image </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">CamBase.GetImage()</a> </dd></dl>

</div>
</div>
<a id="a7bdb17944ef845188c485610b474251e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7bdb17944ef845188c485610b474251e">&#9670;&nbsp;</a></span>GetTimestamp()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.Image.GetTimestamp </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the timestamp of the image, returns zero as long the image is not filled with data The image timestamp allows to track when images have been recorded and if there is a gap which might show that an image was missed and not transfered. </p>
<dl class="section return"><dt>Returns</dt><dd>The timestamp of the image </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">CamBase.GetImage()</a> </dd></dl>

</div>
</div>
<a id="a44768b042b6a974c18bc90197a761084"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44768b042b6a974c18bc90197a761084">&#9670;&nbsp;</a></span>GetChunkList()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;FeatureList&quot; neoapi.Image.GetChunkList </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get a list of all available chunk features of the image. </p>
<dl class="section return"><dt>Returns</dt><dd>A list of available chunk features </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00815.html" title="Provides list functionality for camera features.">FeatureList</a> </dd></dl>

</div>
</div>
<a id="a0688ec4d1bb9e85d943512fdf0fa6c54"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0688ec4d1bb9e85d943512fdf0fa6c54">&#9670;&nbsp;</a></span>Copy()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;Image&quot; neoapi.Image.Copy </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Copies the <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> object and its image data. </p>
<dl class="section note"><dt>Note</dt><dd>Copy works only with uncompressed images. </dd>
<dd>
Memory related meta data like SegmentSize will be copied also. </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>A new <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00783.html" title="Requesting an image while holding all available image resources.">NoImageBufferException</a></td><td>The image could not be copied. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">CamBase.GetImage()</a> </dd></dl>

</div>
</div>
<a id="a7281f446abf2c472c043334bb25e6fdc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7281f446abf2c472c043334bb25e6fdc">&#9670;&nbsp;</a></span>Save()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;None&quot; neoapi.Image.Save </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;str&quot;&#160;</td>
          <td class="paramname"><em>filename</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Save the image in bmp format at the given path If no path is given, the image will be stored where the executable is started. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">filename</td><td>The filename or path for the saved image </td></tr>
  </table>
  </dd>
</dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00787.html" title="File not accessible Exception.">FileAccessException</a></td><td>The image could not be saved at the given Path </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">CamBase.GetImage()</a> </dd></dl>

</div>
</div>
<a id="af9fd5b5a5d619eb69e2c6c49e51c5e70"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af9fd5b5a5d619eb69e2c6c49e51c5e70">&#9670;&nbsp;</a></span>Convert()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;Image&quot; neoapi.Image.Convert </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment"> Please see neoapi.PixelFormat for a list of possible formats.
</pre><dl class="section note"><dt>Note</dt><dd>Not all input formats can be converted to all output formats. You can use the <a class="el" href="a00855.html#ae418b72ce66230f42362b7226b731b1d" title="Get a list with available PixelFormats for Convert pixelformat must be a valid PixelFormat.">Image.GetAvailablePixelFormats()</a> or the <a class="el" href="a00855.html#a8f2d9ca68a7bdc6726483914453285e3" title="Indicates whether the PixelFormat is available for conversion.">Image.IsPixelFormatAvailable()</a> to check if a conversion is possible.</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>pixelformat</b> The target PixelFormat name<br />
 or<br />
 <b>settings</b> The settings for the converted image. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> with requested Settings </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00855.html#ae418b72ce66230f42362b7226b731b1d" title="Get a list with available PixelFormats for Convert pixelformat must be a valid PixelFormat.">Image.GetAvailablePixelFormats()</a> </dd>
<dd>
<a class="el" href="a00855.html#a8f2d9ca68a7bdc6726483914453285e3" title="Indicates whether the PixelFormat is available for conversion.">Image.IsPixelFormatAvailable()</a> </dd></dl>

</div>
</div>
<a id="a4ad7ae4cf7596593572ddd5eb30fcf10"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4ad7ae4cf7596593572ddd5eb30fcf10">&#9670;&nbsp;</a></span>GetImageData()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;PyObject&quot; neoapi.Image.GetImageData </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get a pointer to the image data, returns nullptr as long as the image is not filled with data. </p>
<dl class="section return"><dt>Returns</dt><dd>The pointer to image data </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">CamBase.GetImage()</a> </dd></dl>

</div>
</div>
<a id="ab3d59ea6282dcbb17551891f80b3b667"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab3d59ea6282dcbb17551891f80b3b667">&#9670;&nbsp;</a></span>GetNPArray()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.Image.GetNPArray </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the image data as a numpy array, returns an empty array if the image is empty. </p>
<dl class="section return"><dt>Returns</dt><dd>The image data as numpy array </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">CamBase.GetImage()</a> </dd></dl>

</div>
</div>
<a id="aea3dfa8dcd210d1475ee8ed0d9b79e16"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aea3dfa8dcd210d1475ee8ed0d9b79e16">&#9670;&nbsp;</a></span>GetUserBuffer()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.Image.GetUserBuffer </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get access to a object inherited by <a class="el" href="a00879.html" title="Base class to derive from for use as user buffer.">neoapi.BufferBase</a> and used by this image. </p>
<dl class="section attention"><dt>Attention</dt><dd>When the image object goes out of scope the underlying <a class="el" href="a00879.html" title="Base class to derive from for use as user buffer.">neoapi.BufferBase</a> object will be reused for image acquisition again. This means the content of registered memory region may change then. </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>UserBuffer object </dd></dl>

</div>
</div>
<a id="ae2d5b973f50c053e96ecc7edfe9da635"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae2d5b973f50c053e96ecc7edfe9da635">&#9670;&nbsp;</a></span>GetHeight()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetHeight </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the height of the image in pixel, returns zero as long the image is not filled with data. </p>
<dl class="section return"><dt>Returns</dt><dd>The height of the image in pixel. // TODO: kepp after convert </dd></dl>

</div>
</div>
<a id="a74ea98a5251ad92bcfc39dffb7e46ced"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a74ea98a5251ad92bcfc39dffb7e46ced">&#9670;&nbsp;</a></span>GetYOffset()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetYOffset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Return the y offset in pixel. </p>
<dl class="section return"><dt>Returns</dt><dd>The y offset in pixel // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="aa68a32ae42c15e79caace60e346a4ed6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa68a32ae42c15e79caace60e346a4ed6">&#9670;&nbsp;</a></span>GetYPadding()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetYPadding </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Return the number of extra bytes transmitted at the end of the image. </p>
<dl class="section return"><dt>Returns</dt><dd>The y padding of the image in byte. // TODO: 0 after convert </dd></dl>

</div>
</div>
<a id="ae70432b723d9c832f6abb5cae6030bd5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae70432b723d9c832f6abb5cae6030bd5">&#9670;&nbsp;</a></span>GetWidth()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetWidth </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the width of the image in pixel, returns zero as long the image is not filled with data. </p>
<dl class="section return"><dt>Returns</dt><dd>The width of the image in pixel. // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="a27e12c31a0a6257a2f34c5549412341a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27e12c31a0a6257a2f34c5549412341a">&#9670;&nbsp;</a></span>GetXOffset()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetXOffset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Return the x offset in pixel. </p>
<dl class="section return"><dt>Returns</dt><dd>The x offset in pixel // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="ae264d4741c2e3768699e8305f4502bbe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae264d4741c2e3768699e8305f4502bbe">&#9670;&nbsp;</a></span>GetXPadding()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetXPadding </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Return the the number of extra bytes transmitted at the end of each line. </p>
<dl class="section return"><dt>Returns</dt><dd>The x padding of the image in byte // TODO: 0 after convert </dd></dl>

</div>
</div>
<a id="a66e5a1a4759118139478186645dcde8e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66e5a1a4759118139478186645dcde8e">&#9670;&nbsp;</a></span>GetPixelFormat()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.ImageInfo.GetPixelFormat </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the pixelformat of the image, returns an empty string as long the image is not filled with data The GenICam SFNC defines many different PixelFormats which are used to transfer image data from a camera such as RGB, YUV and BayerRG. </p>
<p>Knowledge about it is essential so the image can be converted to a format needed to process it </p><dl class="section note"><dt>Note</dt><dd>More information about the GenICam SFNC (Standard Feature Naming Convention) can be found in the GenICam SFNC Document on the <a href="https://www.emva.org/wp-content/uploads/GenICam_SFNC_v2_4.pdf">EMVA Website</a> </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>The PixelFormat of the <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> // TODO: ask image after convert </dd></dl>

</div>
</div>
<a id="a2a4b22523f4c2591f18a92aa6672cb3b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a4b22523f4c2591f18a92aa6672cb3b">&#9670;&nbsp;</a></span>GetSize()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSize </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the whole size of the image in bytes. </p>
<dl class="section return"><dt>Returns</dt><dd>the image size in bytes // TODO: ask image after convert </dd></dl>

</div>
</div>
<a id="a4d5595e748e4efee67fb72bca5d12d51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4d5595e748e4efee67fb72bca5d12d51">&#9670;&nbsp;</a></span>GetGroupID()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetGroupID </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Return the group id of the image. </p>
<dl class="section note"><dt>Note</dt><dd>in case of a non GenDC buffer this returns always 0 </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the group id // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="a4c62cef40e061aa4b5648e15f6d45739"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4c62cef40e061aa4b5648e15f6d45739">&#9670;&nbsp;</a></span>GetSourceID()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSourceID </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Return the source id of the image. </p>
<dl class="section note"><dt>Note</dt><dd>in case of a non GenDC buffer this returns always 0 </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the source id // TODO: kepp after convert </dd></dl>

</div>
</div>
<a id="a1dd62ce238c06939313a60842098db1b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1dd62ce238c06939313a60842098db1b">&#9670;&nbsp;</a></span>GetRegionID()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetRegionID </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Return the region id of the image. </p>
<dl class="section note"><dt>Note</dt><dd>in case of a non GenDC buffer this returns always 0 </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the region id // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="a501b21913ccb8e7ad6836707f96511e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a501b21913ccb8e7ad6836707f96511e9">&#9670;&nbsp;</a></span>GetCompression()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;NeoImageCompression&quot; neoapi.ImageInfo.GetCompression </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get compression method of the image. </p>
<dl class="section return"><dt>Returns</dt><dd>The compression method // TODO: RAW after convert </dd></dl>

</div>
</div>
<a id="ab0f6176d9aba067023fc07d32cd374d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab0f6176d9aba067023fc07d32cd374d7">&#9670;&nbsp;</a></span>IsSegmentShared()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.ImageInfo.IsSegmentShared </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Give information if the memory segment in a buffer is shared with other components. </p>
<dl class="section return"><dt>Returns</dt><dd>true if segment is shared with other components, otherwise false // TODO: refresh value after convert </dd></dl>

</div>
</div>
<a id="a0026a34eafc3b9412a7bc3cb58c4db6d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0026a34eafc3b9412a7bc3cb58c4db6d">&#9670;&nbsp;</a></span>GetSegmentOffset()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSegmentOffset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the image data offset related to the begin of the memory segment in the buffer. </p>
<dl class="section return"><dt>Returns</dt><dd>the offset of the image data // TODO: refresh value after convert </dd></dl>

</div>
</div>
<a id="a00db0b522111679788d2624d5c4e32bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a00db0b522111679788d2624d5c4e32bb">&#9670;&nbsp;</a></span>GetSegmentSize()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSegmentSize </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the whole size of the memory segment in the buffer. </p>
<dl class="section note"><dt>Note</dt><dd>when the segment only contains this image the result is identical to <a class="el" href="a00803.html#a2a4b22523f4c2591f18a92aa6672cb3b" title="Get the whole size of the image in bytes.">GetSize()</a> </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the memory size in bytes // TODO: refresh value after convert </dd></dl>

</div>
</div>
<a id="ab40fa50855bca84a534c90af65c32230"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab40fa50855bca84a534c90af65c32230">&#9670;&nbsp;</a></span>GetSegmentIndex()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSegmentIndex </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the index of the memory segment in the buffer. </p>
<dl class="section return"><dt>Returns</dt><dd>the segment index // TODO: 0 after convert </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="aea66aea381f903a8ae7cc7d4f6685822"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aea66aea381f903a8ae7cc7d4f6685822">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.Image.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
