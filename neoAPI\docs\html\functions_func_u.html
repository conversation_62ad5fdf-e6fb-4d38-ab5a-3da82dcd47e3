<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members - Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_u"></a>- u -</h3><ul>
<li>UnregisterMemory()
: <a class="el" href="a00879.html#a32274dfadf9ff612958c8bdda5ad4043">neoapi.BufferBase</a>
</li>
<li>USB2SupportEnable()
: <a class="el" href="a00763.html#a9c73eb4e751549fc648c4c99e2cac181">neoapi.FeatureAccess</a>
</li>
<li>UserOutputSelector()
: <a class="el" href="a00763.html#a0e3751198c8ff68918518dc6b6832156">neoapi.FeatureAccess</a>
</li>
<li>UserOutputValue()
: <a class="el" href="a00763.html#ad980a43e7a7f81cdece4ac208a1eee9d">neoapi.FeatureAccess</a>
</li>
<li>UserOutputValueAll()
: <a class="el" href="a00763.html#a009d36ce4a49f82dc4e9f2ef5b6ff82c">neoapi.FeatureAccess</a>
</li>
<li>UserSetData()
: <a class="el" href="a00763.html#a268cff12722aa59175477c6165ed993f">neoapi.FeatureAccess</a>
</li>
<li>UserSetDataEnable()
: <a class="el" href="a00763.html#a6120654b38aa6affc9c5dddf3ec8eb4f">neoapi.FeatureAccess</a>
</li>
<li>UserSetDefault()
: <a class="el" href="a00763.html#a8242f164ebaac5f8f3ae1889907de7dd">neoapi.FeatureAccess</a>
</li>
<li>UserSetFeatureEnable()
: <a class="el" href="a00763.html#a270ebd0014c3713f0a872f4097d54bfa">neoapi.FeatureAccess</a>
</li>
<li>UserSetFeatureSelector()
: <a class="el" href="a00763.html#a3da001768b3e08e6017f018bda43a4b7">neoapi.FeatureAccess</a>
</li>
<li>UserSetLoad()
: <a class="el" href="a00763.html#af312813e759d7c0a193235ce639490fe">neoapi.FeatureAccess</a>
</li>
<li>UserSetSave()
: <a class="el" href="a00763.html#a2babd879415d2b070bd17966b7748c30">neoapi.FeatureAccess</a>
</li>
<li>UserSetSelector()
: <a class="el" href="a00763.html#a4f3c9fccc5d4b739cedbdad45e0b42ff">neoapi.FeatureAccess</a>
</li>
<li>UserSetStartAddressSelector()
: <a class="el" href="a00763.html#a044be309ae4f234303549b0d864e54cd">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
