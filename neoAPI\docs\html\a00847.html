<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.EnumerationFeature Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00847.html">EnumerationFeature</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00844.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.EnumerationFeature Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00087.html">GenICam Feature Interface</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Base class providing the 'IEnumeration' interface.  
 <a href="a00847.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.EnumerationFeature:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00847.png" usemap="#neoapi.EnumerationFeature_map" alt=""/>
  <map id="neoapi.EnumerationFeature_map" name="neoapi.EnumerationFeature_map">
<area href="a00823.html" title="Base Feature class providing the interface to be used independent of Feature data-type." alt="neoapi.BaseFeature" shape="rect" coords="0,56,310,80"/>
<area href="a00095.html" title="Sets the acquisition mode of the device." alt="neoapi.CAcquisitionMode" shape="rect" coords="320,168,630,192"/>
<area href="a00099.html" title="Selects the internal acquisition signal to read using AcquisitionStatus." alt="neoapi.CAcquisitionStatusSelector" shape="rect" coords="320,224,630,248"/>
<area href="a00103.html" title="Reads the status of the aperture." alt="neoapi.CApertureStatus" shape="rect" coords="320,280,630,304"/>
<area href="a00107.html" title="Controls the mode of the selected Auto Feature Region." alt="neoapi.CAutoFeatureRegionMode" shape="rect" coords="320,336,630,360"/>
<area href="a00111.html" title="Selects the Reference Region of interest." alt="neoapi.CAutoFeatureRegionReference" shape="rect" coords="320,392,630,416"/>
<area href="a00115.html" title="Selects the region of interest to control." alt="neoapi.CAutoFeatureRegionSelector" shape="rect" coords="320,448,630,472"/>
<area href="a00123.html" title="Controls the mode for automatic white balancing between the color channels." alt="neoapi.CBalanceWhiteAuto" shape="rect" coords="320,504,630,528"/>
<area href="a00127.html" title="Returns the status of BalanceWhiteAuto." alt="neoapi.CBalanceWhiteAutoStatus" shape="rect" coords="320,560,630,584"/>
<area href="a00131.html" title="Sets the baud rate of the RS232 interface." alt="neoapi.CBaudrate" shape="rect" coords="320,616,630,640"/>
<area href="a00135.html" title="Sets the mode to use to combine horizontal photo-sensitive cells together when BinningHorizontal is u..." alt="neoapi.CBinningHorizontalMode" shape="rect" coords="320,672,630,696"/>
<area href="a00139.html" title="Selects which binning engine is controlled by the BinningHorizontal and BinningVertical features." alt="neoapi.CBinningSelector" shape="rect" coords="320,728,630,752"/>
<area href="a00143.html" title="Sets the mode to use to combine vertical photo-sensitive cells together when BinningVertical is used." alt="neoapi.CBinningVerticalMode" shape="rect" coords="320,784,630,808"/>
<area href="a00147.html" title="Selects which Black Level is controlled by the various Black Level features." alt="neoapi.CBlackLevelSelector" shape="rect" coords="320,840,630,864"/>
<area href="a00151.html" title="Controls the sensor internal feature for avoiding the black sun effect." alt="neoapi.CBlackSunSuppression" shape="rect" coords="320,896,630,920"/>
<area href="a00715.html" title="Controls if the calibration data configuration mode is active." alt="neoapi.CboCalibrationDataConfigurationMode" shape="rect" coords="320,952,630,976"/>
<area href="a00719.html" title="Selects the calibration matrix." alt="neoapi.CboCalibrationMatrixSelector" shape="rect" coords="320,1008,630,1032"/>
<area href="a00723.html" title="Value selector of calibration matrix." alt="neoapi.CboCalibrationMatrixValueSelector" shape="rect" coords="320,1064,630,1088"/>
<area href="a00727.html" title="Selects the calibration vector." alt="neoapi.CboCalibrationVectorSelector" shape="rect" coords="320,1120,630,1144"/>
<area href="a00731.html" title="Value selector of calibration vector." alt="neoapi.CboCalibrationVectorValueSelector" shape="rect" coords="320,1176,630,1200"/>
<area href="a00735.html" title="Value Selector of geometry distortion." alt="neoapi.CboGeometryDistortionValueSelector" shape="rect" coords="320,1232,630,1256"/>
<area href="a00119.html" title="Selects the shift factor for 8bit pixel format calculated from 12 bit mode." alt="neoapi.CBOPFShift" shape="rect" coords="320,1288,630,1312"/>
<area href="a00155.html" title="Enables the sequencer for special multi-frame mode." alt="neoapi.CBoSequencerEnable" shape="rect" coords="320,1344,630,1368"/>
<area href="a00159.html" title="Selects the Sequencers output lines." alt="neoapi.CBoSequencerIOSelector" shape="rect" coords="320,1400,630,1424"/>
<area href="a00163.html" title="Specifies the running mode of the sequencer." alt="neoapi.CBoSequencerMode" shape="rect" coords="320,1456,630,1480"/>
<area href="a00167.html" title="Sets the number of digitized samples outputted simultaneously by the camera A/D conversion stage for ..." alt="neoapi.CBoSequencerSensorDigitizationTaps" shape="rect" coords="320,1512,630,1536"/>
<area href="a00171.html" title="Starts or stopps the configured sequence." alt="neoapi.CBoSequencerStart" shape="rect" coords="320,1568,630,1592"/>
<area href="a00739.html" title="Serial interface clock frequency." alt="neoapi.CboSerialConfigBaudRate" shape="rect" coords="320,1624,630,1648"/>
<area href="a00743.html" title="Number of data bits." alt="neoapi.CboSerialConfigDataBits" shape="rect" coords="320,1680,630,1704"/>
<area href="a00747.html" title="Serial interface parity." alt="neoapi.CboSerialConfigParity" shape="rect" coords="320,1736,630,1760"/>
<area href="a00751.html" title="Number of stop bits." alt="neoapi.CboSerialConfigStopBits" shape="rect" coords="320,1792,630,1816"/>
<area href="a00755.html" title="States the interface mode of the serial interface." alt="neoapi.CboSerialMode" shape="rect" coords="320,1848,630,1872"/>
<area href="a00759.html" title="Selects which serial interface to configure." alt="neoapi.CboSerialSelector" shape="rect" coords="320,1904,630,1928"/>
<area href="a00175.html" title="Sets the highest priority auto feature to adjust the brightness." alt="neoapi.CBrightnessAutoPriority" shape="rect" coords="320,1960,630,1984"/>
<area href="a00179.html" title="Enables the Brightness Correction." alt="neoapi.CBrightnessCorrection" shape="rect" coords="320,2016,630,2040"/>
<area href="a00183.html" title="Selects the color calibration matrix." alt="neoapi.CCalibrationMatrixColorSelector" shape="rect" coords="320,2072,630,2096"/>
<area href="a00187.html" title="Selects the gain factor of the selected calibration matrix." alt="neoapi.CCalibrationMatrixValueSelector" shape="rect" coords="320,2128,630,2152"/>
<area href="a00191.html" title="Selects which Chunk to enable or control." alt="neoapi.CChunkSelector" shape="rect" coords="320,2184,630,2208"/>
<area href="a00195.html" title="This Camera Link specific feature describes the configuration used by the camera." alt="neoapi.CClConfiguration" shape="rect" coords="320,2240,630,2264"/>
<area href="a00199.html" title="This Camera Link specific feature describes the time multiplexing of the camera link connection to tr..." alt="neoapi.CClTimeSlotsCount" shape="rect" coords="320,2296,630,2320"/>
<area href="a00203.html" title="Controls the mode for automatic adjusting the gains of the active transformation matrix." alt="neoapi.CColorTransformationAuto" shape="rect" coords="320,2352,630,2376"/>
<area href="a00207.html" title="Selects the color transformation factory list tuned to the given color temeperature." alt="neoapi.CColorTransformationFactoryListSelector" shape="rect" coords="320,2408,630,2432"/>
<area href="a00211.html" title="Selects which Color Transformation module is controlled by the various Color Transformation features." alt="neoapi.CColorTransformationSelector" shape="rect" coords="320,2464,630,2488"/>
<area href="a00215.html" title="Selects the Gain factor or Offset of the Transformation matrix to access in the selected Color Transf..." alt="neoapi.CColorTransformationValueSelector" shape="rect" coords="320,2520,630,2544"/>
<area href="a00219.html" title="Selects a component to activate/deactivate its data streaming." alt="neoapi.CComponentSelector" shape="rect" coords="320,2576,630,2600"/>
<area href="a00223.html" title="Selects the Activation mode Event Source signal." alt="neoapi.CCounterEventActivation" shape="rect" coords="320,2632,630,2656"/>
<area href="a00227.html" title="Select the events that will be the source to increment the Counter." alt="neoapi.CCounterEventSource" shape="rect" coords="320,2688,630,2712"/>
<area href="a00231.html" title="Selects the Activation mode of the Counter Reset Source signal." alt="neoapi.CCounterResetActivation" shape="rect" coords="320,2744,630,2768"/>
<area href="a00235.html" title="Selects the signals that will be the source to reset the Counter." alt="neoapi.CCounterResetSource" shape="rect" coords="320,2800,630,2824"/>
<area href="a00239.html" title="Selects which Counter to configure." alt="neoapi.CCounterSelector" shape="rect" coords="320,2856,630,2880"/>
<area href="a00243.html" title="Controls if the custom data configuration mode is active." alt="neoapi.CCustomDataConfigurationMode" shape="rect" coords="320,2912,630,2936"/>
<area href="a00247.html" title="Sets the mode used to reduce the horizontal resolution when DecimationHorizontal is used." alt="neoapi.CDecimationHorizontalMode" shape="rect" coords="320,2968,630,2992"/>
<area href="a00251.html" title="Sets the mode used to reduce the Vertical resolution when DecimationVertical is used." alt="neoapi.CDecimationVerticalMode" shape="rect" coords="320,3024,630,3048"/>
<area href="a00255.html" title="Selects which Defect Pixel List to control." alt="neoapi.CDefectPixelListSelector" shape="rect" coords="320,3080,630,3104"/>
<area href="a00259.html" title="Character set used by the strings of the device." alt="neoapi.CDeviceCharacterSet" shape="rect" coords="320,3136,630,3160"/>
<area href="a00263.html" title="Selects the clock frequency to access from the device." alt="neoapi.CDeviceClockSelector" shape="rect" coords="320,3192,630,3216"/>
<area href="a00267.html" title="Source control for frontside UART interface." alt="neoapi.CDeviceFrontUARTSource" shape="rect" coords="320,3248,630,3272"/>
<area href="a00271.html" title="Returns if the license at the device is valid or not for the license type, selected by the DeviceLice..." alt="neoapi.CDeviceLicense" shape="rect" coords="320,3304,630,3328"/>
<area href="a00275.html" title="Selects the available License types." alt="neoapi.CDeviceLicenseTypeSelector" shape="rect" coords="320,3360,630,3384"/>
<area href="a00279.html" title="Activate or deactivate the Link&#39;s heartbeat." alt="neoapi.CDeviceLinkHeartbeatMode" shape="rect" coords="320,3416,630,3440"/>
<area href="a00283.html" title="Selects which Link of the device to control." alt="neoapi.CDeviceLinkSelector" shape="rect" coords="320,3472,630,3496"/>
<area href="a00287.html" title="Controls if the DeviceLinkThroughputLimit is active." alt="neoapi.CDeviceLinkThroughputLimitMode" shape="rect" coords="320,3528,630,3552"/>
<area href="a00291.html" title="Endianness of the registers of the device." alt="neoapi.CDeviceRegistersEndianness" shape="rect" coords="320,3584,630,3608"/>
<area href="a00295.html" title="Scan type of the sensor of the device." alt="neoapi.CDeviceScanType" shape="rect" coords="320,3640,630,3664"/>
<area href="a00299.html" title="Selects which sensor is controlled by the various sensor specific features." alt="neoapi.CDeviceSensorSelector" shape="rect" coords="320,3696,630,3720"/>
<area href="a00303.html" title="Specifies the type of the sensor." alt="neoapi.CDeviceSensorType" shape="rect" coords="320,3752,630,3776"/>
<area href="a00307.html" title="Specifies the version of the CMOSIS sensor." alt="neoapi.CDeviceSensorVersion" shape="rect" coords="320,3808,630,3832"/>
<area href="a00311.html" title="This feature controls the baud rate used by the selected serial port." alt="neoapi.CDeviceSerialPortBaudRate" shape="rect" coords="320,3864,630,3888"/>
<area href="a00315.html" title="Selects which serial port of the device to control." alt="neoapi.CDeviceSerialPortSelector" shape="rect" coords="320,3920,630,3944"/>
<area href="a00319.html" title="Endianness of multi-byte pixel data for this stream." alt="neoapi.CDeviceStreamChannelEndianness" shape="rect" coords="320,3976,630,4000"/>
<area href="a00323.html" title="Reports the type of the stream channel." alt="neoapi.CDeviceStreamChannelType" shape="rect" coords="320,4032,630,4056"/>
<area href="a00331.html" title="This device tap geometry feature describes the geometrical properties characterizing the taps of a ca..." alt="neoapi.CDeviceTapGeometry" shape="rect" coords="320,4088,630,4112"/>
<area href="a00335.html" title="Selects the location within the device, where the temperature will be measured." alt="neoapi.CDeviceTemperatureSelector" shape="rect" coords="320,4144,630,4168"/>
<area href="a00339.html" title="Returns the current temperature status of the device." alt="neoapi.CDeviceTemperatureStatus" shape="rect" coords="320,4200,630,4224"/>
<area href="a00343.html" title="Selects which temperature transition is controlled by the feature DeviceTemperatureStatusTransition." alt="neoapi.CDeviceTemperatureStatusTransitionSelector" shape="rect" coords="320,4256,630,4280"/>
<area href="a00327.html" title="Transport Layer type of the device." alt="neoapi.CDeviceTLType" shape="rect" coords="320,4312,630,4336"/>
<area href="a00347.html" title="Returns the device type." alt="neoapi.CDeviceType" shape="rect" coords="320,4368,630,4392"/>
<area href="a00351.html" title="Activate or deactivate the notification to the host application of the occurrence of the selected Eve..." alt="neoapi.CEventNotification" shape="rect" coords="320,4424,630,4448"/>
<area href="a00355.html" title="Selects which Event to signal to the host application." alt="neoapi.CEventSelector" shape="rect" coords="320,4480,630,4504"/>
<area href="a00359.html" title="Sets the automatic exposure mode when ExposureMode is Timed." alt="neoapi.CExposureAuto" shape="rect" coords="320,4536,630,4560"/>
<area href="a00363.html" title="Sets the operation mode of the Exposure." alt="neoapi.CExposureMode" shape="rect" coords="320,4592,630,4616"/>
<area href="a00367.html" title="Selects the access mode in which a file is opened in the device." alt="neoapi.CFileOpenMode" shape="rect" coords="320,4648,630,4672"/>
<area href="a00371.html" title="Selects the target operation for the selected file in the device." alt="neoapi.CFileOperationSelector" shape="rect" coords="320,4704,630,4728"/>
<area href="a00375.html" title="Represents the file operation execution status." alt="neoapi.CFileOperationStatus" shape="rect" coords="320,4760,630,4784"/>
<area href="a00379.html" title="Selects the target file in the device." alt="neoapi.CFileSelector" shape="rect" coords="320,4816,630,4840"/>
<area href="a00383.html" title="Reads the status of the focal length." alt="neoapi.CFocalLengthStatus" shape="rect" coords="320,4872,630,4896"/>
<area href="a00387.html" title="Reads the status of the focus." alt="neoapi.CFocusStatus" shape="rect" coords="320,4928,630,4952"/>
<area href="a00391.html" title="Sets the automatic gain control (AGC) mode." alt="neoapi.CGainAuto" shape="rect" coords="320,4984,630,5008"/>
<area href="a00395.html" title="Selects which Gain is controlled by the various Gain features." alt="neoapi.CGainSelector" shape="rect" coords="320,5040,630,5064"/>
<area href="a00399.html" title="Controls the device&#39;s streaming format." alt="neoapi.CGenDCStreamingMode" shape="rect" coords="320,5096,630,5120"/>
<area href="a00403.html" title="Returns whether the current device data streaming format is GenDC." alt="neoapi.CGenDCStreamingStatus" shape="rect" coords="320,5152,630,5176"/>
<area href="a00407.html" title="Controls the device access privilege of an application." alt="neoapi.CGevCCP" shape="rect" coords="320,5208,630,5232"/>
<area href="a00411.html" title="Selects the GigE Vision version to control extended status codes for." alt="neoapi.CGevGVCPExtendedStatusCodesSelector" shape="rect" coords="320,5264,630,5288"/>
<area href="a00415.html" title="Reports the current IP configuration status." alt="neoapi.CGevIPConfigurationStatus" shape="rect" coords="320,5320,630,5344"/>
<area href="a00419.html" title="Selects the GEV option to interrogate for existing support." alt="neoapi.CGevSupportedOptionSelector" shape="rect" coords="320,5376,630,5400"/>
<area href="a00423.html" title="Selects the gain ratio for HDR mode." alt="neoapi.CHDRGainRatioSelector" shape="rect" coords="320,5432,630,5456"/>
<area href="a00427.html" title="Selects the predefined transfer curve for global tone-mapping of the calculated HDR image." alt="neoapi.CHDRTonemappingCurvePresetSelector" shape="rect" coords="320,5488,630,5512"/>
<area href="a00431.html" title="When JPEG is selected as the compression format, a device might optionally offer better control over ..." alt="neoapi.CImageCompressionJPEGFormatOption" shape="rect" coords="320,5544,630,5568"/>
<area href="a00435.html" title="Enable a specific image compression mode as the base mode for image transfer." alt="neoapi.CImageCompressionMode" shape="rect" coords="320,5600,630,5624"/>
<area href="a00439.html" title="Two rate controlling options are offered: fixed bit rate or fixed quality." alt="neoapi.CImageCompressionRateOption" shape="rect" coords="320,5656,630,5680"/>
<area href="a00443.html" title="Returns the interface speed mode as string." alt="neoapi.CInterfaceSpeedMode" shape="rect" coords="320,5712,630,5736"/>
<area href="a00455.html" title="Controls the current electrical format of the selected physical input or output Line." alt="neoapi.CLineFormat" shape="rect" coords="320,5768,630,5792"/>
<area href="a00459.html" title="Controls if the physical Line is used to Input or Output a signal." alt="neoapi.CLineMode" shape="rect" coords="320,5824,630,5848"/>
<area href="a00463.html" title="Enables the line PWM configuration mode." alt="neoapi.CLinePWMConfigurationMode" shape="rect" coords="320,5880,630,5904"/>
<area href="a00467.html" title="Enables the line PWM configuration mode." alt="neoapi.CLinePWMMode" shape="rect" coords="320,5936,630,5960"/>
<area href="a00471.html" title="Selects the physical line (or pin) of the external device connector or the virtual line of the Transp..." alt="neoapi.CLineSelector" shape="rect" coords="320,5992,630,6016"/>
<area href="a00475.html" title="Selects which internal acquisition or I/O source signal to output on the selected Line." alt="neoapi.CLineSource" shape="rect" coords="320,6048,630,6072"/>
<area href="a00447.html" title="Specifies the content of the selected LUT." alt="neoapi.CLUTContent" shape="rect" coords="320,6104,630,6128"/>
<area href="a00451.html" title="Selects which LUT to control." alt="neoapi.CLUTSelector" shape="rect" coords="320,6160,630,6184"/>
<area href="a00479.html" title="Returns the active memory part to write the images in." alt="neoapi.CMemoryActivePart" shape="rect" coords="320,6216,630,6240"/>
<area href="a00483.html" title="Controls the mode to use the memory." alt="neoapi.CMemoryMode" shape="rect" coords="320,6272,630,6296"/>
<area href="a00487.html" title="Selects the source to switch the active memory part." alt="neoapi.CMemoryPartIncrementSource" shape="rect" coords="320,6328,630,6352"/>
<area href="a00491.html" title="Selects the mode to use for the selected memory part." alt="neoapi.CMemoryPartMode" shape="rect" coords="320,6384,630,6408"/>
<area href="a00495.html" title="Selects on of the available memory parts." alt="neoapi.CMemoryPartSelector" shape="rect" coords="320,6440,630,6464"/>
<area href="a00499.html" title="Selects which optic controller to configure." alt="neoapi.COpticControllerSelector" shape="rect" coords="320,6496,630,6520"/>
<area href="a00503.html" title="Reads the status of the optic controller." alt="neoapi.COpticControllerStatus" shape="rect" coords="320,6552,630,6576"/>
<area href="a00507.html" title="Enables the partial scan readout." alt="neoapi.CPartialScanEnabled" shape="rect" coords="320,6608,630,6632"/>
<area href="a00511.html" title="Format of the pixels provided by the device." alt="neoapi.CPixelFormat" shape="rect" coords="320,6664,630,6688"/>
<area href="a00515.html" title="Indicates the expected accuracy of the device PTP clock when it is the grandmaster,..." alt="neoapi.CPtpClockAccuracy" shape="rect" coords="320,6720,630,6744"/>
<area href="a00519.html" title="Sets the mode to handle PtpClockOffset for command PtpClockOffsetSet." alt="neoapi.CPtpClockOffsetMode" shape="rect" coords="320,6776,630,6800"/>
<area href="a00523.html" title="Sets the mode to handle PtpDriftOffset for command PtpDriftOffsetSet." alt="neoapi.CPtpDriftOffsetMode" shape="rect" coords="320,6832,630,6856"/>
<area href="a00527.html" title="Selects the PTP clock type the device will act as." alt="neoapi.CPtpMode" shape="rect" coords="320,6888,630,6912"/>
<area href="a00531.html" title="Returns the latched state of the clock servo." alt="neoapi.CPtpServoStatus" shape="rect" coords="320,6944,630,6968"/>
<area href="a00535.html" title="Returns the latched state of the PTP clock." alt="neoapi.CPtpStatus" shape="rect" coords="320,7000,630,7024"/>
<area href="a00539.html" title="Returns if the latched sync message interval from the PTP master clock is supported by the device." alt="neoapi.CPtpSyncMessageIntervalStatus" shape="rect" coords="320,7056,630,7080"/>
<area href="a00543.html" title="Sets the mode to handle PtpTimestampOffset for command PtpTimestampOffsetSet." alt="neoapi.CPtpTimestampOffsetMode" shape="rect" coords="320,7112,630,7136"/>
<area href="a00547.html" title="Selects the number of image buffers filled with data of sensor output." alt="neoapi.CReadOutBuffering" shape="rect" coords="320,7168,630,7192"/>
<area href="a00551.html" title="Specifies the operation mode of the readout for the acquisition." alt="neoapi.CReadoutMode" shape="rect" coords="320,7224,630,7248"/>
<area href="a00555.html" title="Returns the acquisition mode of the regions." alt="neoapi.CRegionAcquisitionMode" shape="rect" coords="320,7280,630,7304"/>
<area href="a00559.html" title="Returns the configuration mode of the regions." alt="neoapi.CRegionConfigurationMode" shape="rect" coords="320,7336,630,7360"/>
<area href="a00563.html" title="Controls if the selected Region of interest is active and streaming." alt="neoapi.CRegionMode" shape="rect" coords="320,7392,630,7416"/>
<area href="a00567.html" title="Selects the Region of interest to control." alt="neoapi.CRegionSelector" shape="rect" coords="320,7448,630,7472"/>
<area href="a00571.html" title="Returns the transfer mode of the regions." alt="neoapi.CRegionTransferMode" shape="rect" coords="320,7504,630,7528"/>
<area href="a00579.html" title="Controls the sensors AD digitization in bits per pixels." alt="neoapi.CSensorADDigitization" shape="rect" coords="320,7560,630,7584"/>
<area href="a00583.html" title="Controls if the sensor adjustment configuration mode is active." alt="neoapi.CSensorCutConfigurationMode" shape="rect" coords="320,7616,630,7640"/>
<area href="a00587.html" title="Number of digitized samples outputted simultaneously by the camera A/D conversion stage." alt="neoapi.CSensorDigitizationTaps" shape="rect" coords="320,7672,630,7696"/>
<area href="a00591.html" title="Specifies the shutter mode of the device." alt="neoapi.CSensorShutterMode" shape="rect" coords="320,7728,630,7752"/>
<area href="a00595.html" title="Number of taps of the camera sensor." alt="neoapi.CSensorTaps" shape="rect" coords="320,7784,630,7808"/>
<area href="a00599.html" title="Controls if the sequencer configuration mode is active." alt="neoapi.CSequencerConfigurationMode" shape="rect" coords="320,7840,630,7864"/>
<area href="a00603.html" title="Selects which sequencer features to control." alt="neoapi.CSequencerFeatureSelector" shape="rect" coords="320,7896,630,7920"/>
<area href="a00607.html" title="Controls if the sequencer mechanism is active." alt="neoapi.CSequencerMode" shape="rect" coords="320,7952,630,7976"/>
<area href="a00611.html" title="Specifies the activation mode of the sequencer trigger." alt="neoapi.CSequencerTriggerActivation" shape="rect" coords="320,8008,630,8032"/>
<area href="a00615.html" title="Specifies the internal signal or physical input line to use as the sequencer trigger source." alt="neoapi.CSequencerTriggerSource" shape="rect" coords="320,8064,630,8088"/>
<area href="a00619.html" title="Selects the Shading Port Address." alt="neoapi.CShadingSelector" shape="rect" coords="320,8120,630,8144"/>
<area href="a00623.html" title="Selects the Sharpening Mode." alt="neoapi.CSharpeningMode" shape="rect" coords="320,8176,630,8200"/>
<area href="a00575.html" title="Controls the streaming operation." alt="neoapi.CSIControl" shape="rect" coords="320,8232,630,8256"/>
<area href="a00627.html" title="Returns a unique Identifier value that correspond to the selected Source." alt="neoapi.CSourceID" shape="rect" coords="320,8288,630,8312"/>
<area href="a00631.html" title="Selects the source to control." alt="neoapi.CSourceSelector" shape="rect" coords="320,8344,630,8368"/>
<area href="a00635.html" title="Selects the port for the port related features." alt="neoapi.CSwitchPortSelector" shape="rect" coords="320,8400,630,8424"/>
<area href="a00639.html" title="Selects the type of test pattern that is generated by the device as image source." alt="neoapi.CTestPattern" shape="rect" coords="320,8456,630,8480"/>
<area href="a00643.html" title="Selects which test pattern generator is controlled by the TestPattern feature." alt="neoapi.CTestPatternGeneratorSelector" shape="rect" coords="320,8512,630,8536"/>
<area href="a00647.html" title="This feature allows setting a device in test mode and to output a specific payload format for validat..." alt="neoapi.CTestPayloadFormatMode" shape="rect" coords="320,8568,630,8592"/>
<area href="a00651.html" title="Selects which Timer to configure." alt="neoapi.CTimerSelector" shape="rect" coords="320,8624,630,8648"/>
<area href="a00655.html" title="Selects the activation mode of the trigger to start the Timer." alt="neoapi.CTimerTriggerActivation" shape="rect" coords="320,8680,630,8704"/>
<area href="a00659.html" title="Selects the source of the trigger to start the Timer." alt="neoapi.CTimerTriggerSource" shape="rect" coords="320,8736,630,8760"/>
<area href="a00663.html" title="Selects the control method for the transfers." alt="neoapi.CTransferControlMode" shape="rect" coords="320,8792,630,8816"/>
<area href="a00667.html" title="Selects the operation mode of the transfer." alt="neoapi.CTransferOperationMode" shape="rect" coords="320,8848,630,8872"/>
<area href="a00671.html" title="Selects which stream transfers are currently controlled by the selected Transfer features." alt="neoapi.CTransferSelector" shape="rect" coords="320,8904,630,8928"/>
<area href="a00675.html" title="Selects which status of the transfer module to read." alt="neoapi.CTransferStatusSelector" shape="rect" coords="320,8960,630,8984"/>
<area href="a00679.html" title="Specifies the activation mode of the trigger." alt="neoapi.CTriggerActivation" shape="rect" coords="320,9016,630,9040"/>
<area href="a00683.html" title="Controls if the selected trigger is active." alt="neoapi.CTriggerMode" shape="rect" coords="320,9072,630,9096"/>
<area href="a00687.html" title="Specifies the type trigger overlap permitted with the previous frame or line." alt="neoapi.CTriggerOverlap" shape="rect" coords="320,9128,630,9152"/>
<area href="a00691.html" title="Selects the type of trigger to configure." alt="neoapi.CTriggerSelector" shape="rect" coords="320,9184,630,9208"/>
<area href="a00695.html" title="Specifies the internal signal or physical input Line to use as the trigger source." alt="neoapi.CTriggerSource" shape="rect" coords="320,9240,630,9264"/>
<area href="a00699.html" title="Selects which bit of the User Output register will be set by UserOutputValue." alt="neoapi.CUserOutputSelector" shape="rect" coords="320,9296,630,9320"/>
<area href="a00703.html" title="Selects the feature User Set to load and make active by default when the device is reset." alt="neoapi.CUserSetDefault" shape="rect" coords="320,9352,630,9376"/>
<area href="a00707.html" title="Selects which individual UserSet feature to control." alt="neoapi.CUserSetFeatureSelector" shape="rect" coords="320,9408,630,9432"/>
<area href="a00711.html" title="Selects the feature User Set to load, save or configure." alt="neoapi.CUserSetSelector" shape="rect" coords="320,9464,630,9488"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a4a787ada7168db2610fec341f811a6fc"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a4a787ada7168db2610fec341f811a6fc">__init__</a> (self, *args)</td></tr>
<tr class="separator:a4a787ada7168db2610fec341f811a6fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb4392642486f932de046b64d6114df6"><td class="memItemLeft" align="right" valign="top">&quot;FeatureList&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#aeb4392642486f932de046b64d6114df6">GetEnumValueList</a> (self)</td></tr>
<tr class="memdesc:aeb4392642486f932de046b64d6114df6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a list of all possible values of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object Only valid for interface type 'IEnumeration'.  <a href="#aeb4392642486f932de046b64d6114df6">More...</a><br /></td></tr>
<tr class="separator:aeb4392642486f932de046b64d6114df6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a452fdbc062b37f3915e0bcfb0aebf3b0"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a452fdbc062b37f3915e0bcfb0aebf3b0">GetInt</a> (self)</td></tr>
<tr class="memdesc:a452fdbc062b37f3915e0bcfb0aebf3b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object as integer.  <a href="#a452fdbc062b37f3915e0bcfb0aebf3b0">More...</a><br /></td></tr>
<tr class="separator:a452fdbc062b37f3915e0bcfb0aebf3b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bcb8c3b80260c8d0cde108eaf365be4"><td class="memItemLeft" align="right" valign="top">&quot;EnumerationFeature&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a0bcb8c3b80260c8d0cde108eaf365be4">SetInt</a> (self, &quot;int&quot; value)</td></tr>
<tr class="memdesc:a0bcb8c3b80260c8d0cde108eaf365be4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Writes an integer value to the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#a0bcb8c3b80260c8d0cde108eaf365be4">More...</a><br /></td></tr>
<tr class="separator:a0bcb8c3b80260c8d0cde108eaf365be4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4a3e7aba4519e49f8f32afe4ba29869"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#ac4a3e7aba4519e49f8f32afe4ba29869">GetString</a> (self)</td></tr>
<tr class="memdesc:ac4a3e7aba4519e49f8f32afe4ba29869"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> as a string.  <a href="#ac4a3e7aba4519e49f8f32afe4ba29869">More...</a><br /></td></tr>
<tr class="separator:ac4a3e7aba4519e49f8f32afe4ba29869"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82a850e2d931254434f9a8e454fcb536"><td class="memItemLeft" align="right" valign="top">&quot;EnumerationFeature&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a82a850e2d931254434f9a8e454fcb536">SetString</a> (self, &quot;str&quot; value)</td></tr>
<tr class="memdesc:a82a850e2d931254434f9a8e454fcb536"><td class="mdescLeft">&#160;</td><td class="mdescRight">Write the value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> as a string.  <a href="#a82a850e2d931254434f9a8e454fcb536">More...</a><br /></td></tr>
<tr class="separator:a82a850e2d931254434f9a8e454fcb536"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab78a7830a2eeff9e0c82484d8f538962"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#ab78a7830a2eeff9e0c82484d8f538962">IsSelector</a> (self)</td></tr>
<tr class="memdesc:ab78a7830a2eeff9e0c82484d8f538962"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicates whether the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is a selector A selector is a possibility to define feature dependencies.  <a href="#ab78a7830a2eeff9e0c82484d8f538962">More...</a><br /></td></tr>
<tr class="separator:ab78a7830a2eeff9e0c82484d8f538962"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2302f54f1139c38b3de67ae9b8606afb"><td class="memItemLeft" align="right" valign="top">&quot;FeatureList&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a2302f54f1139c38b3de67ae9b8606afb">GetSelectedFeatureList</a> (self)</td></tr>
<tr class="memdesc:a2302f54f1139c38b3de67ae9b8606afb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a list of features that depend on this selector feature.  <a href="#a2302f54f1139c38b3de67ae9b8606afb">More...</a><br /></td></tr>
<tr class="separator:a2302f54f1139c38b3de67ae9b8606afb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affff6bd927a38fc199f92c32fd031490"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#affff6bd927a38fc199f92c32fd031490">GetInterface</a> (self)</td></tr>
<tr class="memdesc:affff6bd927a38fc199f92c32fd031490"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the GenICam interface type of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object Depending on the GenICam interface type, different feature access methods are provided.  <a href="#affff6bd927a38fc199f92c32fd031490">More...</a><br /></td></tr>
<tr class="separator:affff6bd927a38fc199f92c32fd031490"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1aa8c9d453a386b2f9f91cd3b3615c3"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#ad1aa8c9d453a386b2f9f91cd3b3615c3">GetToolTip</a> (self)</td></tr>
<tr class="memdesc:ad1aa8c9d453a386b2f9f91cd3b3615c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a short description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#ad1aa8c9d453a386b2f9f91cd3b3615c3">More...</a><br /></td></tr>
<tr class="separator:ad1aa8c9d453a386b2f9f91cd3b3615c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6830b31068e48ae5db294d32f2def11c"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#a6830b31068e48ae5db294d32f2def11c">GetDescription</a> (self)</td></tr>
<tr class="memdesc:a6830b31068e48ae5db294d32f2def11c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#a6830b31068e48ae5db294d32f2def11c">More...</a><br /></td></tr>
<tr class="separator:a6830b31068e48ae5db294d32f2def11c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae28b09dddd7447d487a04a8258590633"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#ae28b09dddd7447d487a04a8258590633">GetName</a> (self)</td></tr>
<tr class="memdesc:ae28b09dddd7447d487a04a8258590633"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#ae28b09dddd7447d487a04a8258590633">More...</a><br /></td></tr>
<tr class="separator:ae28b09dddd7447d487a04a8258590633"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa79f26165f8083861522250888e0cee3"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#aa79f26165f8083861522250888e0cee3">GetDisplayName</a> (self)</td></tr>
<tr class="memdesc:aa79f26165f8083861522250888e0cee3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the display name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#aa79f26165f8083861522250888e0cee3">More...</a><br /></td></tr>
<tr class="separator:aa79f26165f8083861522250888e0cee3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4bacabd7d5e68d9e54e88314660e2e9a"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#a4bacabd7d5e68d9e54e88314660e2e9a">GetVisibility</a> (self)</td></tr>
<tr class="memdesc:a4bacabd7d5e68d9e54e88314660e2e9a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the recommended visibility (Beginner/Expert/Guru or Invisible) of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#a4bacabd7d5e68d9e54e88314660e2e9a">More...</a><br /></td></tr>
<tr class="separator:a4bacabd7d5e68d9e54e88314660e2e9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04f73633d9a8b93b7865bf91d2d36b67"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#a04f73633d9a8b93b7865bf91d2d36b67">IsReadable</a> (self)</td></tr>
<tr class="memdesc:a04f73633d9a8b93b7865bf91d2d36b67"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicates that the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is readable.  <a href="#a04f73633d9a8b93b7865bf91d2d36b67">More...</a><br /></td></tr>
<tr class="separator:a04f73633d9a8b93b7865bf91d2d36b67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac1faf49fc350a92ddf2ce11d35ba85c"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#aac1faf49fc350a92ddf2ce11d35ba85c">IsWritable</a> (self)</td></tr>
<tr class="memdesc:aac1faf49fc350a92ddf2ce11d35ba85c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicates if a <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is writeable.  <a href="#aac1faf49fc350a92ddf2ce11d35ba85c">More...</a><br /></td></tr>
<tr class="separator:aac1faf49fc350a92ddf2ce11d35ba85c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada3c4ad870b561c575714a16f70d19db"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#ada3c4ad870b561c575714a16f70d19db">IsAvailable</a> (self)</td></tr>
<tr class="memdesc:ada3c4ad870b561c575714a16f70d19db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicates whether the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is available "Not available" or False is equivalent to the access mode 'NA'.  <a href="#ada3c4ad870b561c575714a16f70d19db">More...</a><br /></td></tr>
<tr class="separator:ada3c4ad870b561c575714a16f70d19db"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a8c0a659fcc6f0de09b7d42530b8aaad8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a8c0a659fcc6f0de09b7d42530b8aaad8">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:a8c0a659fcc6f0de09b7d42530b8aaad8"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#a8c0a659fcc6f0de09b7d42530b8aaad8">More...</a><br /></td></tr>
<tr class="separator:a8c0a659fcc6f0de09b7d42530b8aaad8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Base class providing the 'IEnumeration' interface. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a4a787ada7168db2610fec341f811a6fc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4a787ada7168db2610fec341f811a6fc">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.EnumerationFeature.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">   \brief      Constructor
</pre> <dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>object</b> (optional) An <a class="el" href="a00847.html" title="Base class providing the &#39;IEnumeration&#39; interface.">EnumerationFeature</a> object </td></tr>
  </table>
  </dd>
</dl>

<p>Reimplemented from <a class="el" href="a00823.html#ad57036684dcf73a4522484aafbb2b1ca">neoapi.BaseFeature</a>.</p>

<p>Reimplemented in <a class="el" href="a00759.html#a3adaeef1d3b314e4d5ceb8ca811aa260">neoapi.CboSerialSelector</a>, <a class="el" href="a00755.html#a03131f676c5ff9fab7d3b33eb3c829d1">neoapi.CboSerialMode</a>, <a class="el" href="a00751.html#a8a8a7d78e95b35367564f4d6c8a22778">neoapi.CboSerialConfigStopBits</a>, <a class="el" href="a00747.html#a803a1232646998f28873796c4ffa9f61">neoapi.CboSerialConfigParity</a>, <a class="el" href="a00743.html#a4105952f0e10c274a92f5265de179acd">neoapi.CboSerialConfigDataBits</a>, <a class="el" href="a00739.html#a35fcf482c5418397dd9c60aeee2fdf03">neoapi.CboSerialConfigBaudRate</a>, <a class="el" href="a00735.html#a0faddd2074a1f1b7618e89b2a36acbeb">neoapi.CboGeometryDistortionValueSelector</a>, <a class="el" href="a00731.html#a9632f28c2652cbb9f9b3f3a9d5cbc25b">neoapi.CboCalibrationVectorValueSelector</a>, <a class="el" href="a00727.html#a09dde983f376a8e6b1bca60e643a2f3e">neoapi.CboCalibrationVectorSelector</a>, <a class="el" href="a00723.html#a77d77c0344c6aa379545f8797a09ef6d">neoapi.CboCalibrationMatrixValueSelector</a>, <a class="el" href="a00719.html#ab9a2ccdda9dfb481f77f7b34e91ea392">neoapi.CboCalibrationMatrixSelector</a>, <a class="el" href="a00715.html#a149022cc5dd7c8325556b462718fd193">neoapi.CboCalibrationDataConfigurationMode</a>, <a class="el" href="a00711.html#a128a071e3ad5aa6047c21b981c856b99">neoapi.CUserSetSelector</a>, <a class="el" href="a00707.html#a037007f257c625f49848746e9194dc19">neoapi.CUserSetFeatureSelector</a>, <a class="el" href="a00703.html#a1a0917c91b6628a399517e813e1b9f5f">neoapi.CUserSetDefault</a>, <a class="el" href="a00699.html#a8023b16319a03fac32f1fa8b4587bfff">neoapi.CUserOutputSelector</a>, <a class="el" href="a00695.html#af8a2810e7bad70a37241ef52cade01b6">neoapi.CTriggerSource</a>, <a class="el" href="a00691.html#a123327342e3829cb0eeb0ea8bcec4ac7">neoapi.CTriggerSelector</a>, <a class="el" href="a00687.html#adac46bb53d0b232f582f17989d8a9888">neoapi.CTriggerOverlap</a>, <a class="el" href="a00683.html#a95826701d5dceec92870dabb38f800c3">neoapi.CTriggerMode</a>, <a class="el" href="a00679.html#a5e2f3e301de8f8a2d95420ace88be463">neoapi.CTriggerActivation</a>, <a class="el" href="a00675.html#a91e400f47a31ffe16b5c3dd332929ca4">neoapi.CTransferStatusSelector</a>, <a class="el" href="a00671.html#a6c4983c9a945767b69018d2f792f7fc0">neoapi.CTransferSelector</a>, <a class="el" href="a00667.html#abd97ebbdb0f2e36035f5bcc2aa960b27">neoapi.CTransferOperationMode</a>, <a class="el" href="a00663.html#a367d264052829eed2a9e01a54873a500">neoapi.CTransferControlMode</a>, <a class="el" href="a00659.html#ac2378bf4ee2c800a73d1bc3155ec1ec5">neoapi.CTimerTriggerSource</a>, <a class="el" href="a00655.html#a8b10e0687ceea1b9dedca65f33b6335c">neoapi.CTimerTriggerActivation</a>, <a class="el" href="a00651.html#a10d55e378b97d1d18433b1f42b323bf6">neoapi.CTimerSelector</a>, <a class="el" href="a00647.html#a7c4bc0a68345f26feefa31a010827439">neoapi.CTestPayloadFormatMode</a>, <a class="el" href="a00643.html#ae9a29d17b099a4ea9e09ac98626b34b6">neoapi.CTestPatternGeneratorSelector</a>, <a class="el" href="a00639.html#a833f8fa47aedb9aae710650210b14b8c">neoapi.CTestPattern</a>, <a class="el" href="a00635.html#a902f1ec0182347be5faf905fa6bb79c4">neoapi.CSwitchPortSelector</a>, <a class="el" href="a00631.html#a96e1eef72e91b4fb12b11b5fb63c6fda">neoapi.CSourceSelector</a>, <a class="el" href="a00627.html#adf1734942f4f6ceb3097930fcd1e246d">neoapi.CSourceID</a>, <a class="el" href="a00623.html#a72ba9c783363c6a256d34a8c83cb3c09">neoapi.CSharpeningMode</a>, <a class="el" href="a00619.html#a297ec723f67fc739ce13c38712557f2c">neoapi.CShadingSelector</a>, <a class="el" href="a00615.html#a4bd9efd05383d45cc7d09f853bd4c210">neoapi.CSequencerTriggerSource</a>, <a class="el" href="a00611.html#a07591c18def2aeb9f08b4d7a38c0066c">neoapi.CSequencerTriggerActivation</a>, <a class="el" href="a00607.html#a37ec63fddfdd20a0f3c1bfd11d8b8d0d">neoapi.CSequencerMode</a>, <a class="el" href="a00603.html#afb45dc533e64413ec61e342043b2d45c">neoapi.CSequencerFeatureSelector</a>, <a class="el" href="a00599.html#a64bd28c115b2cc23329fe11f254a290a">neoapi.CSequencerConfigurationMode</a>, <a class="el" href="a00595.html#aad034bdd1a93c39d456b407fc362513a">neoapi.CSensorTaps</a>, <a class="el" href="a00591.html#a16c19e4364881ae0c7c3f5c9f4ea5185">neoapi.CSensorShutterMode</a>, <a class="el" href="a00587.html#a39ef79b40ca9fc4c19a0f1ff7963c5f4">neoapi.CSensorDigitizationTaps</a>, <a class="el" href="a00583.html#a0b4a1cbbdb4938b7563edc7a95c0012b">neoapi.CSensorCutConfigurationMode</a>, <a class="el" href="a00579.html#af6d072cc7e440bdb663c54d778bfdb3c">neoapi.CSensorADDigitization</a>, <a class="el" href="a00575.html#ac2f0731e88e6ef6dba44313a93f62d4b">neoapi.CSIControl</a>, <a class="el" href="a00571.html#a2b05cb3df12bd4be13ae84aad81f45f2">neoapi.CRegionTransferMode</a>, <a class="el" href="a00567.html#af079d738cad7a1ff59aabbea1d02174e">neoapi.CRegionSelector</a>, <a class="el" href="a00563.html#a776e00f4c2bf37ff27206c0edc70ef24">neoapi.CRegionMode</a>, <a class="el" href="a00559.html#a00d91c382d46e5730d21a0f2859de921">neoapi.CRegionConfigurationMode</a>, <a class="el" href="a00555.html#a0ee3d0afca69bc5eed005337c7b6c72a">neoapi.CRegionAcquisitionMode</a>, <a class="el" href="a00551.html#ad8be05a7e82a3350900a5a7343b010db">neoapi.CReadoutMode</a>, <a class="el" href="a00547.html#a0c36cf5a9aed77587dee6a862ab776ff">neoapi.CReadOutBuffering</a>, <a class="el" href="a00543.html#ad3d617e30ee5e9969ef20c1b2c65ff52">neoapi.CPtpTimestampOffsetMode</a>, <a class="el" href="a00539.html#a776532b837a28f51755f573a1671468d">neoapi.CPtpSyncMessageIntervalStatus</a>, <a class="el" href="a00535.html#a69fab565e61449e5af1c7d0f9801c6cf">neoapi.CPtpStatus</a>, <a class="el" href="a00531.html#a64bf9b378c928c7c4eb1ba0448e57d8a">neoapi.CPtpServoStatus</a>, <a class="el" href="a00527.html#a6c92631e9942b8062468a851fa1c3701">neoapi.CPtpMode</a>, <a class="el" href="a00523.html#a0ca81180b140a53e47279f266ab539a1">neoapi.CPtpDriftOffsetMode</a>, <a class="el" href="a00519.html#adb9a32d60bd1984f8023bd3a8f3e7f0a">neoapi.CPtpClockOffsetMode</a>, <a class="el" href="a00515.html#abe5bf732fce1895c6000af080b8b4130">neoapi.CPtpClockAccuracy</a>, <a class="el" href="a00511.html#a6640e59f393dfe78a23a0bef3e515109">neoapi.CPixelFormat</a>, <a class="el" href="a00507.html#a394cd4e88f29c5eeba325dec8d3472f6">neoapi.CPartialScanEnabled</a>, <a class="el" href="a00503.html#a1078058278270cb4e901cc3b718cbf86">neoapi.COpticControllerStatus</a>, <a class="el" href="a00499.html#a369517068fa3cc56d550e8913fa1c290">neoapi.COpticControllerSelector</a>, <a class="el" href="a00495.html#afed98cc33d8e33abbc298cb211b51b25">neoapi.CMemoryPartSelector</a>, <a class="el" href="a00491.html#aeeedf440baa3a4f90ef5b37a051ed95f">neoapi.CMemoryPartMode</a>, <a class="el" href="a00487.html#a5564914e48a7bf8e448f1f7ba7540e47">neoapi.CMemoryPartIncrementSource</a>, <a class="el" href="a00483.html#a2309d027b92034e9b7e33310479f25e9">neoapi.CMemoryMode</a>, <a class="el" href="a00479.html#a99c893cddc551abc713e83f4bdeb9611">neoapi.CMemoryActivePart</a>, <a class="el" href="a00475.html#a8026d49c52483344a7796169339a691d">neoapi.CLineSource</a>, <a class="el" href="a00471.html#a8f08e89d3a52e1677c0891edb987cdfc">neoapi.CLineSelector</a>, <a class="el" href="a00467.html#abea2f93ec5e10f1949aa02208abdc41c">neoapi.CLinePWMMode</a>, <a class="el" href="a00463.html#a16ada2431d908cfe7c3cef7bf865bf50">neoapi.CLinePWMConfigurationMode</a>, <a class="el" href="a00459.html#ac5573a68618a927662ef8e0fd66a9241">neoapi.CLineMode</a>, <a class="el" href="a00455.html#a39ab238c75b29e8c84e3fec72b541514">neoapi.CLineFormat</a>, <a class="el" href="a00451.html#ae1d9b0d7e0406396a33b665e60635052">neoapi.CLUTSelector</a>, <a class="el" href="a00447.html#aef009c6d82e276692469032b74bbe0d6">neoapi.CLUTContent</a>, <a class="el" href="a00443.html#a7c1b85b78b40ca967f448cd79036128b">neoapi.CInterfaceSpeedMode</a>, <a class="el" href="a00439.html#af29a23b1158c181a5a548dae63e4578a">neoapi.CImageCompressionRateOption</a>, <a class="el" href="a00435.html#a2b7a759f77eda1a13474b239982e83d9">neoapi.CImageCompressionMode</a>, <a class="el" href="a00431.html#a5fc59ce79617e99c2d8d1e0da77dbb53">neoapi.CImageCompressionJPEGFormatOption</a>, <a class="el" href="a00427.html#ade9c2d64d7cc429e36e6d522af93f236">neoapi.CHDRTonemappingCurvePresetSelector</a>, <a class="el" href="a00423.html#afb4fdf10de10a7f2a094879abe4b9118">neoapi.CHDRGainRatioSelector</a>, <a class="el" href="a00419.html#a6c01b679488aa6c685ef39b2bbeff456">neoapi.CGevSupportedOptionSelector</a>, <a class="el" href="a00415.html#a3c033d81514420c3d5345b418791cae3">neoapi.CGevIPConfigurationStatus</a>, <a class="el" href="a00411.html#a78dd20533498ddf8c17950a0f67cfd63">neoapi.CGevGVCPExtendedStatusCodesSelector</a>, <a class="el" href="a00407.html#a1a424dbdda8723dfc105e3e3373daa09">neoapi.CGevCCP</a>, <a class="el" href="a00403.html#a0d44064e3563600cae23fa162508512a">neoapi.CGenDCStreamingStatus</a>, <a class="el" href="a00399.html#a6678eb48f85d50245add514de8f6d5c3">neoapi.CGenDCStreamingMode</a>, <a class="el" href="a00395.html#aa2ccdcb7e15a5a1c3bbf063548bc3b27">neoapi.CGainSelector</a>, <a class="el" href="a00391.html#a4096bb90eeaa20766a5f11d1bf74d9ca">neoapi.CGainAuto</a>, <a class="el" href="a00387.html#af627b6a54634b29c1df672c274f657ae">neoapi.CFocusStatus</a>, <a class="el" href="a00383.html#a296cb4d671ec23658d0a1658719d7f55">neoapi.CFocalLengthStatus</a>, <a class="el" href="a00379.html#aa7432204fb5a78edc96ae1cffe072e89">neoapi.CFileSelector</a>, <a class="el" href="a00375.html#ab81fc6c30fd0b3cd73723ca32a4b06be">neoapi.CFileOperationStatus</a>, <a class="el" href="a00371.html#ab7ec2873467e4ca90cedefde5a9a8dd9">neoapi.CFileOperationSelector</a>, <a class="el" href="a00367.html#ab26709bed4721cf96ecba7d146ccdadd">neoapi.CFileOpenMode</a>, <a class="el" href="a00363.html#a18974b7099b7ab85d7873a8d6a5db6e4">neoapi.CExposureMode</a>, <a class="el" href="a00359.html#a7fa481c6ec8e43fb3abdffd6094fdbc8">neoapi.CExposureAuto</a>, <a class="el" href="a00355.html#a66883c9689558cf0caad8e93f8912cf5">neoapi.CEventSelector</a>, <a class="el" href="a00351.html#a52104a8c3c9099841b71ee166e555975">neoapi.CEventNotification</a>, <a class="el" href="a00347.html#a018889ba857c16e2f8d50dd34d3f2c24">neoapi.CDeviceType</a>, <a class="el" href="a00343.html#aa74a32807986a63ab9d9ce59c7f65725">neoapi.CDeviceTemperatureStatusTransitionSelector</a>, <a class="el" href="a00339.html#a70bd05f20cc455ebb13aa6832dc86769">neoapi.CDeviceTemperatureStatus</a>, <a class="el" href="a00335.html#a914fbbd083dd3aef59d1013f90359b7d">neoapi.CDeviceTemperatureSelector</a>, <a class="el" href="a00331.html#a1aa2cd4000b4c60810b15f7469a438da">neoapi.CDeviceTapGeometry</a>, <a class="el" href="a00327.html#a004d406fef68a62bc0e86ecbd70adb86">neoapi.CDeviceTLType</a>, <a class="el" href="a00323.html#aaf5cd59b4aaa2ed4facd418e13bbacfb">neoapi.CDeviceStreamChannelType</a>, <a class="el" href="a00319.html#af5eab96113d077e42c16f77ecad2dc2e">neoapi.CDeviceStreamChannelEndianness</a>, <a class="el" href="a00315.html#acee04321da0b2e2e3c343116697a37a7">neoapi.CDeviceSerialPortSelector</a>, <a class="el" href="a00311.html#a35cc52bc567e68ba8ac4cffaf1bb1e35">neoapi.CDeviceSerialPortBaudRate</a>, <a class="el" href="a00307.html#a4f297d11d2d4254059971516eb06d9ef">neoapi.CDeviceSensorVersion</a>, <a class="el" href="a00303.html#a3e755dccd35b4785508fdf5cb4332240">neoapi.CDeviceSensorType</a>, <a class="el" href="a00299.html#a318916b19eabe7e6b2e589c055fe9aa8">neoapi.CDeviceSensorSelector</a>, <a class="el" href="a00295.html#a44cc1a22b30a204cf14b605aa53a65ee">neoapi.CDeviceScanType</a>, <a class="el" href="a00291.html#aab95a2bdf4c5ddf9de0533d46812f967">neoapi.CDeviceRegistersEndianness</a>, <a class="el" href="a00287.html#acfccd99fed632941862ed02bf5f6f200">neoapi.CDeviceLinkThroughputLimitMode</a>, <a class="el" href="a00283.html#ad1a0b53d839b503ecdb37f0a80456976">neoapi.CDeviceLinkSelector</a>, <a class="el" href="a00279.html#a34de8d894792080343d23900db1bdf7d">neoapi.CDeviceLinkHeartbeatMode</a>, <a class="el" href="a00275.html#ae2dabed455765766b6081a2bcff7e018">neoapi.CDeviceLicenseTypeSelector</a>, <a class="el" href="a00271.html#a69abbc7a087ee70705da45703bfa5957">neoapi.CDeviceLicense</a>, <a class="el" href="a00267.html#a04746a8f74d4702b6630cdbc69871947">neoapi.CDeviceFrontUARTSource</a>, <a class="el" href="a00263.html#a92ca6816590a518d84f3992d4326ab8c">neoapi.CDeviceClockSelector</a>, <a class="el" href="a00259.html#ab788ecf67e5d1382147d4866b2190358">neoapi.CDeviceCharacterSet</a>, <a class="el" href="a00255.html#aabc9efae28e2c6fddfcba6966460e627">neoapi.CDefectPixelListSelector</a>, <a class="el" href="a00251.html#af903c9b8f73ef7d05b206c58979dd647">neoapi.CDecimationVerticalMode</a>, <a class="el" href="a00247.html#a896cd09b387b0bc98f98d798cc660b0f">neoapi.CDecimationHorizontalMode</a>, <a class="el" href="a00243.html#ad453e22e4960ce5d2979dde55c242ab5">neoapi.CCustomDataConfigurationMode</a>, <a class="el" href="a00239.html#ab7344ecd49800237d49393623872cabf">neoapi.CCounterSelector</a>, <a class="el" href="a00235.html#ab76be8aae5fa164ddefdf67a2dc10ed3">neoapi.CCounterResetSource</a>, <a class="el" href="a00231.html#af5bbcbbee07d6c5dee7b89f822ea0444">neoapi.CCounterResetActivation</a>, <a class="el" href="a00227.html#af8c36b3e70a6e836be88787143817de0">neoapi.CCounterEventSource</a>, <a class="el" href="a00223.html#aa2fc38efcdde721408cd810e46047911">neoapi.CCounterEventActivation</a>, <a class="el" href="a00219.html#a9cb32f6eabe718d1154eb8a96d5f20bd">neoapi.CComponentSelector</a>, <a class="el" href="a00215.html#af212f98b0615116151aadd1e1a250070">neoapi.CColorTransformationValueSelector</a>, <a class="el" href="a00211.html#af3293737e695c61b8c54f1cd9409662b">neoapi.CColorTransformationSelector</a>, <a class="el" href="a00207.html#a2cb9da03fbad91448d073e504adcc6dc">neoapi.CColorTransformationFactoryListSelector</a>, <a class="el" href="a00203.html#a472ae56a50266dac655878371a836220">neoapi.CColorTransformationAuto</a>, <a class="el" href="a00199.html#a30edcc9122b540b9b9a6380a0081d15e">neoapi.CClTimeSlotsCount</a>, <a class="el" href="a00195.html#a59ed400a7aee135ab8f00b69739da90f">neoapi.CClConfiguration</a>, <a class="el" href="a00191.html#a8138c19328aea2e9121d0c40f9f04546">neoapi.CChunkSelector</a>, <a class="el" href="a00187.html#afe3cefee529932fc71efb5e0509216f4">neoapi.CCalibrationMatrixValueSelector</a>, <a class="el" href="a00183.html#a84e6603f9b03543ec278691f63761aa4">neoapi.CCalibrationMatrixColorSelector</a>, <a class="el" href="a00179.html#a740ffb897884cf84b10f049d522c9d73">neoapi.CBrightnessCorrection</a>, <a class="el" href="a00175.html#abd74b2ecce585bcb22dada8e246ac693">neoapi.CBrightnessAutoPriority</a>, <a class="el" href="a00171.html#acbc1c4b957b7db4e6780cba2e2bf2126">neoapi.CBoSequencerStart</a>, <a class="el" href="a00167.html#acc3401080cd6c067b8eb8c494c93ed8e">neoapi.CBoSequencerSensorDigitizationTaps</a>, <a class="el" href="a00163.html#a298268a4b7b7632edfcc108406d0bc2f">neoapi.CBoSequencerMode</a>, <a class="el" href="a00159.html#a529e35cce1328b5f0357f01e6eef4452">neoapi.CBoSequencerIOSelector</a>, <a class="el" href="a00155.html#a5d630e6adf38fb658bf95de80aa31fe8">neoapi.CBoSequencerEnable</a>, <a class="el" href="a00151.html#a170f6ad62b0f2ac9aa2ee8c2331dfb8f">neoapi.CBlackSunSuppression</a>, <a class="el" href="a00147.html#a475e83e510de97a2e57c6dbeec3d6959">neoapi.CBlackLevelSelector</a>, <a class="el" href="a00143.html#a9d55204c9a6bf38ba1bedd82f0bcdcb0">neoapi.CBinningVerticalMode</a>, <a class="el" href="a00139.html#a3f4a2d82df9f8106dc737d59259ca1ed">neoapi.CBinningSelector</a>, <a class="el" href="a00135.html#a8b0ac4ddaad00dbab5eb0f82ee9ee30d">neoapi.CBinningHorizontalMode</a>, <a class="el" href="a00131.html#a6212cde23a0ec26771bf3c23b8306aa8">neoapi.CBaudrate</a>, <a class="el" href="a00127.html#a32a1cc9349be8dfcad8154c7799b6d61">neoapi.CBalanceWhiteAutoStatus</a>, <a class="el" href="a00123.html#a33b254de22b4663aa1b505c5c93dc6b4">neoapi.CBalanceWhiteAuto</a>, <a class="el" href="a00119.html#a2160836a65a405dee29eb1126418a18d">neoapi.CBOPFShift</a>, <a class="el" href="a00115.html#aab4000d6195fe75ce7caf05f58ff3301">neoapi.CAutoFeatureRegionSelector</a>, <a class="el" href="a00111.html#a304c9b51735f9c3414a4b43b98c1fda4">neoapi.CAutoFeatureRegionReference</a>, <a class="el" href="a00107.html#a684ddeebf7d928ad0a9c64181dbbcc39">neoapi.CAutoFeatureRegionMode</a>, <a class="el" href="a00103.html#aa26e582b7f911a0afb6fae010ed2a861">neoapi.CApertureStatus</a>, <a class="el" href="a00099.html#a68788f06d5bf591950719cfdabd9fd90">neoapi.CAcquisitionStatusSelector</a>, and <a class="el" href="a00095.html#a92d57bef1ffe3136ac2966d6b488fa26">neoapi.CAcquisitionMode</a>.</p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="aeb4392642486f932de046b64d6114df6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeb4392642486f932de046b64d6114df6">&#9670;&nbsp;</a></span>GetEnumValueList()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;FeatureList&quot; neoapi.EnumerationFeature.GetEnumValueList </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get a list of all possible values of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object Only valid for interface type 'IEnumeration'. </p>
<dl class="section return"><dt>Returns</dt><dd>The list of all possible values of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a452fdbc062b37f3915e0bcfb0aebf3b0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a452fdbc062b37f3915e0bcfb0aebf3b0">&#9670;&nbsp;</a></span>GetInt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.EnumerationFeature.GetInt </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object as integer. </p>
<dl class="section return"><dt>Returns</dt><dd>The current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a0bcb8c3b80260c8d0cde108eaf365be4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bcb8c3b80260c8d0cde108eaf365be4">&#9670;&nbsp;</a></span>SetInt()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;EnumerationFeature&quot; neoapi.EnumerationFeature.SetInt </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Writes an integer value to the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>An integer value to be written </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00847.html" title="Base class providing the &#39;IEnumeration&#39; interface.">EnumerationFeature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac4a3e7aba4519e49f8f32afe4ba29869"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac4a3e7aba4519e49f8f32afe4ba29869">&#9670;&nbsp;</a></span>GetString()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.EnumerationFeature.GetString </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> as a string. </p>
<dl class="section return"><dt>Returns</dt><dd>The current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a82a850e2d931254434f9a8e454fcb536"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82a850e2d931254434f9a8e454fcb536">&#9670;&nbsp;</a></span>SetString()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;EnumerationFeature&quot; neoapi.EnumerationFeature.SetString </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;str&quot;&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Write the value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> as a string. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>A string value to be written </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00847.html" title="Base class providing the &#39;IEnumeration&#39; interface.">EnumerationFeature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ab78a7830a2eeff9e0c82484d8f538962"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab78a7830a2eeff9e0c82484d8f538962">&#9670;&nbsp;</a></span>IsSelector()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.EnumerationFeature.IsSelector </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Indicates whether the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is a selector A selector is a possibility to define feature dependencies. </p>
<p>The current value of a selector feature has an impact on the value of another <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </p><dl class="section return"><dt>Returns</dt><dd>True if the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is a selector, otherwise false </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a2302f54f1139c38b3de67ae9b8606afb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2302f54f1139c38b3de67ae9b8606afb">&#9670;&nbsp;</a></span>GetSelectedFeatureList()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;FeatureList&quot; neoapi.EnumerationFeature.GetSelectedFeatureList </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get a list of features that depend on this selector feature. </p>
<dl class="section return"><dt>Returns</dt><dd>The list of all features that depend on this selector <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="affff6bd927a38fc199f92c32fd031490"></a>
<h2 class="memtitle"><span class="permalink"><a href="#affff6bd927a38fc199f92c32fd031490">&#9670;&nbsp;</a></span>GetInterface()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetInterface </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the GenICam interface type of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object Depending on the GenICam interface type, different feature access methods are provided. </p>
<p>The available interface types are defined in header file bgapi2_def.h. See definitions BGAPI2_NODEINTERFACE_xxx </p><dl class="section return"><dt>Returns</dt><dd>The interface type of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ad1aa8c9d453a386b2f9f91cd3b3615c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad1aa8c9d453a386b2f9f91cd3b3615c3">&#9670;&nbsp;</a></span>GetToolTip()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetToolTip </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get a short description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The short description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6830b31068e48ae5db294d32f2def11c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6830b31068e48ae5db294d32f2def11c">&#9670;&nbsp;</a></span>GetDescription()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetDescription </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The description text of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ae28b09dddd7447d487a04a8258590633"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae28b09dddd7447d487a04a8258590633">&#9670;&nbsp;</a></span>GetName()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetName </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa79f26165f8083861522250888e0cee3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa79f26165f8083861522250888e0cee3">&#9670;&nbsp;</a></span>GetDisplayName()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetDisplayName </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the display name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The display name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a4bacabd7d5e68d9e54e88314660e2e9a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4bacabd7d5e68d9e54e88314660e2e9a">&#9670;&nbsp;</a></span>GetVisibility()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetVisibility </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the recommended visibility (Beginner/Expert/Guru or Invisible) of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>A string representing the visibility of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a04f73633d9a8b93b7865bf91d2d36b67"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a04f73633d9a8b93b7865bf91d2d36b67">&#9670;&nbsp;</a></span>IsReadable()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.BaseFeature.IsReadable </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Indicates that the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is readable. </p>
<dl class="section return"><dt>Returns</dt><dd>True if the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is readable, otherwise false </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aac1faf49fc350a92ddf2ce11d35ba85c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac1faf49fc350a92ddf2ce11d35ba85c">&#9670;&nbsp;</a></span>IsWritable()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.BaseFeature.IsWritable </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Indicates if a <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is writeable. </p>
<dl class="section return"><dt>Returns</dt><dd>True if the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is writable, otherwise false </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ada3c4ad870b561c575714a16f70d19db"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada3c4ad870b561c575714a16f70d19db">&#9670;&nbsp;</a></span>IsAvailable()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.BaseFeature.IsAvailable </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Indicates whether the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is available "Not available" or False is equivalent to the access mode 'NA'. </p>
<dl class="section return"><dt>Returns</dt><dd>True if the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is available to work with it, otherwise false </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a8c0a659fcc6f0de09b7d42530b8aaad8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8c0a659fcc6f0de09b7d42530b8aaad8">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.EnumerationFeature.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
