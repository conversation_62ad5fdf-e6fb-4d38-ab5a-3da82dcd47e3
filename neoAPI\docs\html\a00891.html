<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.CamInfoList Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00891.html">CamInfoList</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00888.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.CamInfoList Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00086.html">Cam Interface Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides a list of physically connected cameras available to be used/connected with neoAPI You can use this class if you don't know what camera(s) might be connected during program operation.  
 <a href="a00891.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.CamInfoList:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00891.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a8383513389d8811c0ec2baae700247f9"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a8383513389d8811c0ec2baae700247f9">__init__</a> (self, *args, **kwargs)</td></tr>
<tr class="memdesc:a8383513389d8811c0ec2baae700247f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor is not defined.  <a href="#a8383513389d8811c0ec2baae700247f9">More...</a><br /></td></tr>
<tr class="separator:a8383513389d8811c0ec2baae700247f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a520de154bd17cf582c07566b25f99ae1"><td class="memItemLeft" align="right" valign="top">&quot;CamInfoList&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a520de154bd17cf582c07566b25f99ae1">Get</a> ()</td></tr>
<tr class="memdesc:a520de154bd17cf582c07566b25f99ae1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the single instance.  <a href="#a520de154bd17cf582c07566b25f99ae1">More...</a><br /></td></tr>
<tr class="separator:a520de154bd17cf582c07566b25f99ae1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f1634b1ae2d5386893de770ff8fef98"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a7f1634b1ae2d5386893de770ff8fef98">Refresh</a> (self)</td></tr>
<tr class="memdesc:a7f1634b1ae2d5386893de770ff8fef98"><td class="mdescLeft">&#160;</td><td class="mdescRight">Refresh camera list.  <a href="#a7f1634b1ae2d5386893de770ff8fef98">More...</a><br /></td></tr>
<tr class="separator:a7f1634b1ae2d5386893de770ff8fef98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82ac0ff93e40ce0631ece40948286647"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a82ac0ff93e40ce0631ece40948286647">size</a> (self)</td></tr>
<tr class="memdesc:a82ac0ff93e40ce0631ece40948286647"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the current number of cameras in this list.  <a href="#a82ac0ff93e40ce0631ece40948286647">More...</a><br /></td></tr>
<tr class="separator:a82ac0ff93e40ce0631ece40948286647"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ae3f835b0ca232846f1fcc819d32949"><td class="memItemLeft" align="right" valign="top">&quot;NeoEvent&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a2ae3f835b0ca232846f1fcc819d32949">GetPnPEvent</a> (self, &quot;int&quot; timeout=400)</td></tr>
<tr class="memdesc:a2ae3f835b0ca232846f1fcc819d32949"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a plug and play event from the camera info list The GetPnPEvent method is used to retrieve plug and play events from the camera info list.  <a href="#a2ae3f835b0ca232846f1fcc819d32949">More...</a><br /></td></tr>
<tr class="separator:a2ae3f835b0ca232846f1fcc819d32949"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a765baee4d9cb0a86c9ba7be81f75bec7"><td class="memItemLeft" align="right" valign="top">&quot;CamInfoList&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a765baee4d9cb0a86c9ba7be81f75bec7">ClearPnPEvents</a> (self)</td></tr>
<tr class="memdesc:a765baee4d9cb0a86c9ba7be81f75bec7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Delete all events in the event queue.  <a href="#a765baee4d9cb0a86c9ba7be81f75bec7">More...</a><br /></td></tr>
<tr class="separator:a765baee4d9cb0a86c9ba7be81f75bec7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80fc5ad3f6b12e2a80c9b91e351893cb"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a80fc5ad3f6b12e2a80c9b91e351893cb">EnablePnPEventCallback</a> (self, callback)</td></tr>
<tr class="memdesc:a80fc5ad3f6b12e2a80c9b91e351893cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enable event callback to get notified of changes of the <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a>.  <a href="#a80fc5ad3f6b12e2a80c9b91e351893cb">More...</a><br /></td></tr>
<tr class="separator:a80fc5ad3f6b12e2a80c9b91e351893cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5ad4b079de5ee2d2745b4a0686b6d577"><td class="memItemLeft" align="right" valign="top">&quot;CamInfoList&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a5ad4b079de5ee2d2745b4a0686b6d577">DisablePnPEventCallback</a> (self)</td></tr>
<tr class="memdesc:a5ad4b079de5ee2d2745b4a0686b6d577"><td class="mdescLeft">&#160;</td><td class="mdescRight">Disable an active event callback.  <a href="#a5ad4b079de5ee2d2745b4a0686b6d577">More...</a><br /></td></tr>
<tr class="separator:a5ad4b079de5ee2d2745b4a0686b6d577"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa14de1077e3187e5623006012791ed95"><td class="memItemLeft" align="right" valign="top">&quot;CamInfoIterator&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#aa14de1077e3187e5623006012791ed95">__iter__</a> (self)</td></tr>
<tr class="memdesc:aa14de1077e3187e5623006012791ed95"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides iterator functionality.  <a href="#aa14de1077e3187e5623006012791ed95">More...</a><br /></td></tr>
<tr class="separator:aa14de1077e3187e5623006012791ed95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5333a252b55b080808272b39ddf87dc7"><td class="memItemLeft" align="right" valign="top">&quot;CamInfo&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a5333a252b55b080808272b39ddf87dc7">__getitem__</a> (self, &quot;int&quot; index)</td></tr>
<tr class="memdesc:a5333a252b55b080808272b39ddf87dc7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides iterator functionality.  <a href="#a5333a252b55b080808272b39ddf87dc7">More...</a><br /></td></tr>
<tr class="separator:a5333a252b55b080808272b39ddf87dc7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a25550ab6a6f4ba63a32edcc933f79f84"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00891.html#a25550ab6a6f4ba63a32edcc933f79f84">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:a25550ab6a6f4ba63a32edcc933f79f84"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#a25550ab6a6f4ba63a32edcc933f79f84">More...</a><br /></td></tr>
<tr class="separator:a25550ab6a6f4ba63a32edcc933f79f84"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Provides a list of physically connected cameras available to be used/connected with neoAPI You can use this class if you don't know what camera(s) might be connected during program operation. </p>
<p>With the <a class="el" href="a00891.html#a7f1634b1ae2d5386893de770ff8fef98" title="Refresh camera list.">neoapi.CamInfoList.Refresh()</a> method you can update the list to reflect current system status We provide Plug and Play Events for the <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a> class which you can poll or register an callback for. Those will notify you of any changes on the system. If you get such an event, you still need to call neoapi. <a class="el" href="a00891.html#a7f1634b1ae2d5386893de770ff8fef98" title="Refresh camera list.">CamInfoList.Refresh()</a> manually to update the list, see <a class="el" href="a00921.html">Event and Plug and Play Programming Concepts of the neoAPI</a> for details </p><dl class="section note"><dt>Note</dt><dd>This is a singleton and is only instanciated once </dd></dl>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a8383513389d8811c0ec2baae700247f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8383513389d8811c0ec2baae700247f9">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.CamInfoList.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">**&#160;</td>
          <td class="paramname"><em>kwargs</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructor is not defined. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>not defined </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">**kwargs</td><td>not defined </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a520de154bd17cf582c07566b25f99ae1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a520de154bd17cf582c07566b25f99ae1">&#9670;&nbsp;</a></span>Get()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;CamInfoList&quot; neoapi.CamInfoList.Get </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the single instance. </p>
<dl class="section return"><dt>Returns</dt><dd>The instance of the <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a> </dd></dl>

</div>
</div>
<a id="a7f1634b1ae2d5386893de770ff8fef98"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f1634b1ae2d5386893de770ff8fef98">&#9670;&nbsp;</a></span>Refresh()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.CamInfoList.Refresh </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Refresh camera list. </p>
<dl class="section return"><dt>Returns</dt><dd>True if the list has changed, otherwise false </dd></dl>

</div>
</div>
<a id="a82ac0ff93e40ce0631ece40948286647"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82ac0ff93e40ce0631ece40948286647">&#9670;&nbsp;</a></span>size()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.CamInfoList.size </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Provides the current number of cameras in this list. </p>
<dl class="section return"><dt>Returns</dt><dd>The number of cameras </dd></dl>

</div>
</div>
<a id="a2ae3f835b0ca232846f1fcc819d32949"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2ae3f835b0ca232846f1fcc819d32949">&#9670;&nbsp;</a></span>GetPnPEvent()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;NeoEvent&quot; neoapi.CamInfoList.GetPnPEvent </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>timeout</em> = <code>400</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get a plug and play event from the camera info list The GetPnPEvent method is used to retrieve plug and play events from the camera info list. </p>
<p>Those events notify you that one ore more camera has been connected or disconnected from the system. If an event is waiting to be retrieved, it will return immediately, if no event is available at the time the method is called, it will wait for the timeout specified with the parameter timeout (in ms). The default Timeout is 400 ms </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">timeout</td><td>Timeout in ms to wait for an event, default is 400 ms </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00807.html" title="Provides access to events This class provides an easy way to work with events.">NeoEvent</a> object with all relevant data, could be empty </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00807.html#a319a9dbb64925123137c58a8466196b4" title="Check if the event is empty or filled with data.">NeoEvent.IsEmpty()</a> </dd></dl>

</div>
</div>
<a id="a765baee4d9cb0a86c9ba7be81f75bec7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a765baee4d9cb0a86c9ba7be81f75bec7">&#9670;&nbsp;</a></span>ClearPnPEvents()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;CamInfoList&quot; neoapi.CamInfoList.ClearPnPEvents </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Delete all events in the event queue. </p>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a> object </dd></dl>

</div>
</div>
<a id="a80fc5ad3f6b12e2a80c9b91e351893cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a80fc5ad3f6b12e2a80c9b91e351893cb">&#9670;&nbsp;</a></span>EnablePnPEventCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.CamInfoList.EnablePnPEventCallback </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Enable event callback to get notified of changes of the <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a>. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The EventCallback method of the given object will be called for every event data. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a> object Auto created callback class. Auto created callback methode. </dd></dl>

</div>
</div>
<a id="a5ad4b079de5ee2d2745b4a0686b6d577"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5ad4b079de5ee2d2745b4a0686b6d577">&#9670;&nbsp;</a></span>DisablePnPEventCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;CamInfoList&quot; neoapi.CamInfoList.DisablePnPEventCallback </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Disable an active event callback. </p>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a> object </dd></dl>

</div>
</div>
<a id="aa14de1077e3187e5623006012791ed95"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa14de1077e3187e5623006012791ed95">&#9670;&nbsp;</a></span>__iter__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;CamInfoIterator&quot; neoapi.CamInfoList.__iter__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Provides iterator functionality. </p>
<dl class="section return"><dt>Returns</dt><dd>Iterator </dd></dl>

</div>
</div>
<a id="a5333a252b55b080808272b39ddf87dc7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5333a252b55b080808272b39ddf87dc7">&#9670;&nbsp;</a></span>__getitem__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;CamInfo&quot; neoapi.CamInfoList.__getitem__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>index</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Provides iterator functionality. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">index</td><td>The index to the <a class="el" href="a00883.html" title="Camera info container class which offers basic information about an available camera If neoapi....">CamInfo</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00883.html" title="Camera info container class which offers basic information about an available camera If neoapi....">CamInfo</a> </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a25550ab6a6f4ba63a32edcc933f79f84"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a25550ab6a6f4ba63a32edcc933f79f84">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.CamInfoList.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
