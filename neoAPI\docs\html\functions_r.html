<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_r"></a>- r -</h3><ul>
<li>ReadMemory()
: <a class="el" href="a00863.html#a2e6dc00e3cd05182ef06b227742a12da">neoapi.Cam</a>
, <a class="el" href="a00859.html#a10265873a47b6e513c5ea997f17baa54">neoapi.CamBase</a>
</li>
<li>ReadOutBuffering()
: <a class="el" href="a00763.html#ad73d262d9b733d50fb74f8a577d5d5e6">neoapi.FeatureAccess</a>
</li>
<li>ReadoutMode()
: <a class="el" href="a00763.html#a17538c6371761a737e9f31b91c184d47">neoapi.FeatureAccess</a>
</li>
<li>ReadOutTime()
: <a class="el" href="a00763.html#a54f322036c7124085cee9a2b41afeddd">neoapi.FeatureAccess</a>
</li>
<li>Refresh()
: <a class="el" href="a00891.html#a7f1634b1ae2d5386893de770ff8fef98">neoapi.CamInfoList</a>
</li>
<li>RegionAcquisitionMode()
: <a class="el" href="a00763.html#a1466a9d8e78886bd411f7bf114c44c1b">neoapi.FeatureAccess</a>
</li>
<li>RegionConfigurationMode()
: <a class="el" href="a00763.html#affa25530b710a8f3b859f288193ca3c4">neoapi.FeatureAccess</a>
</li>
<li>RegionIDValue()
: <a class="el" href="a00763.html#a4fcf8c05bdf81c45b48687114e4daaf2">neoapi.FeatureAccess</a>
</li>
<li>RegionMode()
: <a class="el" href="a00763.html#a5b80f1a227d47b2b0a0e6fd8238086b5">neoapi.FeatureAccess</a>
</li>
<li>RegionSelector()
: <a class="el" href="a00763.html#a1ddaa21932a67569fa87cd340ab8cb65">neoapi.FeatureAccess</a>
</li>
<li>RegionTransferMode()
: <a class="el" href="a00763.html#a8961cd5b95ee4d44a6386574be07253d">neoapi.FeatureAccess</a>
</li>
<li>RegionVersion()
: <a class="el" href="a00763.html#a79f3f4ebe75fe555890fd8b8500dc2f2">neoapi.FeatureAccess</a>
</li>
<li>RegisterMemory()
: <a class="el" href="a00879.html#accdcffb1da9a2438b3c3df5d1b2bd377">neoapi.BufferBase</a>
</li>
<li>RegisterSegment()
: <a class="el" href="a00879.html#aab7090bce8566f9b492abf7e1ba8a449">neoapi.BufferBase</a>
</li>
<li>ReverseX()
: <a class="el" href="a00763.html#a4be2ca8afc5e41c815c22cc22788626a">neoapi.FeatureAccess</a>
</li>
<li>ReverseY()
: <a class="el" href="a00763.html#a7908a87cefd1869054aa9c8f7b3433d2">neoapi.FeatureAccess</a>
</li>
<li>RevokeUserBuffer()
: <a class="el" href="a00863.html#ac3334df2cd21515996229fd6fdb3db90">neoapi.Cam</a>
, <a class="el" href="a00859.html#ae55b7e85dbf2e1553af0fa906d709b4d">neoapi.CamBase</a>
</li>
<li>RxAcknowledgeLength()
: <a class="el" href="a00763.html#af17bcb27cf860e8279d2e3db7d1f812a">neoapi.FeatureAccess</a>
</li>
<li>RxDiscardedMessages()
: <a class="el" href="a00763.html#a54e0cf2c6128975b3b00ced8afd6611f">neoapi.FeatureAccess</a>
</li>
<li>RxFiFo()
: <a class="el" href="a00763.html#a50f32ff426068df7ee5f23bbf4a80f3f">neoapi.FeatureAccess</a>
</li>
<li>RxFiFoMessageLength()
: <a class="el" href="a00763.html#a9548f8cbc6c4669f538311998c9065bc">neoapi.FeatureAccess</a>
</li>
<li>RxRetryCount()
: <a class="el" href="a00763.html#af903ea0b6d7b73c6bcd86e7cc7cd71b0">neoapi.FeatureAccess</a>
</li>
<li>RxSynchronizationDelay()
: <a class="el" href="a00763.html#ac41a5d05c0d7bb7844ea3042c463f225">neoapi.FeatureAccess</a>
</li>
<li>RxSynchronizationDelayNormalized()
: <a class="el" href="a00763.html#a395e1328a335682fe8b9d99a71f47c80">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
