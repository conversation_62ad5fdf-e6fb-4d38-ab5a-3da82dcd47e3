<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_a"></a>- a -</h3><ul>
<li>aActionCommandMACCtrlFramesError()
: <a class="el" href="a00763.html#a7a1ed900be72f117f414deb9609a6e68">neoapi.FeatureAccess</a>
</li>
<li>aActionCommandMACCtrlFramesReceived()
: <a class="el" href="a00763.html#acb016960ceb291bb4f436b6fde186693">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionAbort()
: <a class="el" href="a00763.html#ab27ada8e8edc38dab523f43881a80038">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionFrameCount()
: <a class="el" href="a00763.html#af97899bb332a8e9e3be1d5e46905de8f">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionFrameRate()
: <a class="el" href="a00763.html#a3d77f18e235688b181f8fb78721e963e">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionFrameRateEnable()
: <a class="el" href="a00763.html#a429ac2c32412a9f63bace306ee177442">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionFrameRateLimit()
: <a class="el" href="a00763.html#a7ed74163973fb7a66f3832513dd1672f">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionMode()
: <a class="el" href="a00763.html#ae304236860e45790e00c068d2e64283a">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionStart()
: <a class="el" href="a00763.html#abe73cd4a73b01725b552d860ea9fabeb">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionStartAuto()
: <a class="el" href="a00763.html#a89145edc93628b705e5e1953f3d297c6">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionStatus()
: <a class="el" href="a00763.html#a202bab1173023dafb18f6f823901ef81">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionStatusSelector()
: <a class="el" href="a00763.html#adbfefe8d8c5297655e1551c95f9e5cff">neoapi.FeatureAccess</a>
</li>
<li>AcquisitionStop()
: <a class="el" href="a00763.html#ace39c0cc0c418ffdf7a2d52509dbea5c">neoapi.FeatureAccess</a>
</li>
<li>ActionDeviceKey()
: <a class="el" href="a00763.html#af7c729f4c5d25b72032e0260c212155f">neoapi.FeatureAccess</a>
</li>
<li>ActionGroupKey()
: <a class="el" href="a00763.html#af9d856d0e0b4f97248f0a2312c7a9e83">neoapi.FeatureAccess</a>
</li>
<li>ActionGroupMask()
: <a class="el" href="a00763.html#add36d7d28520915181e9653bc454a474">neoapi.FeatureAccess</a>
</li>
<li>ActionSelector()
: <a class="el" href="a00763.html#a490d75fcac40111aa65730acb4631434">neoapi.FeatureAccess</a>
</li>
<li>Add()
: <a class="el" href="a00867.html#a6bb971602bd5d990e484b9ba12994a31">neoapi.FeatureStack</a>
</li>
<li>AddUserBuffer()
: <a class="el" href="a00859.html#ad62bf7e7209b986b3f0140301273b202">neoapi.CamBase</a>
</li>
<li>aPacketMACCtrlFramesError()
: <a class="el" href="a00763.html#a535dfc95c814e458fc4d3dbe3f5ef646">neoapi.FeatureAccess</a>
</li>
<li>aPacketMACCtrlFramesLost()
: <a class="el" href="a00763.html#ae154fc67f800bf5bc1fd43b052cc3ba5">neoapi.FeatureAccess</a>
</li>
<li>aPacketMACCtrlFramesReceived()
: <a class="el" href="a00763.html#af4bd30090022a02e1187586f3e0ab22d">neoapi.FeatureAccess</a>
</li>
<li>aPAUSEMACCtrlFramesReceived()
: <a class="el" href="a00763.html#ac3c65a6a19a2f9bad15f51b5dace5df7">neoapi.FeatureAccess</a>
</li>
<li>Aperture()
: <a class="el" href="a00763.html#a39ec90eb1406c97a37a95ffea567a6a1">neoapi.FeatureAccess</a>
</li>
<li>ApertureInitialize()
: <a class="el" href="a00763.html#a41294bfaff7232f9f060c910a08f1681">neoapi.FeatureAccess</a>
</li>
<li>ApertureStatus()
: <a class="el" href="a00763.html#adabf6855bd615f8459725bdab1816df0">neoapi.FeatureAccess</a>
</li>
<li>ApertureStepper()
: <a class="el" href="a00763.html#a15c604e6cc647c0fe689b8c26081fe8e">neoapi.FeatureAccess</a>
</li>
<li>aResendMACCtrlFramesError()
: <a class="el" href="a00763.html#ae2746a9c2d6b5106c05eab052e4bec50">neoapi.FeatureAccess</a>
</li>
<li>aResendMACCtrlFramesReceived()
: <a class="el" href="a00763.html#abf8ddfda74020a3446cd47a8cc1285bb">neoapi.FeatureAccess</a>
</li>
<li>AutoFeatureCycleTime()
: <a class="el" href="a00763.html#a86daa37234e96ba6920f2863bfe59c3e">neoapi.FeatureAccess</a>
</li>
<li>AutoFeatureHeight()
: <a class="el" href="a00763.html#a2c4867ee1a74641daaae2277db736d37">neoapi.FeatureAccess</a>
</li>
<li>AutoFeatureOffsetX()
: <a class="el" href="a00763.html#a01b430ff64f23f9c39ebefe7ea8507e9">neoapi.FeatureAccess</a>
</li>
<li>AutoFeatureOffsetY()
: <a class="el" href="a00763.html#af21e9f9cdbfe6852b191ed0ef03204ac">neoapi.FeatureAccess</a>
</li>
<li>AutoFeatureRegionMode()
: <a class="el" href="a00763.html#aceb300bb84d592de854e7e374a1edd8a">neoapi.FeatureAccess</a>
</li>
<li>AutoFeatureRegionReference()
: <a class="el" href="a00763.html#a0e8c3bd340a882eddd6dfcf2b662df1a">neoapi.FeatureAccess</a>
</li>
<li>AutoFeatureRegionSelector()
: <a class="el" href="a00763.html#a81bb2275793b8d9f500fa40e5198174f">neoapi.FeatureAccess</a>
</li>
<li>AutoFeatureWidth()
: <a class="el" href="a00763.html#a807b426b0e594d6867b8eae8770a2a05">neoapi.FeatureAccess</a>
</li>
<li>AveragingEnable()
: <a class="el" href="a00763.html#a3aa897248769e430d37fa278de4b064e">neoapi.FeatureAccess</a>
</li>
<li>AveragingImageCount()
: <a class="el" href="a00763.html#adac6c9cf584b32e48d602a83e124fd44">neoapi.FeatureAccess</a>
</li>
<li>AveragingNormalization()
: <a class="el" href="a00763.html#ab69743f9f6a8d180fb549dff83eed26c">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
