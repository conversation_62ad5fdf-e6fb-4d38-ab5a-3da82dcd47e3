import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QSpinBox, QDoubleSpinBox, QLineEdit, QPushButton,
                             QGroupBox, QLabel, QCheckBox, QComboBox, QFileDialog)
from PyQt5.QtCore import pyqtSignal

class SettingsWidget(QWidget):
    """Widget für Anwendungseinstellungen"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.settings = self.load_default_settings()
        self.init_ui()
        
    def load_default_settings(self):
        """Lädt Standard-Einstellungen"""
        from core.config import (DEFAULT_TEMP_INTERVAL, TEMP_WARNING_THRESHOLD,
                                   TEMP_CRITICAL_THRESHOLD, MAX_TEMP_HISTORY_DAYS)
        
        return {
            'measurement_interval': DEFAULT_TEMP_INTERVAL,
            'warning_threshold': TEMP_WARNING_THRESHOLD,
            'critical_threshold': TEMP_CRITICAL_THRESHOLD,
            'history_days': MAX_TEMP_HISTORY_DAYS,
            'auto_discovery': True,
            'discovery_interval': 300,
            'network_range': '192.168.1',
            'log_level': 'INFO',
            'export_path': '',
            'enable_alerts': True
        }
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        layout = QVBoxLayout(self)
        
        # Temperatur-Einstellungen
        temp_group = QGroupBox("Temperatur-Einstellungen")
        temp_layout = QFormLayout(temp_group)
        
        # Messintervall
        self.interval_spinbox = QSpinBox()
        self.interval_spinbox.setRange(5, 3600)
        self.interval_spinbox.setSuffix(" Sekunden")
        self.interval_spinbox.setValue(self.settings['measurement_interval'])
        self.interval_spinbox.valueChanged.connect(self.on_settings_changed)
        temp_layout.addRow("Messintervall:", self.interval_spinbox)
        
        # Warnschwelle
        self.warning_spinbox = QDoubleSpinBox()
        self.warning_spinbox.setRange(0, 100)
        self.warning_spinbox.setSuffix(" °C")
        self.warning_spinbox.setValue(self.settings['warning_threshold'])
        self.warning_spinbox.valueChanged.connect(self.on_settings_changed)
        temp_layout.addRow("Warnschwelle:", self.warning_spinbox)
        
        # Kritische Schwelle
        self.critical_spinbox = QDoubleSpinBox()
        self.critical_spinbox.setRange(0, 100)
        self.critical_spinbox.setSuffix(" °C")
        self.critical_spinbox.setValue(self.settings['critical_threshold'])
        self.critical_spinbox.valueChanged.connect(self.on_settings_changed)
        temp_layout.addRow("Kritische Schwelle:", self.critical_spinbox)
        
        # Historien-Aufbewahrung
        self.history_spinbox = QSpinBox()
        self.history_spinbox.setRange(1, 365)
        self.history_spinbox.setSuffix(" Tage")
        self.history_spinbox.setValue(self.settings['history_days'])
        self.history_spinbox.valueChanged.connect(self.on_settings_changed)
        temp_layout.addRow("Daten aufbewahren:", self.history_spinbox)
        
        layout.addWidget(temp_group)
        
        # Netzwerk-Einstellungen
        network_group = QGroupBox("Netzwerk-Einstellungen")
        network_layout = QFormLayout(network_group)
        
        # Auto-Discovery
        self.auto_discovery_checkbox = QCheckBox()
        self.auto_discovery_checkbox.setChecked(self.settings['auto_discovery'])
        self.auto_discovery_checkbox.toggled.connect(self.on_settings_changed)
        network_layout.addRow("Automatische Erkennung:", self.auto_discovery_checkbox)
        
        # Discovery-Intervall
        self.discovery_interval_spinbox = QSpinBox()
        self.discovery_interval_spinbox.setRange(60, 3600)
        self.discovery_interval_spinbox.setSuffix(" Sekunden")
        self.discovery_interval_spinbox.setValue(self.settings['discovery_interval'])
        self.discovery_interval_spinbox.valueChanged.connect(self.on_settings_changed)
        network_layout.addRow("Suchintervall:", self.discovery_interval_spinbox)
        
        # Netzwerk-Bereich
        self.network_range_edit = QLineEdit()
        self.network_range_edit.setText(self.settings['network_range'])
        self.network_range_edit.setPlaceholderText("z.B. 192.168.1")
        self.network_range_edit.textChanged.connect(self.on_settings_changed)
        network_layout.addRow("Netzwerk-Bereich:", self.network_range_edit)
        
        layout.addWidget(network_group)
        
        # System-Einstellungen
        system_group = QGroupBox("System-Einstellungen")
        system_layout = QFormLayout(system_group)
        
        # Log-Level
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(['DEBUG', 'INFO', 'WARNING', 'ERROR'])
        self.log_level_combo.setCurrentText(self.settings['log_level'])
        self.log_level_combo.currentTextChanged.connect(self.on_settings_changed)
        system_layout.addRow("Log-Level:", self.log_level_combo)
        
        # Benachrichtigungen
        self.alerts_checkbox = QCheckBox()
        self.alerts_checkbox.setChecked(self.settings['enable_alerts'])
        self.alerts_checkbox.toggled.connect(self.on_settings_changed)
        system_layout.addRow("Benachrichtigungen:", self.alerts_checkbox)
        
        layout.addWidget(system_group)
        
        # Export-Einstellungen
        export_group = QGroupBox("Export-Einstellungen")
        export_layout = QFormLayout(export_group)
        
        # Export-Pfad
        export_path_layout = QHBoxLayout()
        self.export_path_edit = QLineEdit()
        self.export_path_edit.setText(self.settings['export_path'])
        self.export_path_edit.setPlaceholderText("Pfad für Datenexport")
        export_path_layout.addWidget(self.export_path_edit)
        
        self.browse_button = QPushButton("Durchsuchen")
        self.browse_button.clicked.connect(self.browse_export_path)
        export_path_layout.addWidget(self.browse_button)
        
        export_layout.addRow("Export-Pfad:", export_path_layout)
        
        layout.addWidget(export_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("Einstellungen speichern")
        self.save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_button)
        
        self.reset_button = QPushButton("Zurücksetzen")
        self.reset_button.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_button)
        
        self.export_button = QPushButton("Daten exportieren")
        self.export_button.clicked.connect(self.export_data)
        button_layout.addWidget(self.export_button)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # Status
        self.status_label = QLabel("Einstellungen bereit")
        layout.addWidget(self.status_label)
        
    def on_settings_changed(self):
        """Wird aufgerufen wenn Einstellungen geändert werden"""
        self.update_settings_from_ui()
        self.settings_changed.emit()
        self.status_label.setText("Einstellungen geändert (nicht gespeichert)")
        
    def update_settings_from_ui(self):
        """Aktualisiert Einstellungen aus UI-Elementen"""
        self.settings['measurement_interval'] = self.interval_spinbox.value()
        self.settings['warning_threshold'] = self.warning_spinbox.value()
        self.settings['critical_threshold'] = self.critical_spinbox.value()
        self.settings['history_days'] = self.history_spinbox.value()
        self.settings['auto_discovery'] = self.auto_discovery_checkbox.isChecked()
        self.settings['discovery_interval'] = self.discovery_interval_spinbox.value()
        self.settings['network_range'] = self.network_range_edit.text()
        self.settings['log_level'] = self.log_level_combo.currentText()
        self.settings['enable_alerts'] = self.alerts_checkbox.isChecked()
        self.settings['export_path'] = self.export_path_edit.text()
        
    def save_settings(self):
        """Speichert Einstellungen"""
        try:
            self.update_settings_from_ui()
            
            # Hier könnte eine Konfigurationsdatei gespeichert werden
            # Für jetzt nur in-memory
            
            self.status_label.setText("Einstellungen gespeichert")
            logging.info("Einstellungen gespeichert")
            
        except Exception as e:
            logging.error(f"Fehler beim Speichern der Einstellungen: {e}")
            self.status_label.setText("Fehler beim Speichern")
            
    def reset_settings(self):
        """Setzt Einstellungen zurück"""
        self.settings = self.load_default_settings()
        self.update_ui_from_settings()
        self.status_label.setText("Einstellungen zurückgesetzt")
        
    def update_ui_from_settings(self):
        """Aktualisiert UI aus Einstellungen"""
        self.interval_spinbox.setValue(self.settings['measurement_interval'])
        self.warning_spinbox.setValue(self.settings['warning_threshold'])
        self.critical_spinbox.setValue(self.settings['critical_threshold'])
        self.history_spinbox.setValue(self.settings['history_days'])
        self.auto_discovery_checkbox.setChecked(self.settings['auto_discovery'])
        self.discovery_interval_spinbox.setValue(self.settings['discovery_interval'])
        self.network_range_edit.setText(self.settings['network_range'])
        self.log_level_combo.setCurrentText(self.settings['log_level'])
        self.alerts_checkbox.setChecked(self.settings['enable_alerts'])
        self.export_path_edit.setText(self.settings['export_path'])
        
    def browse_export_path(self):
        """Öffnet Dialog für Export-Pfad"""
        path = QFileDialog.getExistingDirectory(self, "Export-Pfad wählen")
        if path:
            self.export_path_edit.setText(path)
            self.on_settings_changed()
            
    def export_data(self):
        """Exportiert Temperatur-Daten"""
        try:
            from core.database import TemperatureDatabase
            import csv
            import os
            from datetime import datetime
            
            if not self.settings['export_path']:
                self.status_label.setText("Bitte Export-Pfad festlegen")
                return
                
            db = TemperatureDatabase()
            cameras = db.get_cameras()
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"temperatur_export_{timestamp}.csv"
            filepath = os.path.join(self.settings['export_path'], filename)
            
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['Kamera_IP', 'Kamera_Name', 'Zeitstempel', 'Temperatur'])
                
                for camera in cameras:
                    camera_id, ip, model, serial, name = camera[:5]
                    temp_history = db.get_temperature_history(camera_id, 24 * 30)  # 30 Tage
                    
                    for timestamp_str, temp in temp_history:
                        writer.writerow([ip, name, timestamp_str, temp])
                        
            self.status_label.setText(f"Daten exportiert: {filename}")
            logging.info(f"Temperatur-Daten exportiert nach {filepath}")
            
        except Exception as e:
            logging.error(f"Fehler beim Datenexport: {e}")
            self.status_label.setText("Fehler beim Export")
            
    def get_measurement_interval(self):
        """Gibt aktuelles Messintervall zurück"""
        return self.settings['measurement_interval']
        
    def get_warning_threshold(self):
        """Gibt Warnschwelle zurück"""
        return self.settings['warning_threshold']
        
    def get_critical_threshold(self):
        """Gibt kritische Schwelle zurück"""
        return self.settings['critical_threshold']
