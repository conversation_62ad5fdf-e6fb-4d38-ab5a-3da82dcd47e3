<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_s"></a>- s -</h3><ul>
<li>Save()
: <a class="el" href="a00855.html#a7281f446abf2c472c043334bb25e6fdc">neoapi.Image</a>
</li>
<li>SensorADDigitization()
: <a class="el" href="a00763.html#a8e459c92ea9157e175cfb7c2cfac7d8a">neoapi.FeatureAccess</a>
</li>
<li>SensorCutConfigurationMode()
: <a class="el" href="a00763.html#ab907fe08e5c009d30db0fb4fdc24f62e">neoapi.FeatureAccess</a>
</li>
<li>SensorCutX()
: <a class="el" href="a00763.html#ae37090873d547624e7cb60c6b65b2280">neoapi.FeatureAccess</a>
</li>
<li>SensorCutY()
: <a class="el" href="a00763.html#a0522bf7ee03acacecdc33d1087634343">neoapi.FeatureAccess</a>
</li>
<li>SensorDigitizationTaps()
: <a class="el" href="a00763.html#a15d9ae3b40c1e01a5eb4a8856a10605a">neoapi.FeatureAccess</a>
</li>
<li>SensorEffectCorrection()
: <a class="el" href="a00763.html#a37e2a823592bc7ef429fecc869ad0cc8">neoapi.FeatureAccess</a>
</li>
<li>SensorFiFoOverflowCounter()
: <a class="el" href="a00763.html#a7cf311c7492a70ca467001a2725733a8">neoapi.FeatureAccess</a>
</li>
<li>SensorFiFoOverflowCounterReset()
: <a class="el" href="a00763.html#ac2a2b4dcc78f14e2a93d8a627f040ae3">neoapi.FeatureAccess</a>
</li>
<li>SensorHeight()
: <a class="el" href="a00763.html#afcb181adafe625a7bbffaf4a99c8f852">neoapi.FeatureAccess</a>
</li>
<li>SensorName()
: <a class="el" href="a00763.html#a9cd5a8c17d059127574c79f5c575649e">neoapi.FeatureAccess</a>
</li>
<li>SensorPixelHeight()
: <a class="el" href="a00763.html#a784245031bca894bbe679f9479d015df">neoapi.FeatureAccess</a>
</li>
<li>SensorPixelWidth()
: <a class="el" href="a00763.html#a5876e61a0a50748ee8f7f01d34ec5493">neoapi.FeatureAccess</a>
</li>
<li>SensorShutterMode()
: <a class="el" href="a00763.html#afe50f0353bc4e39f0c719ec400caed30">neoapi.FeatureAccess</a>
</li>
<li>SensorTaps()
: <a class="el" href="a00763.html#a1a2b8a5d1aea4ad4ddea276368129c61">neoapi.FeatureAccess</a>
</li>
<li>SensorWidth()
: <a class="el" href="a00763.html#a260ce202f07fba12b4592ffb362cba2c">neoapi.FeatureAccess</a>
</li>
<li>SequencerConfigurationMode()
: <a class="el" href="a00763.html#a104020a2c94fcb879e28d78e7bec7a98">neoapi.FeatureAccess</a>
</li>
<li>SequencerFeatureEnable()
: <a class="el" href="a00763.html#aee1765d1593159d9686c3af189c6d6f2">neoapi.FeatureAccess</a>
</li>
<li>SequencerFeatureSelector()
: <a class="el" href="a00763.html#a6494d0926a235272b0d186fd5477eefd">neoapi.FeatureAccess</a>
</li>
<li>SequencerMode()
: <a class="el" href="a00763.html#a25188b91a6111da3a39f442bfc305b07">neoapi.FeatureAccess</a>
</li>
<li>SequencerPathSelector()
: <a class="el" href="a00763.html#ab95c7ad1fc62a4ba8ac4b6c8cb33b194">neoapi.FeatureAccess</a>
</li>
<li>SequencerSetActive()
: <a class="el" href="a00763.html#a45361708c2bc52d9f050e6c7adfd9bca">neoapi.FeatureAccess</a>
</li>
<li>SequencerSetLoad()
: <a class="el" href="a00763.html#a8ac30be9e7a4ca79760f949d185d8b89">neoapi.FeatureAccess</a>
</li>
<li>SequencerSetNext()
: <a class="el" href="a00763.html#a4457071f2d41eafc243c880b89542f4a">neoapi.FeatureAccess</a>
</li>
<li>SequencerSetSave()
: <a class="el" href="a00763.html#a095ef281bdbca1cc4bf3c15ae52f65dd">neoapi.FeatureAccess</a>
</li>
<li>SequencerSetSelector()
: <a class="el" href="a00763.html#a89e3f9460eeb2b851b205b04c557c2b1">neoapi.FeatureAccess</a>
</li>
<li>SequencerSetStart()
: <a class="el" href="a00763.html#a5e4db233f07e8c7c6e5ad38f6da10773">neoapi.FeatureAccess</a>
</li>
<li>SequencerTriggerActivation()
: <a class="el" href="a00763.html#a8877be9fb5a9668d3ac69ffc5bb40596">neoapi.FeatureAccess</a>
</li>
<li>SequencerTriggerSource()
: <a class="el" href="a00763.html#a3355126c6e360bc3da77d8259736ba2c">neoapi.FeatureAccess</a>
</li>
<li>Set()
: <a class="el" href="a00839.html#aeccd933c8ada9465659ea7b9a6f72dff">neoapi.BoolFeature</a>
, <a class="el" href="a00095.html#adfe24b96b26e9dc44d8a87b1cb869fbf">neoapi.CAcquisitionMode</a>
, <a class="el" href="a00099.html#a34a433e27e4318e81a96cdaca8efb041">neoapi.CAcquisitionStatusSelector</a>
, <a class="el" href="a00103.html#adec6472bd389eda0f7274b5b5106c164">neoapi.CApertureStatus</a>
, <a class="el" href="a00107.html#aecd581604317fdd6fc971dcfe0b3de3f">neoapi.CAutoFeatureRegionMode</a>
, <a class="el" href="a00111.html#a3711ace96c31a749d2035efc4ade37ef">neoapi.CAutoFeatureRegionReference</a>
, <a class="el" href="a00115.html#ac3f5f8cca6ed6f187e25a957406b621e">neoapi.CAutoFeatureRegionSelector</a>
, <a class="el" href="a00123.html#aa32fdbee36879f4f7643602aa1d08db4">neoapi.CBalanceWhiteAuto</a>
, <a class="el" href="a00127.html#acd06f6bfec2c8f4a3b576bb21ca533ea">neoapi.CBalanceWhiteAutoStatus</a>
, <a class="el" href="a00131.html#a2ed7ef305fd269c95cdc6bf14ee8bb47">neoapi.CBaudrate</a>
, <a class="el" href="a00135.html#a4f5eded112c2d4ba251eb4380ad2fa91">neoapi.CBinningHorizontalMode</a>
, <a class="el" href="a00139.html#a71d821fc47e49590ca7a87d55524d765">neoapi.CBinningSelector</a>
, <a class="el" href="a00143.html#a4ff9ad9035fb0824554e68351fa1df3c">neoapi.CBinningVerticalMode</a>
, <a class="el" href="a00147.html#a81e42bfb8476fc4080d363db7eda44ef">neoapi.CBlackLevelSelector</a>
, <a class="el" href="a00151.html#a7d09d76eddf7a7ab4dfab7a2e4c24cea">neoapi.CBlackSunSuppression</a>
, <a class="el" href="a00715.html#a77702c346b39ab9cd4065d82295c6afa">neoapi.CboCalibrationDataConfigurationMode</a>
, <a class="el" href="a00719.html#a54a36329226b8967181f5a6496dada0b">neoapi.CboCalibrationMatrixSelector</a>
, <a class="el" href="a00723.html#adbabda2f44e6420ad53768d4fa2c4895">neoapi.CboCalibrationMatrixValueSelector</a>
, <a class="el" href="a00727.html#a0868525f8b61e085eaa3b9f476fd72ab">neoapi.CboCalibrationVectorSelector</a>
, <a class="el" href="a00731.html#a390e92a5d50c6c696fd265f9e1269931">neoapi.CboCalibrationVectorValueSelector</a>
, <a class="el" href="a00735.html#a872d9444414e5c9ba52f3eaf27ea9f3b">neoapi.CboGeometryDistortionValueSelector</a>
, <a class="el" href="a00119.html#ac1fe5ae617839b4d188b9379204b6279">neoapi.CBOPFShift</a>
, <a class="el" href="a00155.html#a6ebde5da5d1635698386ef70cb8592b5">neoapi.CBoSequencerEnable</a>
, <a class="el" href="a00159.html#ac4307719ec0ec7950da93828bdd192f8">neoapi.CBoSequencerIOSelector</a>
, <a class="el" href="a00163.html#a6697d29ae87066bec3efbbde8800bd50">neoapi.CBoSequencerMode</a>
, <a class="el" href="a00167.html#a7dac5e56885c4450a7881e6c1d269952">neoapi.CBoSequencerSensorDigitizationTaps</a>
, <a class="el" href="a00171.html#a88fe9004d738eb9466c650504354f1fb">neoapi.CBoSequencerStart</a>
, <a class="el" href="a00739.html#ab681396d1e1b848c42eff6eb4b45ee67">neoapi.CboSerialConfigBaudRate</a>
, <a class="el" href="a00743.html#adc420861e993f2df9fe135bf8368bffe">neoapi.CboSerialConfigDataBits</a>
, <a class="el" href="a00747.html#a01c156f656df59423d28ed9c7b67323d">neoapi.CboSerialConfigParity</a>
, <a class="el" href="a00751.html#a72dc21bf9d6e4f52c4c06bfb78ab5e4a">neoapi.CboSerialConfigStopBits</a>
, <a class="el" href="a00755.html#a90e390993b4e19947b7b20d16bb35026">neoapi.CboSerialMode</a>
, <a class="el" href="a00759.html#a5265472ff3c8986b920212ac72181acd">neoapi.CboSerialSelector</a>
, <a class="el" href="a00175.html#a7a2fbeba9a914d93966a8fa12f4454b5">neoapi.CBrightnessAutoPriority</a>
, <a class="el" href="a00179.html#a7876aa1ff63abc57ae5316a66ac23ac2">neoapi.CBrightnessCorrection</a>
, <a class="el" href="a00183.html#aceda7d51537bde0072fb1d04b7118aa5">neoapi.CCalibrationMatrixColorSelector</a>
, <a class="el" href="a00187.html#a2bd05928c82992bdc8fb43899eb44c4f">neoapi.CCalibrationMatrixValueSelector</a>
, <a class="el" href="a00191.html#ab67e5794953edaa162407b459837d0a1">neoapi.CChunkSelector</a>
, <a class="el" href="a00195.html#a2a7cffc6d196e8526f88ad9aad9acfde">neoapi.CClConfiguration</a>
, <a class="el" href="a00199.html#a29de1f0b9ed2162919cf1b38f49a7900">neoapi.CClTimeSlotsCount</a>
, <a class="el" href="a00203.html#a16a3033a4bc26eb7e65bf5577bb91a48">neoapi.CColorTransformationAuto</a>
, <a class="el" href="a00207.html#aa27173508e63d9f75dbfe7700539a7da">neoapi.CColorTransformationFactoryListSelector</a>
, <a class="el" href="a00211.html#a3ea6b9ef7d1b9bef0611672543d53416">neoapi.CColorTransformationSelector</a>
, <a class="el" href="a00215.html#a7c6bb316915b444a0346124a173bb624">neoapi.CColorTransformationValueSelector</a>
, <a class="el" href="a00219.html#ac2e9b3ceceec4b376f94ca5168ac1a3a">neoapi.CComponentSelector</a>
, <a class="el" href="a00223.html#a4bc9db7aca385d140657c341681f8418">neoapi.CCounterEventActivation</a>
, <a class="el" href="a00227.html#ae960e79a42a8a1584dd5e505a9bca694">neoapi.CCounterEventSource</a>
, <a class="el" href="a00231.html#ae4e206b80d43ecbf0ed748d5b88dbd12">neoapi.CCounterResetActivation</a>
, <a class="el" href="a00235.html#a046f600ab820e1ead828c483d9cd20dd">neoapi.CCounterResetSource</a>
, <a class="el" href="a00239.html#a8ba225dd6a76b75153b56ba8ec230e1f">neoapi.CCounterSelector</a>
, <a class="el" href="a00243.html#a24775cb3f998e0412c5315a88c18d9aa">neoapi.CCustomDataConfigurationMode</a>
, <a class="el" href="a00247.html#a3e02d0d01518551763cf6b948a770484">neoapi.CDecimationHorizontalMode</a>
, <a class="el" href="a00251.html#a316c6ad226264c5b7d7343619ffa0a37">neoapi.CDecimationVerticalMode</a>
, <a class="el" href="a00255.html#a700cf7a56506f5ad9146439898c0e6d3">neoapi.CDefectPixelListSelector</a>
, <a class="el" href="a00259.html#a0b5ebdf7fa97f2908efdb3cd24ab209f">neoapi.CDeviceCharacterSet</a>
, <a class="el" href="a00263.html#a7227cd092fc4d353f1ce3810e6292a60">neoapi.CDeviceClockSelector</a>
, <a class="el" href="a00267.html#aba3f60e2335e9097477918c1cee95534">neoapi.CDeviceFrontUARTSource</a>
, <a class="el" href="a00271.html#af4fd710c67c2be927fb1412ef8419e17">neoapi.CDeviceLicense</a>
, <a class="el" href="a00275.html#a486cd9ff55ec0796f8c68f2f1647ec78">neoapi.CDeviceLicenseTypeSelector</a>
, <a class="el" href="a00279.html#a28496904d505c7bc3d0d8345241e2ef1">neoapi.CDeviceLinkHeartbeatMode</a>
, <a class="el" href="a00283.html#a75e423df6fb8f4561fd7e22b129cbed3">neoapi.CDeviceLinkSelector</a>
, <a class="el" href="a00287.html#a6327f90658bfc1b0b06bd67f4e03749f">neoapi.CDeviceLinkThroughputLimitMode</a>
, <a class="el" href="a00291.html#a8ecbc02f8854e6d8ea12bdf63447a979">neoapi.CDeviceRegistersEndianness</a>
, <a class="el" href="a00295.html#a688ddb779b18b6e12de40c8d7ebef0a7">neoapi.CDeviceScanType</a>
, <a class="el" href="a00299.html#ab89801cae1f0731576dc5f9c2f4f4f7c">neoapi.CDeviceSensorSelector</a>
, <a class="el" href="a00303.html#a7b479d98a161d47212df2c0b2fb84df8">neoapi.CDeviceSensorType</a>
, <a class="el" href="a00307.html#a667d25ca62b7e02217160ba11206ef57">neoapi.CDeviceSensorVersion</a>
, <a class="el" href="a00311.html#abc6c592c342f3b5b39d32aaa69fee4cd">neoapi.CDeviceSerialPortBaudRate</a>
, <a class="el" href="a00315.html#add8e0101cbfef640ec1ffa17b8fe51f6">neoapi.CDeviceSerialPortSelector</a>
, <a class="el" href="a00319.html#a6418830566191ac6f2153fbe101f3154">neoapi.CDeviceStreamChannelEndianness</a>
, <a class="el" href="a00323.html#a6af2516ed817bc61ec2b6f6a3ab8a7cb">neoapi.CDeviceStreamChannelType</a>
, <a class="el" href="a00331.html#ae046f4510ba11a83a57d5f6c24bd3ea3">neoapi.CDeviceTapGeometry</a>
, <a class="el" href="a00335.html#a4b27709e79ee072df14d451c6d152356">neoapi.CDeviceTemperatureSelector</a>
, <a class="el" href="a00339.html#ac213a436959aa4f1366e6ee3c38e3246">neoapi.CDeviceTemperatureStatus</a>
, <a class="el" href="a00343.html#adc1e316d5e5a01c04bcf6e05a5ee9a8c">neoapi.CDeviceTemperatureStatusTransitionSelector</a>
, <a class="el" href="a00327.html#a3f0acd97e7c2035eb76ef310bb9d1d3d">neoapi.CDeviceTLType</a>
, <a class="el" href="a00347.html#a2f8a9e91064463ad6f66b40a9ce7769e">neoapi.CDeviceType</a>
, <a class="el" href="a00351.html#a768b0ee805faa072c1d5df9f85b0b05e">neoapi.CEventNotification</a>
, <a class="el" href="a00355.html#a807532da11b446aecf32712d2f4b7e83">neoapi.CEventSelector</a>
, <a class="el" href="a00359.html#a4eebf393af997230fa67c556d46cf91e">neoapi.CExposureAuto</a>
, <a class="el" href="a00363.html#a1dd32aa85a3419da3a05f2131bf877b0">neoapi.CExposureMode</a>
, <a class="el" href="a00367.html#af35bc0a6516bcc6a19d38f2615e33025">neoapi.CFileOpenMode</a>
, <a class="el" href="a00371.html#a1f00186b3dca7bae23a7187b4acad71b">neoapi.CFileOperationSelector</a>
, <a class="el" href="a00375.html#a2c396ac9d60cc59323216edf298701aa">neoapi.CFileOperationStatus</a>
, <a class="el" href="a00379.html#a3527980c5f51549aed4ad24fb00a9143">neoapi.CFileSelector</a>
, <a class="el" href="a00383.html#a4a5de8d354b1d994de0b66942996b64f">neoapi.CFocalLengthStatus</a>
, <a class="el" href="a00387.html#af24963641217c1233c3a210169aa7507">neoapi.CFocusStatus</a>
, <a class="el" href="a00391.html#aeb27d71750121c91169a9b95586f0465">neoapi.CGainAuto</a>
, <a class="el" href="a00395.html#a99417d8d639efa624eae89a0bb0f1163">neoapi.CGainSelector</a>
, <a class="el" href="a00399.html#a0db263ab17da79148c39dcef82bf3859">neoapi.CGenDCStreamingMode</a>
, <a class="el" href="a00403.html#a3a900096b3cd5f1cb56ba64f309c329d">neoapi.CGenDCStreamingStatus</a>
, <a class="el" href="a00407.html#af6284a055687c91276689e077310cbad">neoapi.CGevCCP</a>
, <a class="el" href="a00411.html#a80e3efff003847e935fb87fe92726a23">neoapi.CGevGVCPExtendedStatusCodesSelector</a>
, <a class="el" href="a00415.html#ad06bca2597972f45f58df2f6f10e8dd7">neoapi.CGevIPConfigurationStatus</a>
, <a class="el" href="a00419.html#a617347fa698404416807a94992bffb49">neoapi.CGevSupportedOptionSelector</a>
, <a class="el" href="a00423.html#a7acb99c633c25bd6b826d120a1d4331d">neoapi.CHDRGainRatioSelector</a>
, <a class="el" href="a00427.html#a0f401496a5c68fc73989e642c5ecb21c">neoapi.CHDRTonemappingCurvePresetSelector</a>
, <a class="el" href="a00431.html#ae1213281bb5e57866aef55fcdec32488">neoapi.CImageCompressionJPEGFormatOption</a>
, <a class="el" href="a00435.html#af0c74d1375a82233eec86d48dcf217e3">neoapi.CImageCompressionMode</a>
, <a class="el" href="a00439.html#a82bb12a6a8700630446b9101f1d5f649">neoapi.CImageCompressionRateOption</a>
, <a class="el" href="a00443.html#aabe4df433ed2ddaf2346ea44258350ec">neoapi.CInterfaceSpeedMode</a>
, <a class="el" href="a00455.html#a2220580e5cd60c61b021a2e6f25b4bd3">neoapi.CLineFormat</a>
, <a class="el" href="a00459.html#ae9c65468d41a679c3c400aa9e8e87e77">neoapi.CLineMode</a>
, <a class="el" href="a00463.html#a9a2417e0c8fcb293d426d633228c632b">neoapi.CLinePWMConfigurationMode</a>
, <a class="el" href="a00467.html#a20598ea34063be4acbe59089fc7ac6a3">neoapi.CLinePWMMode</a>
, <a class="el" href="a00471.html#a8bbf6703cb7618c310b1fba1bc4364dd">neoapi.CLineSelector</a>
, <a class="el" href="a00475.html#a459be0477ae9718f766a471fb1a5673f">neoapi.CLineSource</a>
, <a class="el" href="a00447.html#ab98c94953910a69d30abcb285c899f0b">neoapi.CLUTContent</a>
, <a class="el" href="a00451.html#aee670b547a3e7df35954fe6b094883b7">neoapi.CLUTSelector</a>
, <a class="el" href="a00479.html#ab169b2b62c2656e6df857ec72e09a59b">neoapi.CMemoryActivePart</a>
, <a class="el" href="a00483.html#aed748b506fbe81b2be56219661ed73ea">neoapi.CMemoryMode</a>
, <a class="el" href="a00487.html#aac9a93fe24e4294d5c0ed8fdddf1828d">neoapi.CMemoryPartIncrementSource</a>
, <a class="el" href="a00491.html#aff8d36f32e1d31751b9bd718314185f7">neoapi.CMemoryPartMode</a>
, <a class="el" href="a00495.html#a8fdcaf796ef43d4c688202bd6b24bec1">neoapi.CMemoryPartSelector</a>
, <a class="el" href="a00499.html#a4d49ec92b7d27c56952e7852160eed31">neoapi.COpticControllerSelector</a>
, <a class="el" href="a00503.html#af0113eda44a2b227bdec039f27cfd184">neoapi.COpticControllerStatus</a>
, <a class="el" href="a00507.html#a354894ef2de349c6e7827ff55c5b0283">neoapi.CPartialScanEnabled</a>
, <a class="el" href="a00511.html#a85f372845a235118316e84d418c537bb">neoapi.CPixelFormat</a>
, <a class="el" href="a00515.html#a2b7e18a5cca2f5e095199ace511e6ae1">neoapi.CPtpClockAccuracy</a>
, <a class="el" href="a00519.html#a98ffec8a93e05597d3945a5439762f7d">neoapi.CPtpClockOffsetMode</a>
, <a class="el" href="a00523.html#ac49c3816e15d4b5415547fb846f975d7">neoapi.CPtpDriftOffsetMode</a>
, <a class="el" href="a00527.html#a5a8a8cdcd0ea68a1941ffaf69adaf35f">neoapi.CPtpMode</a>
, <a class="el" href="a00531.html#a0aa824e807a21f31fbc301c69b4fee2c">neoapi.CPtpServoStatus</a>
, <a class="el" href="a00535.html#a6c0815d61fc6db7b6d4700981a3d92f6">neoapi.CPtpStatus</a>
, <a class="el" href="a00539.html#a4f59680eb2cb1896eaeeaa52a87775b7">neoapi.CPtpSyncMessageIntervalStatus</a>
, <a class="el" href="a00543.html#a12a728ca9447dd098c26687653e2452e">neoapi.CPtpTimestampOffsetMode</a>
, <a class="el" href="a00547.html#a754d38fbc51971257879b7c7d392a7a4">neoapi.CReadOutBuffering</a>
, <a class="el" href="a00551.html#ae093401972091b17a99262f5378bd671">neoapi.CReadoutMode</a>
, <a class="el" href="a00555.html#a8052928631a991c3e675cc5730e05bfd">neoapi.CRegionAcquisitionMode</a>
, <a class="el" href="a00559.html#a7b3649ad7e60080720bb601133637b32">neoapi.CRegionConfigurationMode</a>
, <a class="el" href="a00563.html#a978944adc8169224ae8a9727d9b6f762">neoapi.CRegionMode</a>
, <a class="el" href="a00567.html#aac4b7fc579ed45f81a938b590b7d3af9">neoapi.CRegionSelector</a>
, <a class="el" href="a00571.html#a556f94e882066f8788431d7cd4772df6">neoapi.CRegionTransferMode</a>
, <a class="el" href="a00579.html#a9cf9a8189071c1e5b9655b710e55d44f">neoapi.CSensorADDigitization</a>
, <a class="el" href="a00583.html#ae9ca531f85f92aceec60ae4f013a21be">neoapi.CSensorCutConfigurationMode</a>
, <a class="el" href="a00587.html#ae6aedf1c1edd38da68dfa00375a9888a">neoapi.CSensorDigitizationTaps</a>
, <a class="el" href="a00591.html#ab4ae190317f40dd6615434cfd63d621f">neoapi.CSensorShutterMode</a>
, <a class="el" href="a00595.html#af57c5cefcd6a926471a422ac3ef05e7e">neoapi.CSensorTaps</a>
, <a class="el" href="a00599.html#a3eebd0bcc067ea59bcb17bd708695900">neoapi.CSequencerConfigurationMode</a>
, <a class="el" href="a00603.html#a1d86aec80cb2f2509920548c43e3cb51">neoapi.CSequencerFeatureSelector</a>
, <a class="el" href="a00607.html#a844db0f66911640321e143ce40a5aa3b">neoapi.CSequencerMode</a>
, <a class="el" href="a00611.html#a2ff3a93e04d2db8ed9ac10ff4fae5d03">neoapi.CSequencerTriggerActivation</a>
, <a class="el" href="a00615.html#a1783bac4499a375eff16a057957ad194">neoapi.CSequencerTriggerSource</a>
, <a class="el" href="a00619.html#a1c3020f5904320b4a406550534e3366f">neoapi.CShadingSelector</a>
, <a class="el" href="a00623.html#a6b35bc96eeecf41248d0a119393660e2">neoapi.CSharpeningMode</a>
, <a class="el" href="a00575.html#ab0a3dcee5116b65a0729d9bd77bd5445">neoapi.CSIControl</a>
, <a class="el" href="a00627.html#a8269165d283d5232e05519ca339893b1">neoapi.CSourceID</a>
, <a class="el" href="a00631.html#af1c5efdfe38851b2d7dbb86a87bc4c23">neoapi.CSourceSelector</a>
, <a class="el" href="a00635.html#a34ba9b01711621ebfbdef77104a5568b">neoapi.CSwitchPortSelector</a>
, <a class="el" href="a00639.html#a88d4d8ea67495fa88504a679e6ffa8b5">neoapi.CTestPattern</a>
, <a class="el" href="a00643.html#a391ce10aaff11070dc3f6b0c8e78d072">neoapi.CTestPatternGeneratorSelector</a>
, <a class="el" href="a00647.html#a56ba38dd066ea7b99e02f316456e523a">neoapi.CTestPayloadFormatMode</a>
, <a class="el" href="a00651.html#acd9991b01dc115f96ee8327b9044580f">neoapi.CTimerSelector</a>
, <a class="el" href="a00655.html#ac531796cf1a2d57f86056e8dff5ff83e">neoapi.CTimerTriggerActivation</a>
, <a class="el" href="a00659.html#aaee7127f6b337fa876935b3d8a09d773">neoapi.CTimerTriggerSource</a>
, <a class="el" href="a00663.html#a940b2db233a0f4347ad4a609098202e1">neoapi.CTransferControlMode</a>
, <a class="el" href="a00667.html#ad92dc0bbb015bad6c7232b189291e06e">neoapi.CTransferOperationMode</a>
, <a class="el" href="a00671.html#ae68c17574ee66c54c66236a00967916a">neoapi.CTransferSelector</a>
, <a class="el" href="a00675.html#adb0f978e16dffc70049981c140fc1b76">neoapi.CTransferStatusSelector</a>
, <a class="el" href="a00679.html#a9c318832ce68c32a4f2cdc017e1491e6">neoapi.CTriggerActivation</a>
, <a class="el" href="a00683.html#a1ed7b531e6d03deb5b678664c1b38f26">neoapi.CTriggerMode</a>
, <a class="el" href="a00687.html#a4e95c702b61913af8d8f19f74494d076">neoapi.CTriggerOverlap</a>
, <a class="el" href="a00691.html#a67cf3111f69a924065a2581035dee340">neoapi.CTriggerSelector</a>
, <a class="el" href="a00695.html#a794e820782c96bf610b676f5b3fff04c">neoapi.CTriggerSource</a>
, <a class="el" href="a00699.html#af62482f7e58f0a0401da9981a1ef52a4">neoapi.CUserOutputSelector</a>
, <a class="el" href="a00703.html#a7de6d420ff96cd19183054941a722645">neoapi.CUserSetDefault</a>
, <a class="el" href="a00707.html#a0cb4d7eae6edd499cc9f2db636001740">neoapi.CUserSetFeatureSelector</a>
, <a class="el" href="a00711.html#a41ac4f5e5369cc61035ee90b91f20da7">neoapi.CUserSetSelector</a>
, <a class="el" href="a00827.html#a5157b897ea8163ab56586e995ae62c84">neoapi.DoubleFeature</a>
, <a class="el" href="a00831.html#a46bc37108605e41fb6fe83d5f5165a93">neoapi.IntegerFeature</a>
</li>
<li>SetAdjustFeatureValueMode()
: <a class="el" href="a00863.html#a3299965f197821f2de6f63b43eefd251">neoapi.Cam</a>
, <a class="el" href="a00859.html#ace4716e7126adb8e85bcc5e928ce87a7">neoapi.CamBase</a>
</li>
<li>SetBool()
: <a class="el" href="a00811.html#a5b4b8294c5c3def133e1e6070689d81d">neoapi.Feature</a>
</li>
<li>SetColorTransformationMatrix()
: <a class="el" href="a00799.html#a3712de0c8e33df788d575470c67fdcc2">neoapi.ConverterSettings</a>
</li>
<li>SetDebayerFormat()
: <a class="el" href="a00799.html#a2a55273091ee46cfb3a92178007acba9">neoapi.ConverterSettings</a>
</li>
<li>SetDemosaicingMethod()
: <a class="el" href="a00799.html#a65ae9893b226ed4e6c44673f0eeed82b">neoapi.ConverterSettings</a>
</li>
<li>SetDouble()
: <a class="el" href="a00811.html#a9be4d0cfa11eec57556f4b7b3bcbc626">neoapi.Feature</a>
</li>
<li>SetFeature()
: <a class="el" href="a00863.html#a196586403385f1c09291a129e712cb62">neoapi.Cam</a>
, <a class="el" href="a00859.html#a3e8944a4aa1f5c87a9b95b391d278945">neoapi.CamBase</a>
</li>
<li>SetImageBufferCount()
: <a class="el" href="a00863.html#a2f19563bca5378be0b09e23dc1fd70d1">neoapi.Cam</a>
, <a class="el" href="a00859.html#ab5ad7ce815a8397b5073767ed344a896">neoapi.CamBase</a>
</li>
<li>SetImageBufferCycleCount()
: <a class="el" href="a00863.html#af2f0ac4b47d3afdb2f90c42891b8909e">neoapi.Cam</a>
, <a class="el" href="a00859.html#ad6dc663d08df99799ec4f89a70a8bf89">neoapi.CamBase</a>
</li>
<li>SetInt()
: <a class="el" href="a00847.html#a0bcb8c3b80260c8d0cde108eaf365be4">neoapi.EnumerationFeature</a>
, <a class="el" href="a00811.html#a75e03ac71173b851ae10ccdba70d0399">neoapi.Feature</a>
</li>
<li>SetRegister()
: <a class="el" href="a00811.html#afee1c8bcca5a1e4edbee79c441217fdd">neoapi.Feature</a>
, <a class="el" href="a00851.html#ab0cb288f7e2082580298dc6bd5c8ed9d">neoapi.RegisterFeature</a>
</li>
<li>SetReplaceMode()
: <a class="el" href="a00867.html#a748784192fb01e27b6d4c6e3718b23ff">neoapi.FeatureStack</a>
</li>
<li>SetSeverity()
: <a class="el" href="a00899.html#adf653fbda7506a4664adcfb0be61b473">neoapi.NeoTrace</a>
</li>
<li>SetSharpeningFactor()
: <a class="el" href="a00799.html#ab3ab541c2c53e4e4b3a5fb5e64ec222d">neoapi.ConverterSettings</a>
</li>
<li>SetSharpeningMode()
: <a class="el" href="a00799.html#a0357a33e15928d69aeaaba69f0051184">neoapi.ConverterSettings</a>
</li>
<li>SetSharpeningSensitivityThreshold()
: <a class="el" href="a00799.html#a392cb2ffe95118e605b5cc076a1802f3">neoapi.ConverterSettings</a>
</li>
<li>SetString()
: <a class="el" href="a00847.html#a82a850e2d931254434f9a8e454fcb536">neoapi.EnumerationFeature</a>
, <a class="el" href="a00811.html#ab8402ad968e3bd0c4ef41a517224576f">neoapi.Feature</a>
, <a class="el" href="a00835.html#a71438e70944a46ff83cd93885107c53f">neoapi.StringFeature</a>
</li>
<li>SetSynchronFeatureMode()
: <a class="el" href="a00863.html#ab14f94f0300b589b9a502a592c1cf7d5">neoapi.Cam</a>
, <a class="el" href="a00859.html#a068ff88af2a49cfade6f63f23339251e">neoapi.CamBase</a>
</li>
<li>SetUserBufferMode()
: <a class="el" href="a00863.html#ae93a07bcc61563515507f4a726a1bcdc">neoapi.Cam</a>
, <a class="el" href="a00859.html#a8c378141255469a625d0005afcce88c6">neoapi.CamBase</a>
</li>
<li>SetValue()
: <a class="el" href="a00795.html#a37db09b0787505c9db9d383279fb54e2">neoapi.ColorMatrix</a>
, <a class="el" href="a00811.html#af9ace9058d51b65cbecd9297a245c9f6">neoapi.Feature</a>
</li>
<li>ShadingCalibrationStart()
: <a class="el" href="a00763.html#a0fd16660713a00630566a22dec18db2e">neoapi.FeatureAccess</a>
</li>
<li>ShadingEnable()
: <a class="el" href="a00763.html#a5ce8aece68798c5ea835ad0380798b93">neoapi.FeatureAccess</a>
</li>
<li>ShadingMaxGain()
: <a class="el" href="a00763.html#aea8d980b5ee879e2f5b2e74093c57b5c">neoapi.FeatureAccess</a>
</li>
<li>ShadingPortAddress()
: <a class="el" href="a00763.html#a48d2b486939a11d0fcca5cdb7926b411">neoapi.FeatureAccess</a>
</li>
<li>ShadingSelector()
: <a class="el" href="a00763.html#a9dd99e324899280a18383ccf53317c32">neoapi.FeatureAccess</a>
</li>
<li>ShadingXBorder()
: <a class="el" href="a00763.html#a1db9dd72671128d8354d4516178d1bb6">neoapi.FeatureAccess</a>
</li>
<li>ShadingYBorder()
: <a class="el" href="a00763.html#a442d7cacc683b84d21345df33f2bf41c">neoapi.FeatureAccess</a>
</li>
<li>SharpeningEnable()
: <a class="el" href="a00763.html#adf40d32b143c3f266200497f89a00632">neoapi.FeatureAccess</a>
</li>
<li>SharpeningFactor()
: <a class="el" href="a00763.html#a5a0c069b7bb433e59173647b1134fd61">neoapi.FeatureAccess</a>
</li>
<li>SharpeningMode()
: <a class="el" href="a00763.html#ad95cdaade620b3968a108097420be840">neoapi.FeatureAccess</a>
</li>
<li>SharpeningSensitivityThreshold()
: <a class="el" href="a00763.html#abf806ea2ae7c49db52404e3b54eedcda">neoapi.FeatureAccess</a>
</li>
<li>SharpeningSensitvityThreshold()
: <a class="el" href="a00763.html#a87ff24be6cc81a2eb1f0564f1a694de6">neoapi.FeatureAccess</a>
</li>
<li>ShortExposureTimeEnable()
: <a class="el" href="a00763.html#ab0a9561b54af29d579222541a1998215">neoapi.FeatureAccess</a>
</li>
<li>ShutterLineCorrectionEnable()
: <a class="el" href="a00763.html#aa0b5415a7ba2099b087697fb1a0e0a24">neoapi.FeatureAccess</a>
</li>
<li>ShutterLineOffsetEven()
: <a class="el" href="a00763.html#a0c3a55b867057ebd4577b6dbedab603e">neoapi.FeatureAccess</a>
</li>
<li>ShutterLineOffsetOdd()
: <a class="el" href="a00763.html#aa5725572c71d73508121c87f3b9879bf">neoapi.FeatureAccess</a>
</li>
<li>SIControl()
: <a class="el" href="a00763.html#aa67631404621342bf9aa7788c83ce37a">neoapi.FeatureAccess</a>
</li>
<li>SIPayloadFinalTransfer1Size()
: <a class="el" href="a00763.html#abf125423da5fc28e467f81df87259373">neoapi.FeatureAccess</a>
</li>
<li>SIPayloadFinalTransfer2Size()
: <a class="el" href="a00763.html#a129d3c85270b8fad6ede80069ba56d5a">neoapi.FeatureAccess</a>
</li>
<li>SIPayloadTransferCount()
: <a class="el" href="a00763.html#af512282597cbaf4841fd92dd7068b3d5">neoapi.FeatureAccess</a>
</li>
<li>SIPayloadTransferSize()
: <a class="el" href="a00763.html#a9feb59a5633dfba44e23cc5ade296438">neoapi.FeatureAccess</a>
</li>
<li>size()
: <a class="el" href="a00891.html#a82ac0ff93e40ce0631ece40948286647">neoapi.CamInfoList</a>
</li>
<li>SourceCount()
: <a class="el" href="a00763.html#add1c557a0cfb98a88b9d338f9c18f5d7">neoapi.FeatureAccess</a>
</li>
<li>SourceID()
: <a class="el" href="a00763.html#ae48b89d2c1dab439e0bd8eec8e74c2e3">neoapi.FeatureAccess</a>
</li>
<li>SourceIDValue()
: <a class="el" href="a00763.html#a56ee78eaeae976f540e60a24381760f5">neoapi.FeatureAccess</a>
</li>
<li>SourceSelector()
: <a class="el" href="a00763.html#af2c266c8c171a9a97bdfe4525569b8f6">neoapi.FeatureAccess</a>
</li>
<li>StartStreaming()
: <a class="el" href="a00863.html#ab47c16af10208521e0191267cd4b72b6">neoapi.Cam</a>
, <a class="el" href="a00859.html#a79a59c8640be69d19e9e312694fbec65">neoapi.CamBase</a>
</li>
<li>StopStreaming()
: <a class="el" href="a00863.html#a3c29d9505242b8f6c3bf790ef0e11d11">neoapi.Cam</a>
, <a class="el" href="a00859.html#abd8483f382bcb2857267e184ef661af0">neoapi.CamBase</a>
</li>
<li>SwitchMACAddressTableEntryIsValid()
: <a class="el" href="a00763.html#a3bb5a54f3637362996b5aaa17c189ce3">neoapi.FeatureAccess</a>
</li>
<li>SwitchMACAddressTableEntryMACAddress()
: <a class="el" href="a00763.html#a34792f62343ea12386d41cacdc92d217">neoapi.FeatureAccess</a>
</li>
<li>SwitchMACAddressTableEntryPortNumber()
: <a class="el" href="a00763.html#abfc489ffdacd67ae9c4a063e631b7830">neoapi.FeatureAccess</a>
</li>
<li>SwitchMACAddressTableEntrySelector()
: <a class="el" href="a00763.html#a40990805b31f0cef7c7b9305f57e61f4">neoapi.FeatureAccess</a>
</li>
<li>SwitchNumberOfMACAddresses()
: <a class="el" href="a00763.html#a7c6fc15607c2a0272487d5f275f0a157">neoapi.FeatureAccess</a>
</li>
<li>SwitchNumberOfPorts()
: <a class="el" href="a00763.html#aa47f96dd3ceabc6c1b6b945123a3c578">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortBroadcastValidCounter()
: <a class="el" href="a00763.html#ab31fd6eb7eff1c87bec9be8ef05754d1">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortBufferFullCounter()
: <a class="el" href="a00763.html#a617dac9f3a997345cd016cb3f0d66913">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortBufferSize()
: <a class="el" href="a00763.html#a25d1534520f5d2a51001464994c14571">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortCRCErrorCounter()
: <a class="el" href="a00763.html#a1c1a269107febfdfaff7c58354ac004e">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortMulticastValidCounter()
: <a class="el" href="a00763.html#ac160dc2cfd7066defffaf06734ddceca">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortNumberOfBuffers()
: <a class="el" href="a00763.html#a357478aa5f20613983ba1ffa29a26e90">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortPacketSizeErrorCounter()
: <a class="el" href="a00763.html#ae0a4d4a614a6a1ec1bb096b1aa8ad6fa">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortPAUSEFrameReceptionCounter()
: <a class="el" href="a00763.html#a961d33b62ebe66ba415cfcceb03af3b5">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortSelector()
: <a class="el" href="a00763.html#ac068b732623d3e3adc584f9ce47e9a51">neoapi.FeatureAccess</a>
</li>
<li>SwitchPortUnicastValidCounter()
: <a class="el" href="a00763.html#aa0e60422f76685f9ed78ac6aadcae85e">neoapi.FeatureAccess</a>
</li>
<li>SwitchVersion()
: <a class="el" href="a00763.html#ac7f9be5e6bd5293a72e8d7bf2a9b0909">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
