#!/usr/bin/env python3
"""
Erstellt portables Paket für Baumer Temperatur Monitor
"""

import os
import shutil
from datetime import datetime

def create_portable_package():
    """Erstellt portables Paket"""
    print("=== ERSTELLE PORTABLES PAKET ===")
    
    try:
        # Erstelle Paket-Ordner
        package_dir = "BaumerTemperaturMonitor_Portable"
        if os.path.exists(package_dir):
            shutil.rmtree(package_dir)
        
        os.makedirs(package_dir)
        print(f"✓ Paket-Ordner erstellt: {package_dir}")
        
        # Kopiere EXE (verwende die standalone Version)
        exe_source = "dist/BaumerTemperaturMonitor_Standalone.exe"
        if os.path.exists(exe_source):
            shutil.copy2(exe_source, f"{package_dir}/BaumerTemperaturMonitor_Standalone.exe")
            
            # Prüfe EXE-Größe
            size_mb = os.path.getsize(f"{package_dir}/BaumerTemperaturMonitor_Standalone.exe") / (1024 * 1024)
            print(f"✓ EXE kopiert ({size_mb:.1f} MB)")
        else:
            print("✗ EXE-Datei nicht gefunden!")
            return False
        
        # Erstelle Ordner-Struktur
        os.makedirs(f"{package_dir}/logs")
        os.makedirs(f"{package_dir}/data")
        print("✓ Ordner-Struktur erstellt")
        
        # Erstelle README
        readme_content = f"""# Baumer Temperatur Monitor - Portable Version

## 🚀 SOFORT EINSATZBEREIT!
Keine Installation erforderlich! Einfach BaumerTemperaturMonitor.exe starten.

## 📋 BEKANNTE BAUMER-KAMERAS:
Das Programm ist vorkonfiguriert für diese Kamera-IPs:
- *************** (VCXG-13M)
- ************** (VCXG-13M)
- ************** (VCXG-13M)
- ************** (VCXG-13M)

## 🎯 VERWENDUNG:
1. BaumerTemperaturMonitor.exe doppelklicken
2. "Alle bekannten Kameras testen" klicken
3. Erreichbare Kameras werden angezeigt
4. Kameras auswählen und "Verbinden" klicken
5. Temperatur-Überwachung startet automatisch

## 🔧 SCAN-OPTIONEN:
- **Alle bekannten Kameras testen** ⭐ (Empfohlen)
- **Gezielter Scan** (bekannte Bereiche)
- **Schneller Scan** (1-50)
- **Vollständiger Scan** (1-254)

## 🌐 NETZWERK-KONFIGURATION:
- Ethernet 5 Verbindung zu Baumer-Kameras
- 169.254.x.x Netzwerk (APIPA/Link-Local)
- Keine Baumer-Treiber erforderlich
- Funktioniert auch ohne Baumer-Software

## 💻 SYSTEMANFORDERUNGEN:
- Windows 7/8/10/11 (64-bit)
- Ethernet-Verbindung zu den Kameras
- Keine Python-Installation erforderlich
- Keine zusätzliche Software erforderlich

## 📁 ORDNER-STRUKTUR:
- BaumerTemperaturMonitor.exe - Hauptprogramm
- Start.bat - Schnellstart-Datei
- logs/ - System-Logs und Fehlermeldungen
- data/ - Temperatur-Datenbank (SQLite)
- README.txt - Diese Anleitung

## 🔍 FEHLERBEHEBUNG:
1. **Kameras nicht gefunden:**
   - Prüfen Sie die Ethernet 5 Verbindung
   - Stellen Sie sicher, dass die Kameras eingeschaltet sind
   - Verwenden Sie "Gezielter Scan" für bekannte Bereiche

2. **Programm startet nicht:**
   - Rechtsklick auf .exe → "Als Administrator ausführen"
   - Windows Defender/Antivirus temporär deaktivieren
   - Prüfen Sie die logs/ für Fehlermeldungen

3. **Temperatur-Daten fehlen:**
   - Prüfen Sie die Kamera-Verbindung
   - Verwenden Sie "Temperatur testen" Button
   - Logs prüfen für API-Fehler

## 📊 FEATURES:
✅ Automatische Kamera-Erkennung
✅ Echtzeit-Temperatur-Überwachung  
✅ Historische Daten-Speicherung
✅ Grafische Benutzeroberfläche
✅ Portable - keine Installation
✅ Keine Treiber erforderlich

## 📞 SUPPORT:
Bei Problemen prüfen Sie:
1. logs/ethernet5_log_YYYYMMDD_HHMMSS.txt
2. System-Log im Programm (Log-Tab)
3. Windows Ereignisanzeige

## 📅 VERSION INFORMATION:
- Version: 1.0.5
- Build-Datum: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- Plattform: Windows 64-bit
- Python: Eingebettet (keine Installation erforderlich)
- GUI: PyQt5 (eingebettet)

## 🎉 VIEL ERFOLG!
Das Programm ist bereit für die Überwachung Ihrer Baumer-Kameras!
"""
        
        with open(f"{package_dir}/README.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✓ README.txt erstellt")
        
        # Erstelle Batch-Datei
        batch_content = """@echo off
title Baumer Temperatur Monitor
echo.
echo ========================================
echo   Baumer Temperatur Monitor v1.0.5
echo ========================================
echo.
echo Starte Baumer Temperatur Monitor...
echo.
echo Bekannte Kameras:
echo - ***************
echo - **************
echo - **************
echo - **************
echo.
echo Programm wird gestartet...
echo.

BaumerTemperaturMonitor_Standalone.exe

echo.
echo Programm beendet.
pause"""
        
        with open(f"{package_dir}/Start.bat", 'w', encoding='utf-8') as f:
            f.write(batch_content)
        print("✓ Start.bat erstellt")
        
        # Erstelle Installations-Info
        install_info = f"""# Installation und Deployment

## Portable Paket erstellt:
- Datum: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- EXE-Größe: {size_mb:.1f} MB
- Python-Version: Eingebettet
- PyQt5: Eingebettet

## Deployment:
1. Kopieren Sie den kompletten Ordner "BaumerTemperaturMonitor_Portable"
2. Auf Ziel-PC: Doppelklick auf BaumerTemperaturMonitor.exe
3. Keine weitere Installation erforderlich

## Getestet auf:
- Windows 11 (Build-System)
- Ethernet 5 Netzwerk-Adapter
- 169.254.x.x IP-Bereich

## Nächste Schritte:
1. Testen Sie die EXE auf dem Ziel-PC
2. Prüfen Sie die Kamera-Verbindungen
3. Verwenden Sie "Alle bekannten Kameras testen"
"""
        
        with open(f"{package_dir}/INSTALLATION.txt", 'w', encoding='utf-8') as f:
            f.write(install_info)
        print("✓ INSTALLATION.txt erstellt")
        
        print(f"\n✅ PORTABLES PAKET ERFOLGREICH ERSTELLT!")
        print(f"📁 Ordner: {package_dir}/")
        print(f"🚀 Hauptprogramm: {package_dir}/BaumerTemperaturMonitor.exe")
        print(f"📋 Anleitung: {package_dir}/README.txt")
        print(f"⚡ Schnellstart: {package_dir}/Start.bat")
        
        return True
        
    except Exception as e:
        print(f"✗ Fehler beim Erstellen des Pakets: {e}")
        return False

def main():
    """Hauptfunktion"""
    print("Baumer Temperatur Monitor - Portable Package Creator")
    print("=" * 60)
    
    if not os.path.exists("dist/BaumerTemperaturMonitor.exe"):
        print("✗ EXE-Datei nicht gefunden!")
        print("Führen Sie zuerst PyInstaller aus:")
        print("py -m PyInstaller --onefile --windowed --name=BaumerTemperaturMonitor ethernet5_main.py")
        return False
    
    success = create_portable_package()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 DEPLOYMENT BEREIT!")
        print("=" * 60)
        print("Das portable Paket kann jetzt auf jeden Windows-PC kopiert werden.")
        print("Keine Python-Installation oder Baumer-Treiber erforderlich!")
        print("\nTesten Sie die EXE auf dem Ziel-PC mit den echten Kameras.")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\nDrücken Sie Enter zum Beenden...")
