<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Member List</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00883.html">CamInfo</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">neoapi.CamInfo Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="a00883.html">neoapi.CamInfo</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="a00883.html#a1444e63e26d810f16085eee8e5c1e4dc">__init__</a>(self, *args)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00883.html#a6a0352d41af150acea7033ec7cffe4cf">GetGevGateway</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00883.html#a175411ea401a6181a618fc17d2e5679c">GetGevIpAddress</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00883.html#ab16917727ebcd2c47cd17bae14f8ee90">GetGevMACAddress</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00883.html#a4d8c1ef2009f95f8c0f1151d3aa6e81b">GetGevSubnetMask</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00883.html#a3b280fc28767fa40b626fa7d55e05e22">GetId</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00883.html#ad5b279a8ae9ce9fe893d619a8cd94d40">GetModelName</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00883.html#a9a7e2ddc7afebf27dfaf9f39142ae004">GetSerialNumber</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00883.html#a37322389fc2a46340e7b5a2127b8d5fd">GetTLType</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00883.html#a22ac983ae10d350dc8ddfc267550e81c">GetUSB3VisionGUID</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00883.html#a5de8d52a362cb493cd9e59ac694c835c">GetUSBPortID</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00883.html#ab32d45aaec3dd88fd8710536cd3992e9">GetVendorName</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00883.html#a27191e69df494ad411b19603074cece0">IsConnectable</a>(self)</td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00883.html#a58c833eb8d0490277872e0ed78e29534">thisown</a></td><td class="entry"><a class="el" href="a00883.html">neoapi.CamInfo</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
