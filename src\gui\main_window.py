import sys
import logging
from PyQt5.QtWidgets import (QMain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTabWidget, QStatusBar, QMenuBar, QAction, QMessageBox)
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtGui import QIcon

from gui.camera_widget import CameraWidget
from gui.temperature_plot import TemperaturePlotWidget
from gui.settings_widget import SettingsWidget
from core.database import TemperatureDatabase
from camera.discovery import CameraDiscovery
from camera.baumer_camera import BaumerCamera

class MainWindow(QMainWindow):
    """Hauptfenster der Anwendung"""
    
    temperature_received = pyqtSignal(str, float)  # IP, Temperatur
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Kamera Temperatur Monitor")
        self.setGeometry(100, 100, 1200, 800)
        
        # Core-Komponenten
        self.database = TemperatureDatabase()
        self.camera_discovery = CameraDiscovery()
        self.connected_cameras = {}  # IP -> BaumerCamera
        
        # GUI initialisieren
        self.init_ui()
        self.init_menu()
        self.init_status_bar()
        
        # Timer für GUI-Updates DEAKTIVIERT (verhindert automatische Scans)
        # self.update_timer = QTimer()
        # self.update_timer.timeout.connect(self.update_gui)
        # self.update_timer.start(5000)  # 5 Sekunden
        logging.info("GUI-Update-Timer deaktiviert für Stabilität")
        
        # Signal-Verbindungen
        self.temperature_received.connect(self.on_temperature_received)
        
        # Kamera-Entdeckung NICHT automatisch starten (verhindert Abstürze)
        # self.camera_discovery.start_continuous_discovery()
        logging.info("Automatische Discovery deaktiviert für Stabilität")
        
        logging.info("Hauptfenster initialisiert")
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hauptlayout
        main_layout = QVBoxLayout(central_widget)
        
        # Tab-Widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Kamera-Tab
        self.camera_widget = CameraWidget(self.database, self.camera_discovery)
        self.camera_widget.camera_selected.connect(self.on_camera_selected)
        self.camera_widget.camera_deselected.connect(self.on_camera_deselected)
        self.tab_widget.addTab(self.camera_widget, "Kameras")
        
        # Temperatur-Plot-Tab
        self.plot_widget = TemperaturePlotWidget(self.database)
        self.tab_widget.addTab(self.plot_widget, "Temperaturverlauf")
        
        # Einstellungen-Tab
        self.settings_widget = SettingsWidget()
        self.tab_widget.addTab(self.settings_widget, "Einstellungen")
        
    def init_menu(self):
        """Initialisiert die Menüleiste"""
        menubar = self.menuBar()
        
        # Datei-Menü
        file_menu = menubar.addMenu('Datei')
        
        exit_action = QAction('Beenden', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Ansicht-Menü
        view_menu = menubar.addMenu('Ansicht')
        
        refresh_action = QAction('Aktualisieren', self)
        refresh_action.setShortcut('F5')
        refresh_action.triggered.connect(self.refresh_data)
        view_menu.addAction(refresh_action)
        
        # Hilfe-Menü
        help_menu = menubar.addMenu('Hilfe')
        
        about_action = QAction('Über', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def init_status_bar(self):
        """Initialisiert die Statusleiste"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Bereit")
        
    def on_camera_selected(self, ip_address: str):
        """Wird aufgerufen wenn eine Kamera ausgewählt wird"""
        try:
            if ip_address not in self.connected_cameras:
                camera = BaumerCamera(ip_address)
                if camera.connect():
                    self.connected_cameras[ip_address] = camera
                    
                    # Kamera zur Datenbank hinzufügen
                    camera_info = camera.get_info()
                    camera_id = self.database.add_camera(
                        ip_address, 
                        camera_info['model'], 
                        camera_info['serial_number'],
                        f"Kamera {ip_address}"
                    )
                    
                    # Temperatur-Monitoring starten
                    camera.start_temperature_monitoring(
                        self.on_temperature_measurement,
                        self.settings_widget.get_measurement_interval()
                    )
                    
                    self.status_bar.showMessage(f"Kamera {ip_address} verbunden")
                    logging.info(f"Kamera {ip_address} erfolgreich verbunden")
                else:
                    self.status_bar.showMessage(f"Verbindung zu {ip_address} fehlgeschlagen")
                    
        except Exception as e:
            logging.error(f"Fehler beim Verbinden zur Kamera {ip_address}: {e}")
            self.status_bar.showMessage(f"Fehler bei Kamera {ip_address}")
            
    def on_camera_deselected(self, ip_address: str):
        """Wird aufgerufen wenn eine Kamera abgewählt wird"""
        if ip_address in self.connected_cameras:
            camera = self.connected_cameras[ip_address]
            camera.disconnect()
            del self.connected_cameras[ip_address]
            self.status_bar.showMessage(f"Kamera {ip_address} getrennt")
            logging.info(f"Kamera {ip_address} getrennt")
            
    def on_temperature_measurement(self, ip_address: str, temperature: float):
        """Callback für Temperaturmessungen"""
        self.temperature_received.emit(ip_address, temperature)
        
    def on_temperature_received(self, ip_address: str, temperature: float):
        """Verarbeitet empfangene Temperaturmessungen"""
        try:
            # Finde Kamera-ID
            cameras = self.database.get_cameras()
            camera_id = None
            for camera in cameras:
                if camera[1] == ip_address:  # IP-Adresse ist Index 1
                    camera_id = camera[0]  # ID ist Index 0
                    break
                    
            if camera_id:
                # Speichere Temperatur
                self.database.add_temperature_reading(camera_id, temperature)
                
                # Update GUI
                self.plot_widget.add_temperature_point(ip_address, temperature)
                self.camera_widget.update_temperature_display(ip_address, temperature)
                
                # Prüfe Temperatur-Grenzwerte
                self.check_temperature_thresholds(ip_address, temperature)
                
        except Exception as e:
            logging.error(f"Fehler bei Temperaturverarbeitung: {e}")
            
    def check_temperature_thresholds(self, ip_address: str, temperature: float):
        """Prüft Temperatur-Grenzwerte"""
        from ..core.config import TEMP_WARNING_THRESHOLD, TEMP_CRITICAL_THRESHOLD
        
        if temperature >= TEMP_CRITICAL_THRESHOLD:
            self.status_bar.showMessage(f"KRITISCH: Kamera {ip_address} - {temperature}°C", 10000)
            logging.warning(f"Kritische Temperatur bei {ip_address}: {temperature}°C")
        elif temperature >= TEMP_WARNING_THRESHOLD:
            self.status_bar.showMessage(f"WARNUNG: Kamera {ip_address} - {temperature}°C", 5000)
            logging.warning(f"Hohe Temperatur bei {ip_address}: {temperature}°C")
            
    def update_gui(self):
        """Aktualisiert GUI-Elemente"""
        # Update Kamera-Liste
        self.camera_widget.refresh_camera_list()
        
        # Update Status
        connected_count = len(self.connected_cameras)
        if connected_count > 0:
            self.status_bar.showMessage(f"{connected_count} Kamera(s) verbunden")
        else:
            self.status_bar.showMessage("Keine Kameras verbunden")
            
    def refresh_data(self):
        """Aktualisiert alle Daten"""
        self.camera_widget.refresh_camera_list()
        self.plot_widget.refresh_plots()
        self.status_bar.showMessage("Daten aktualisiert", 2000)
        
    def show_about(self):
        """Zeigt Über-Dialog"""
        QMessageBox.about(self, "Über", 
                         "Baumer Kamera Temperatur Monitor\n\n"
                         "Überwacht Temperaturen von Baumer-Kameras im Netzwerk\n"
                         "Version 1.0.0")
                         
    def closeEvent(self, event):
        """Wird beim Schließen aufgerufen"""
        # Alle Kameras trennen
        for camera in self.connected_cameras.values():
            camera.disconnect()
            
        # Discovery stoppen
        self.camera_discovery.stop_discovery()
        
        logging.info("Anwendung wird beendet")
        event.accept()
