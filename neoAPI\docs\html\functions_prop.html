<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members - Properties</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_t"></a>- t -</h3><ul>
<li>thisown
: <a class="el" href="a00823.html#abcc68ade07b0fc007aec9d75826894a1">neoapi.BaseFeature</a>
, <a class="el" href="a00839.html#a3aea12878d77a3a75f291606312c9015">neoapi.BoolFeature</a>
, <a class="el" href="a00879.html#abe1d87748dfad8ccb23e6212d2c7a317">neoapi.BufferBase</a>
, <a class="el" href="a00863.html#ae50022e50933bacdcc0438eb00ccedd1">neoapi.Cam</a>
, <a class="el" href="a00859.html#a95029eaabd281118ff8ac42556f056aa">neoapi.CamBase</a>
, <a class="el" href="a00883.html#a58c833eb8d0490277872e0ed78e29534">neoapi.CamInfo</a>
, <a class="el" href="a00891.html#a25550ab6a6f4ba63a32edcc933f79f84">neoapi.CamInfoList</a>
, <a class="el" href="a00887.html#aef55df7d9561f35e7c1a62f219281af5">neoapi.CamInfoListIterator</a>
, <a class="el" href="a00795.html#aec5adc25ece28bd03a426c7cbe6afdc3">neoapi.ColorMatrix</a>
, <a class="el" href="a00843.html#a12a23a6052d3db77cf8a595f2808d0a6">neoapi.CommandFeature</a>
, <a class="el" href="a00799.html#a23439d8bb5e3d744ef10cd429a4d5386">neoapi.ConverterSettings</a>
, <a class="el" href="a00827.html#a3b70f3e1fcf20a96821fb7fc402d9739">neoapi.DoubleFeature</a>
, <a class="el" href="a00847.html#a8c0a659fcc6f0de09b7d42530b8aaad8">neoapi.EnumerationFeature</a>
, <a class="el" href="a00811.html#a7433fbf6a07e587ba3dc7bedff7e420f">neoapi.Feature</a>
, <a class="el" href="a00779.html#a678614581e88470152a17278b5cd2f2c">neoapi.FeatureAccessException</a>
, <a class="el" href="a00815.html#a278ea658fcdca5c884842bb3a9aa1a1e">neoapi.FeatureList</a>
, <a class="el" href="a00819.html#ab69f38e9151262e4e5c502c71c1f0785">neoapi.FeatureListIterator</a>
, <a class="el" href="a00867.html#a16d9bc334da35fa5b8b19cb356d2490c">neoapi.FeatureStack</a>
, <a class="el" href="a00787.html#a2001de07a6fb41f5c56762b88b07ceb7">neoapi.FileAccessException</a>
, <a class="el" href="a00855.html#aea66aea381f903a8ae7cc7d4f6685822">neoapi.Image</a>
, <a class="el" href="a00803.html#a27ecc4d388b740e2749c167c4565dd79">neoapi.ImageInfo</a>
, <a class="el" href="a00831.html#a3d0d2071e26d6630ce6858238b7f1a31">neoapi.IntegerFeature</a>
, <a class="el" href="a00791.html#a4a4cd6f73dbf9c425966b3a4b70911ff">neoapi.InvalidArgumentException</a>
, <a class="el" href="a00807.html#a3ffddffba67360a348acc72c67975b38">neoapi.NeoEvent</a>
, <a class="el" href="a00875.html#a82cbdf3cbd7793ce09c678b92f0d56e5">neoapi.NeoEventCallback</a>
, <a class="el" href="a00767.html#a738404193c4bc1ddb62513c2a089cda3">neoapi.NeoException</a>
, <a class="el" href="a00871.html#aad52fa4dcae410558454f375dadbb59c">neoapi.NeoImageCallback</a>
, <a class="el" href="a00899.html#ab6337840e87e1ffc6d4db4358ccb476e">neoapi.NeoTrace</a>
, <a class="el" href="a00895.html#a0a74b8ac7d2342c855383c57f827f9e1">neoapi.NeoTraceCallback</a>
, <a class="el" href="a00775.html#a25223830ec605bb73ad9a4be5c14df2e">neoapi.NoAccessException</a>
, <a class="el" href="a00783.html#a2bcfdb13c5e54769019926e1600910aa">neoapi.NoImageBufferException</a>
, <a class="el" href="a00771.html#a4f0dfe38dc0301c36bdc59b26efc7364">neoapi.NotConnectedException</a>
, <a class="el" href="a00851.html#a748adab3c3d3ad1cad6b9b6181d4a44d">neoapi.RegisterFeature</a>
, <a class="el" href="a00835.html#a9c4fef880e95ee8aad4ca09340cdf77e">neoapi.StringFeature</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
