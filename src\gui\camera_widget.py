import logging
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QListWidget,
                             QListWidgetItem, QPushButton, QLabel, QCheckBox,
                             QGroupBox, QGridLayout, QProgressBar, QComboBox)
from PyQt5.QtCore import pyqtSignal, QTimer
from PyQt5.QtGui import QFont

class CameraWidget(QWidget):
    """Widget für Kamera-Verwaltung"""
    
    camera_selected = pyqtSignal(str)  # IP-Adresse
    camera_deselected = pyqtSignal(str)  # IP-Adresse
    
    def __init__(self, database, camera_discovery):
        super().__init__()
        self.database = database
        self.camera_discovery = camera_discovery
        self.camera_items = {}  # IP -> QListWidgetItem
        self.temperature_labels = {}  # IP -> QLabel
        
        self.init_ui()
        
        # Timer für Discovery-Updates DEAKTIVIERT (verhindert automatische Scans)
        # self.discovery_timer = QTimer()
        # self.discovery_timer.timeout.connect(self.refresh_camera_list)
        # self.discovery_timer.start(10000)  # 10 Sekunden
        logging.info("Discovery-Timer deaktiviert für Stabilität")

        # Initialisiere Netzwerkkarten-Liste
        self.init_network_interfaces()
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        layout = QVBoxLayout(self)
        
        # Titel
        title_label = QLabel("Verfügbare Kameras")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Hauptbereich
        main_layout = QHBoxLayout()
        layout.addLayout(main_layout)
        
        # Linke Seite - Kamera-Liste
        left_group = QGroupBox("Entdeckte Kameras")
        left_layout = QVBoxLayout(left_group)

        # Netzwerkkarten-Auswahl
        network_layout = QHBoxLayout()
        network_layout.addWidget(QLabel("Netzwerkkarte:"))

        self.network_combo = QComboBox()
        self.network_combo.currentTextChanged.connect(self.on_network_changed)
        network_layout.addWidget(self.network_combo)

        left_layout.addLayout(network_layout)

        self.camera_list = QListWidget()
        self.camera_list.itemChanged.connect(self.on_camera_item_changed)
        left_layout.addWidget(self.camera_list)

        # Buttons
        button_layout = QHBoxLayout()
        self.refresh_button = QPushButton("Aktualisieren")
        self.refresh_button.clicked.connect(self.refresh_camera_list)
        button_layout.addWidget(self.refresh_button)

        self.scan_button = QPushButton("Netzwerk scannen")
        self.scan_button.clicked.connect(self.start_network_scan)
        button_layout.addWidget(self.scan_button)

        left_layout.addLayout(button_layout)
        main_layout.addWidget(left_group)
        
        # Rechte Seite - Verbundene Kameras
        right_group = QGroupBox("Verbundene Kameras")
        right_layout = QVBoxLayout(right_group)
        
        self.connected_cameras_widget = QWidget()
        self.connected_layout = QGridLayout(self.connected_cameras_widget)
        right_layout.addWidget(self.connected_cameras_widget)
        
        main_layout.addWidget(right_group)
        
        # Status
        self.status_label = QLabel("Bereit für Kamera-Entdeckung")
        layout.addWidget(self.status_label)

    def init_network_interfaces(self):
        """Initialisiert Netzwerkkarten-Auswahl"""
        try:
            # Hole alle verfügbaren Adapter
            all_adapters = self.camera_discovery.get_all_adapters_info()
            active_interfaces = self.camera_discovery.available_interfaces

            self.network_combo.clear()

            # Füge aktive Interfaces hinzu (mit IP)
            active_count = 0
            for interface in active_interfaces:
                self.network_combo.addItem(f"[AKTIV] {interface['description']}", interface)
                active_count += 1

            # Füge inaktive Adapter hinzu (ohne IP, aber auswählbar)
            inactive_count = 0
            for adapter in all_adapters:
                if not adapter.get('has_ip', False):
                    # Erstelle Interface-Info für inaktive Adapter
                    inactive_interface = {
                        'name': adapter['name'],
                        'ip': 'auto',
                        'netmask': '*************',
                        'network_base': '192.168.1',  # Standard für inaktive
                        'description': f"[INAKTIV] {adapter['name']} (Nicht konfiguriert)"
                    }
                    self.network_combo.addItem(inactive_interface['description'], inactive_interface)
                    inactive_count += 1

            # Füge Standard-Netzwerk-Bereiche hinzu
            default_networks = self.camera_discovery._get_default_networks()
            for network in default_networks:
                self.network_combo.addItem(f"[STANDARD] {network['description']}", network)

            # Wähle ersten aktiven Adapter als Standard
            if active_interfaces:
                self.camera_discovery.set_network_interface(active_interfaces[0])
                self.status_label.setText(f"Netzwerkkarte: {active_interfaces[0]['description']}")
            else:
                self.status_label.setText("Keine aktiven Netzwerkkarten gefunden")

            total_options = len(all_adapters) + len(default_networks)
            logging.info(f"Netzwerkkarten-Auswahl initialisiert: {active_count} aktiv, {inactive_count} inaktiv, {total_options} gesamt")

        except Exception as e:
            logging.error(f"Fehler bei Netzwerkkarten-Initialisierung: {e}")
            self.status_label.setText("Fehler bei Netzwerkkarten-Erkennung")

    def on_network_changed(self, text):
        """Wird aufgerufen wenn Netzwerkkarte geändert wird"""
        try:
            current_data = self.network_combo.currentData()
            if current_data:
                self.camera_discovery.set_network_interface(current_data)
                self.status_label.setText(f"Netzwerkkarte gewechselt: {text}")

                # Lösche alte Kamera-Liste
                self.camera_list.clear()
                self.camera_items.clear()

                logging.info(f"Netzwerkkarte gewechselt zu: {text}")

        except Exception as e:
            logging.error(f"Fehler beim Wechseln der Netzwerkkarte: {e}")
            self.status_label.setText("Fehler beim Netzwerkkarten-Wechsel")
        
    def refresh_camera_list(self):
        """Aktualisiert die Kamera-Liste (NUR manuell aufgerufen)"""
        try:
            # Nur aktualisieren wenn Discovery-Daten vorhanden sind
            discovered_cameras = self.camera_discovery.get_discovered_cameras()

            if not discovered_cameras:
                # Keine automatische Suche - nur Status aktualisieren
                self.status_label.setText("Bereit für manuellen Scan")
                return

            # Entferne nicht mehr verfügbare Kameras
            current_ips = {camera['ip'] for camera in discovered_cameras}
            items_to_remove = []

            for i in range(self.camera_list.count()):
                item = self.camera_list.item(i)
                ip = item.data(1)  # IP in UserRole gespeichert
                if ip not in current_ips:
                    items_to_remove.append(item)

            for item in items_to_remove:
                row = self.camera_list.row(item)
                self.camera_list.takeItem(row)
                if item.data(1) in self.camera_items:
                    del self.camera_items[item.data(1)]

            # Füge neue Kameras hinzu
            for camera in discovered_cameras:
                ip = camera['ip']
                if ip not in self.camera_items:
                    self.add_camera_item(camera)

            self.status_label.setText(f"{len(discovered_cameras)} Kamera(s) gefunden")
            logging.info(f"Kamera-Liste manuell aktualisiert: {len(discovered_cameras)} Kameras")

        except Exception as e:
            logging.error(f"Fehler beim Aktualisieren der Kamera-Liste: {e}")
            self.status_label.setText("Fehler beim Aktualisieren")
            
    def add_camera_item(self, camera_info):
        """Fügt Kamera zur Liste hinzu"""
        ip = camera_info['ip']
        
        # Hole detaillierte Informationen
        details = self.camera_discovery.get_camera_details(ip)
        
        # Erstelle Listeneintrag
        item_text = f"{ip} - {details['model']}"
        if details['serial'] != 'Unbekannt':
            item_text += f" (SN: {details['serial']})"
            
        item = QListWidgetItem(item_text)
        item.setData(1, ip)  # IP in UserRole speichern
        item.setCheckState(0)  # Unchecked
        
        self.camera_list.addItem(item)
        self.camera_items[ip] = item
        
        logging.info(f"Kamera hinzugefügt: {item_text}")
        
    def on_camera_item_changed(self, item):
        """Wird aufgerufen wenn Kamera-Checkbox geändert wird"""
        ip = item.data(1)
        
        if item.checkState() == 2:  # Checked
            self.camera_selected.emit(ip)
            self.add_connected_camera_display(ip)
        else:  # Unchecked
            self.camera_deselected.emit(ip)
            self.remove_connected_camera_display(ip)
            
    def add_connected_camera_display(self, ip):
        """Fügt Anzeige für verbundene Kamera hinzu"""
        row = len(self.temperature_labels)
        
        # IP-Label
        ip_label = QLabel(f"Kamera {ip}:")
        ip_label.setStyleSheet("font-weight: bold;")
        self.connected_layout.addWidget(ip_label, row, 0)
        
        # Temperatur-Label
        temp_label = QLabel("-- °C")
        temp_label.setStyleSheet("font-size: 16px; color: #0066CC;")
        self.connected_layout.addWidget(temp_label, row, 1)
        
        # Status-Indikator
        status_label = QLabel("Verbinde...")
        status_label.setStyleSheet("color: orange;")
        self.connected_layout.addWidget(status_label, row, 2)
        
        self.temperature_labels[ip] = {
            'temp': temp_label,
            'status': status_label,
            'ip': ip_label
        }
        
    def remove_connected_camera_display(self, ip):
        """Entfernt Anzeige für getrennte Kamera"""
        if ip in self.temperature_labels:
            labels = self.temperature_labels[ip]
            labels['temp'].deleteLater()
            labels['status'].deleteLater()
            labels['ip'].deleteLater()
            del self.temperature_labels[ip]
            
    def update_temperature_display(self, ip, temperature):
        """Aktualisiert Temperaturanzeige"""
        if ip in self.temperature_labels:
            labels = self.temperature_labels[ip]
            labels['temp'].setText(f"{temperature:.1f} °C")
            labels['status'].setText("Verbunden")
            labels['status'].setStyleSheet("color: green;")
            
            # Farbkodierung basierend auf Temperatur
            if temperature >= 70:
                labels['temp'].setStyleSheet("font-size: 16px; color: red; font-weight: bold;")
            elif temperature >= 60:
                labels['temp'].setStyleSheet("font-size: 16px; color: orange; font-weight: bold;")
            else:
                labels['temp'].setStyleSheet("font-size: 16px; color: green;")
                
    def start_network_scan(self):
        """Startet manuellen Netzwerk-Scan"""
        try:
            self.scan_button.setEnabled(False)
            self.scan_button.setText("Scanne...")
            self.status_label.setText("Netzwerk-Scan läuft...")

            # Starte Scan in separatem Thread
            import threading

            def scan_thread():
                try:
                    logging.info("=== MANUELLER Netzwerk-Scan gestartet ===")

                    # Führe UPnP Discovery durch
                    upnp_cameras = self.camera_discovery.discover_cameras_upnp()
                    logging.info(f"UPnP Scan: {len(upnp_cameras)} Geräte gefunden")

                    # Führe IP-Scan durch (verwendet ausgewählte Netzwerkkarte)
                    ip_cameras = self.camera_discovery.discover_cameras_ip_scan()
                    logging.info(f"IP Scan: {len(ip_cameras)} Geräte gefunden")

                    # Kombiniere und aktualisiere
                    all_cameras = upnp_cameras + ip_cameras
                    unique_cameras = []
                    seen_ips = set()
                    for camera in all_cameras:
                        if camera['ip'] not in seen_ips:
                            unique_cameras.append(camera)
                            seen_ips.add(camera['ip'])

                    # Setze Discovery-Ergebnisse
                    self.camera_discovery.discovered_cameras = unique_cameras

                    logging.info(f"=== MANUELLER Scan abgeschlossen: {len(unique_cameras)} Kameras ===")

                    # GUI-Update im Hauptthread
                    QTimer.singleShot(0, self.finish_network_scan)

                except Exception as e:
                    logging.error(f"Fehler beim manuellen Netzwerk-Scan: {e}")
                    QTimer.singleShot(0, lambda: self.scan_error(str(e)))

            thread = threading.Thread(target=scan_thread, daemon=True)
            thread.start()

            # Timeout nach 30 Sekunden
            QTimer.singleShot(30000, self.scan_timeout)

        except Exception as e:
            logging.error(f"Fehler beim Starten des Netzwerk-Scans: {e}")
            self.scan_error(str(e))

    def finish_network_scan(self):
        """Beendet Netzwerk-Scan erfolgreich"""
        try:
            self.scan_button.setEnabled(True)
            self.scan_button.setText("Netzwerk scannen")
            self.refresh_camera_list()
            logging.info("Manueller Netzwerk-Scan abgeschlossen")
        except Exception as e:
            logging.error(f"Fehler beim Beenden des Scans: {e}")

    def scan_timeout(self):
        """Wird aufgerufen bei Scan-Timeout"""
        if not self.scan_button.isEnabled():
            self.scan_button.setEnabled(True)
            self.scan_button.setText("Netzwerk scannen")
            self.status_label.setText("Scan-Timeout - versuchen Sie es erneut")
            logging.warning("Netzwerk-Scan Timeout")

    def scan_error(self, error_msg):
        """Wird bei Scan-Fehlern aufgerufen"""
        self.scan_button.setEnabled(True)
        self.scan_button.setText("Netzwerk scannen")
        self.status_label.setText(f"Scan-Fehler: {error_msg}")
        logging.error(f"Netzwerk-Scan Fehler: {error_msg}")
