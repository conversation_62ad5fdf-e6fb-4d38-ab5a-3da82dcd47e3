<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_d"></a>- d -</h3><ul>
<li>DecimationHorizontalMode_Average
: <a class="el" href="a00091.html#a24a7269caa3e31620b58580c9a468df8">neoapi</a>
</li>
<li>DecimationHorizontalMode_Discard
: <a class="el" href="a00091.html#a73324097fd43bd41202553c3a259c48c">neoapi</a>
</li>
<li>DecimationVerticalMode_Average
: <a class="el" href="a00091.html#a1b49ff2c89a202f72baa41621dc8557e">neoapi</a>
</li>
<li>DecimationVerticalMode_Discard
: <a class="el" href="a00091.html#ad99d8357a912982b55fda191feb8e707">neoapi</a>
</li>
<li>DefectPixelListSelector_Column
: <a class="el" href="a00091.html#adb4226b8f62a9a6e1e2db90cfc8cd853">neoapi</a>
</li>
<li>DefectPixelListSelector_Pixel
: <a class="el" href="a00091.html#a375ef4ff3a9a0a4577dcad3ce9349a4f">neoapi</a>
</li>
<li>DefectPixelListSelector_Row
: <a class="el" href="a00091.html#a429e970db73db907575587777a1cff3d">neoapi</a>
</li>
<li>DeviceCharacterSet_ASCII
: <a class="el" href="a00091.html#a196573030d30a19b1747aac92f403c61">neoapi</a>
</li>
<li>DeviceCharacterSet_UTF16
: <a class="el" href="a00091.html#a572f9f4beef7994020d3a6f5fe6ea416">neoapi</a>
</li>
<li>DeviceCharacterSet_UTF8
: <a class="el" href="a00091.html#a35aa271cad3a25c88e8f9c0586733630">neoapi</a>
</li>
<li>DeviceClockSelector_CameraLink
: <a class="el" href="a00091.html#a6c087873e78745f986024cd3bfa3a52f">neoapi</a>
</li>
<li>DeviceClockSelector_Sensor
: <a class="el" href="a00091.html#a1a4d73a9fc2c45a93b5b58a5f65a0b1e">neoapi</a>
</li>
<li>DeviceClockSelector_SensorDigitization
: <a class="el" href="a00091.html#a4b849561d1c6b611ecd8ae9faac71b65">neoapi</a>
</li>
<li>DeviceFrontUARTSource_OpticControl
: <a class="el" href="a00091.html#ac44061df0646244b942890e61e43b765">neoapi</a>
</li>
<li>DeviceFrontUARTSource_RS232
: <a class="el" href="a00091.html#ae4d95578bc1a9646ef27ab8bb8b7075b">neoapi</a>
</li>
<li>DeviceLicense_Invalid
: <a class="el" href="a00091.html#a05a0efd9f904a43cc2eb5cb57f248cf3">neoapi</a>
</li>
<li>DeviceLicense_Valid
: <a class="el" href="a00091.html#a5e98456de0e6424710cb24d092bff3a6">neoapi</a>
</li>
<li>DeviceLicenseTypeSelector_eVARuntime
: <a class="el" href="a00091.html#aef5afed2f1d0a83c56d796069fa9a6c1">neoapi</a>
</li>
<li>DeviceLinkHeartbeatMode_Off
: <a class="el" href="a00091.html#ac38ee4225324f0ff3c95e6b2e5937c21">neoapi</a>
</li>
<li>DeviceLinkHeartbeatMode_On
: <a class="el" href="a00091.html#a827888b4a00a5cebadf7265e8d3dae9c">neoapi</a>
</li>
<li>DeviceLinkSelector_USB0
: <a class="el" href="a00091.html#a8a8be5a27ab2f12bc65ebd0e42c48b79">neoapi</a>
</li>
<li>DeviceLinkThroughputLimitMode_Off
: <a class="el" href="a00091.html#ad14a7d7a136e2559d084683431be10f9">neoapi</a>
</li>
<li>DeviceLinkThroughputLimitMode_On
: <a class="el" href="a00091.html#a69151d04e3162b33dcb4d1b8177ed033">neoapi</a>
</li>
<li>DeviceRegistersEndianness_Big
: <a class="el" href="a00091.html#aa9e223257f5f83d08763c7241125dc4b">neoapi</a>
</li>
<li>DeviceRegistersEndianness_Little
: <a class="el" href="a00091.html#aade6003a07938dfbb66c5b57e0cbf3c4">neoapi</a>
</li>
<li>DeviceScanType_Areascan
: <a class="el" href="a00091.html#aee7229bc06cd54c8fb78f554c18f99b3">neoapi</a>
</li>
<li>DeviceScanType_Areascan3D
: <a class="el" href="a00091.html#a527d038e3806f499f0c632a89df7c3e5">neoapi</a>
</li>
<li>DeviceScanType_Linescan
: <a class="el" href="a00091.html#ad42e1afcd1fd4830d2bac181aa4fecf9">neoapi</a>
</li>
<li>DeviceScanType_Linescan3D
: <a class="el" href="a00091.html#abb9a706a1c2825b7090c4f09d0e96490">neoapi</a>
</li>
<li>DeviceSensorSelector_All
: <a class="el" href="a00091.html#a72317b178862ae24b26d6083942a8bb4">neoapi</a>
</li>
<li>DeviceSensorSelector_Left
: <a class="el" href="a00091.html#a162aaa3f5686d3cd25639e955fe2198b">neoapi</a>
</li>
<li>DeviceSensorSelector_Right
: <a class="el" href="a00091.html#a5c92190407fc4c10b420c1cfbcf3e3bc">neoapi</a>
</li>
<li>DeviceSensorType_CCD
: <a class="el" href="a00091.html#ad1ee1d7cc182718eccd1b685e1d0fa5d">neoapi</a>
</li>
<li>DeviceSensorType_CMOS
: <a class="el" href="a00091.html#adac13357f82d923efc54d7ff864928c9">neoapi</a>
</li>
<li>DeviceSensorVersion_CMOSIS_CMV2000_V2
: <a class="el" href="a00091.html#a05828025e05283a1a70d07013b9d0c0d">neoapi</a>
</li>
<li>DeviceSensorVersion_CMOSIS_CMV2000_V3
: <a class="el" href="a00091.html#af8a5a477d76f02970787eb356dcc6505">neoapi</a>
</li>
<li>DeviceSensorVersion_CMOSIS_CMV4000_V2
: <a class="el" href="a00091.html#a16f498ace839dd8ad3263ae3634ee01a">neoapi</a>
</li>
<li>DeviceSensorVersion_CMOSIS_CMV4000_V3
: <a class="el" href="a00091.html#a6cad6f9754839b14f6c29a8984fb0f37">neoapi</a>
</li>
<li>DeviceSerialPortBaudRate_Baud115200
: <a class="el" href="a00091.html#a96268b95f0dc404edcf761ccc7586469">neoapi</a>
</li>
<li>DeviceSerialPortBaudRate_Baud19200
: <a class="el" href="a00091.html#a38323a4a9dfa103643343fc11a16c260">neoapi</a>
</li>
<li>DeviceSerialPortBaudRate_Baud230400
: <a class="el" href="a00091.html#a28021351a876e17dd9dd37e657ddc123">neoapi</a>
</li>
<li>DeviceSerialPortBaudRate_Baud38400
: <a class="el" href="a00091.html#a65a4f4ab530fe598ff9bb7400a752469">neoapi</a>
</li>
<li>DeviceSerialPortBaudRate_Baud460800
: <a class="el" href="a00091.html#a922153eee58d122c8037539a967122ba">neoapi</a>
</li>
<li>DeviceSerialPortBaudRate_Baud57600
: <a class="el" href="a00091.html#a12062990f1c34cc9a193159145cddb89">neoapi</a>
</li>
<li>DeviceSerialPortBaudRate_Baud921600
: <a class="el" href="a00091.html#ac2461d2ad4d7fe541c8899880d9ca07a">neoapi</a>
</li>
<li>DeviceSerialPortBaudRate_Baud9600
: <a class="el" href="a00091.html#a16fc130361ce3a4354534e326eebf3a5">neoapi</a>
</li>
<li>DeviceSerialPortSelector_CameraLink
: <a class="el" href="a00091.html#a258030747e161059bf78d1579ad3bbe9">neoapi</a>
</li>
<li>DeviceStreamChannelEndianness_Big
: <a class="el" href="a00091.html#aaca58509d440783cb8e63fc0c3ffd2bb">neoapi</a>
</li>
<li>DeviceStreamChannelEndianness_Little
: <a class="el" href="a00091.html#a750b175849eb2f546230d1096564795e">neoapi</a>
</li>
<li>DeviceStreamChannelType_Receiver
: <a class="el" href="a00091.html#ae63febb32a94755fa94766ac356a3b6a">neoapi</a>
</li>
<li>DeviceStreamChannelType_Transmitter
: <a class="el" href="a00091.html#a03c99a0337ac61e72a797e07fe6c8fca">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_10X
: <a class="el" href="a00091.html#af0edfa4bb85933ba88bffb732a718fc3">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_10X_1Y
: <a class="el" href="a00091.html#a7ca09e407c47e7eff191a9385455e2af">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X
: <a class="el" href="a00091.html#af74f234d893e5415d7197e64134744f2">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X10
: <a class="el" href="a00091.html#a9357ab9df21af12988d9f322d4239e35">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X10_1Y
: <a class="el" href="a00091.html#a09e749b29ac7ba2871c33b83cea638ee">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X2
: <a class="el" href="a00091.html#a4afdce3a5bcb8238aa51554be67d7995">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X2_1Y
: <a class="el" href="a00091.html#ad5794cb6fb21ce249551f1b634f9494f">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X2_1Y2
: <a class="el" href="a00091.html#af6d14a7e4978dde8f447309d77cc5595">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X2_2YE
: <a class="el" href="a00091.html#abe15235a2c49e0134fb46b02b35de143">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X3
: <a class="el" href="a00091.html#a5fc00467def76b013056bf7793838e8d">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X3_1Y
: <a class="el" href="a00091.html#a298fdcbc1fd868090fd9cbc04b0f1c76">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X4
: <a class="el" href="a00091.html#a00ba6567f66d4be362e61a4bd12b3210">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X4_1Y
: <a class="el" href="a00091.html#ab1550645017a46b11fe348e973d2ec68">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X8
: <a class="el" href="a00091.html#a48c17e1c876cdd30ddf28ab1b6482dd8">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X8_1Y
: <a class="el" href="a00091.html#a3b014f7a9c0ffc3046d90ee66cde5d2f">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X_1Y
: <a class="el" href="a00091.html#af84e09a35029624c1ae7a0580ee7479c">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X_1Y2
: <a class="el" href="a00091.html#a6eafb62e5c00823138f89ad82348622c">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_1X_2YE
: <a class="el" href="a00091.html#a4e1ff166aba3b9f048661987a93f6d37">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X
: <a class="el" href="a00091.html#ad98c1627caadf649be83e5ab5a685287">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X2
: <a class="el" href="a00091.html#a0d58587e3a455494a355d2348c3e6e71">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X2_1Y
: <a class="el" href="a00091.html#ab91e5fd9f561b617d5f20927c2b96106">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X2E
: <a class="el" href="a00091.html#a486b9151777b6a2479798b570ea93786">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X2E_1Y
: <a class="el" href="a00091.html#a595b5bec7b6580a0b3213d294b625f07">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X2E_2YE
: <a class="el" href="a00091.html#a8057ebca9db39942155d616e26454270">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X2M
: <a class="el" href="a00091.html#ab3e6273e5f789144658cb32df0f8cc15">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X2M_1Y
: <a class="el" href="a00091.html#a7dc49633f8da6882f620d7722684c50b">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X_1Y
: <a class="el" href="a00091.html#aa742414ace1456b77f8eb5221f41b3dd">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X_1Y2
: <a class="el" href="a00091.html#ab4240d14b862d3f3c8638251a59f630c">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2X_2YE
: <a class="el" href="a00091.html#ae4e07f7c07f62134cc2d47c146487244">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2XE
: <a class="el" href="a00091.html#a4304dd97bfceb7ade7640eb38ac55633">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2XE_1Y
: <a class="el" href="a00091.html#a830e5011e97a2765a59f7edcef2f8111">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2XE_1Y2
: <a class="el" href="a00091.html#a39498888ae506f5f750dc30bf255eda1">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2XE_2YE
: <a class="el" href="a00091.html#a3f7753e201a47d6cb71ae2fb9587fe12">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2XM
: <a class="el" href="a00091.html#a741ee245aee939b8c6d9bfbcd69e2a5c">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2XM_1Y
: <a class="el" href="a00091.html#a0d1b0587c8f08d1666b7507e5bf6403f">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2XM_1Y2
: <a class="el" href="a00091.html#a34c468941b074c0cea9b360c300519b6">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_2XM_2YE
: <a class="el" href="a00091.html#aeb1158225d02c1c6ce626a22b63c14bf">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_3X
: <a class="el" href="a00091.html#a2355e112c62fc3c867c7d1c8ca88a60f">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_3X_1Y
: <a class="el" href="a00091.html#aaa08abc7b868113a09f8cc7a281e6937">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_4X
: <a class="el" href="a00091.html#ace78a3afd92d25e6694cad46f07ec59c">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_4X2
: <a class="el" href="a00091.html#ad0f39399cd0188a60795ecb75a2b80da">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_4X2_1Y
: <a class="el" href="a00091.html#a6fa004b364bdd3f691a4243b1b95204a">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_4X2E
: <a class="el" href="a00091.html#af20af49d2d09eb29d4535f72467f57fb">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_4X2E_1Y
: <a class="el" href="a00091.html#a9862935328bfb3d465ea1a090289fbfe">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_4X_1Y
: <a class="el" href="a00091.html#a063f8aec69b7ff093213140ca58164a2">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_8X
: <a class="el" href="a00091.html#a4861776a5fb0fb197f62378bf6b31c99">neoapi</a>
</li>
<li>DeviceTapGeometry_Geometry_8X_1Y
: <a class="el" href="a00091.html#a01d6fc5e765ed13cd5a6568f2bea8323">neoapi</a>
</li>
<li>DeviceTemperatureSelector_InHouse
: <a class="el" href="a00091.html#a8f021b38a5ed46319991e99bccf0c7f6">neoapi</a>
</li>
<li>DeviceTemperatureSelector_Mainboard
: <a class="el" href="a00091.html#ace91dd50c8b6e5a8817eb609351062f5">neoapi</a>
</li>
<li>DeviceTemperatureSelector_Sensor
: <a class="el" href="a00091.html#a3fd197d9d3b01793db5f408a0ec29a4e">neoapi</a>
</li>
<li>DeviceTemperatureStatus_Exceeded
: <a class="el" href="a00091.html#a627a46f2f4b6d14845605429d8cc2675">neoapi</a>
</li>
<li>DeviceTemperatureStatus_High
: <a class="el" href="a00091.html#aee34630e8355796750e83c908b07ec47">neoapi</a>
</li>
<li>DeviceTemperatureStatus_Normal
: <a class="el" href="a00091.html#ad011c2ff603a3ed2df9543e1ad1efeec">neoapi</a>
</li>
<li>DeviceTemperatureStatusTransitionSelector_ExceededToNormal
: <a class="el" href="a00091.html#ae1497910eb99be9003e3a8f830e16a98">neoapi</a>
</li>
<li>DeviceTemperatureStatusTransitionSelector_HighToExceeded
: <a class="el" href="a00091.html#a23ee8591d0a0d1fd9cce2d4764022b34">neoapi</a>
</li>
<li>DeviceTemperatureStatusTransitionSelector_NormalToHigh
: <a class="el" href="a00091.html#a38d13a449187ff179bda1989955a3f6d">neoapi</a>
</li>
<li>DeviceTLType_CameraLink
: <a class="el" href="a00091.html#a9491b1a0a16a41e6b99f1b6a6d589cff">neoapi</a>
</li>
<li>DeviceTLType_CameraLinkHS
: <a class="el" href="a00091.html#aa40543c9761efdbd78fd944432bc61ad">neoapi</a>
</li>
<li>DeviceTLType_CoaXPress
: <a class="el" href="a00091.html#acb21b3309444ff149885b5b245263460">neoapi</a>
</li>
<li>DeviceTLType_Custom
: <a class="el" href="a00091.html#abfd8b075490a5c3fcfaf7d86af1d6ab6">neoapi</a>
</li>
<li>DeviceTLType_GigEVision
: <a class="el" href="a00091.html#ae6e881e6ed506301377b14982e6f4249">neoapi</a>
</li>
<li>DeviceTLType_USB3Vision
: <a class="el" href="a00091.html#aab0c1d54911c3f99ffd0172d42fb720f">neoapi</a>
</li>
<li>DeviceType_Peripheral
: <a class="el" href="a00091.html#a28202a1b6b82762ea75c5dcc3e6261f7">neoapi</a>
</li>
<li>DeviceType_Receiver
: <a class="el" href="a00091.html#aec0c5bd73d4f7402ebc8a5f7d1f01b30">neoapi</a>
</li>
<li>DeviceType_Transceiver
: <a class="el" href="a00091.html#a8e992046772cf63dd759e9135b88b43c">neoapi</a>
</li>
<li>DeviceType_Transmitter
: <a class="el" href="a00091.html#a490b371f9d5dd64658fbf5b5ddfdfd27">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
