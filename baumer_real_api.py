VERSION = "1.0.9"

import sys
import os
import logging
import socket
import threading
import time
import requests
import json
import xml.etree.ElementTree as ET
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QTabWidget, QLabel, QPushButton,
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox)
from PyQt5.QtCore import QTimer

# Baumer neoAPI Import (falls verfügbar)
try:
    import neoapi
    NEOAPI_AVAILABLE = True
    print("✅ Baumer neoAPI verfügbar")
except ImportError:
    NEOAPI_AVAILABLE = False
    print("❌ Baumer neoAPI nicht verfügbar - verwende Simulation")

def setup_logging():
    """Konfiguriert Logging"""
    try:
        os.makedirs('logs', exist_ok=True)
        log_filename = os.path.join('logs', f"baumer_real_{datetime.now().strftime('%Y%m%d')}.log")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    except Exception as e:
        print(f"Logging Setup Fehler: {e}")

class BaumerRealAPIWindow(QMainWindow):
    """Baumer Monitor mit echten API-Calls"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Kamera Monitor v1.0.8 - Echte API")
        self.setGeometry(100, 100, 1000, 800)
        
        # Bekannte Kamera-IPs
        self.known_cameras = [
            "***************",
            "**************", 
            "**************",
            "**************"
        ]
        
        # Baumer-spezifische API-Endpunkte
        self.api_endpoints = [
            # Standard Baumer-Endpunkte
            "/api/device/temperature",
            "/api/temperature", 
            "/device/temperature",
            "/temperature",
            "/cgi-bin/temperature",
            "/cgi-bin/device_info",
            "/api/device/info",
            "/device_info",
            "/status",
            "/api/status",
            # GigE Vision Standard-Endpunkte
            "/api/v1/device/temperature",
            "/v1/temperature",
            "/genicam/temperature",
            # Baumer-spezifische Pfade
            "/baumer/temperature",
            "/vcxg/temperature",
            "/camera/temperature"
        ]
        
        # Initialisiere Komponenten
        self.connected_cameras = {}
        self.temperature_readings = {}
        self.temperature_timer = QTimer()
        self.temperature_timer.timeout.connect(self.update_all_temperatures)
        
        # GUI aufbauen
        self.init_ui()
        
        logging.info("Baumer Real API GUI initialisiert")
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hauptlayout
        main_layout = QVBoxLayout(central_widget)
        
        # Titel
        title_label = QLabel("🌡️ Baumer Kamera Monitor v1.0.8 - Echte API")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: blue;")
        main_layout.addWidget(title_label)
        
        # Status
        self.status_label = QLabel("✅ Bereit für echte Temperatur-Messungen")
        self.status_label.setStyleSheet("padding: 5px; background-color: #e8f5e8; border: 1px solid #4CAF50; border-radius: 3px;")
        main_layout.addWidget(self.status_label)
        
        # Kameras Gruppe
        cameras_group = QGroupBox("🎯 Baumer-Kameras")
        cameras_layout = QVBoxLayout(cameras_group)
        
        # Kamera-Liste
        self.camera_list = QListWidget()
        for ip in self.known_cameras:
            item = QListWidgetItem(f"📹 VCXG-13M - {ip}")
            item.setData(1, ip)
            self.camera_list.addItem(item)
        cameras_layout.addWidget(self.camera_list)
        
        # Kamera-Buttons
        camera_buttons = QHBoxLayout()
        
        self.connect_button = QPushButton("🔗 Kamera verbinden")
        self.connect_button.clicked.connect(self.connect_selected_camera)
        self.connect_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        camera_buttons.addWidget(self.connect_button)
        
        self.test_api_button = QPushButton("🔍 API-Endpunkte testen")
        self.test_api_button.clicked.connect(self.test_api_endpoints)
        camera_buttons.addWidget(self.test_api_button)
        
        self.single_temp_button = QPushButton("🌡️ Temperatur messen")
        self.single_temp_button.clicked.connect(self.measure_single_temp)
        camera_buttons.addWidget(self.single_temp_button)
        
        cameras_layout.addLayout(camera_buttons)
        main_layout.addWidget(cameras_group)
        
        # Temperatur-Anzeige
        temp_group = QGroupBox("🌡️ Temperatur-Überwachung")
        temp_layout = QVBoxLayout(temp_group)
        
        # Temperatur-Tabelle
        self.temp_table_widget = QWidget()
        self.temp_table_layout = QGridLayout(self.temp_table_widget)
        
        # Header
        headers = ["Kamera", "Temperatur", "Status", "Methode", "Letzte Messung", "Aktion"]
        for i, header in enumerate(headers):
            label = QLabel(header)
            label.setStyleSheet("font-weight: bold; background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc;")
            self.temp_table_layout.addWidget(label, 0, i)
        
        temp_layout.addWidget(self.temp_table_widget)
        
        # Überwachungs-Buttons
        monitoring_buttons = QHBoxLayout()
        
        self.start_monitoring_button = QPushButton("▶️ Überwachung starten")
        self.start_monitoring_button.clicked.connect(self.start_monitoring)
        self.start_monitoring_button.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px;")
        monitoring_buttons.addWidget(self.start_monitoring_button)
        
        self.stop_monitoring_button = QPushButton("⏹️ Überwachung stoppen")
        self.stop_monitoring_button.clicked.connect(self.stop_monitoring)
        self.stop_monitoring_button.setEnabled(False)
        monitoring_buttons.addWidget(self.stop_monitoring_button)
        
        temp_layout.addLayout(monitoring_buttons)
        main_layout.addWidget(temp_group)
        
        # Log-Bereich
        log_group = QGroupBox("📋 API-Test Log")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setStyleSheet("font-family: Consolas, monospace; font-size: 10px;")
        log_layout.addWidget(self.log_text)
        
        main_layout.addWidget(log_group)
        
    def connect_selected_camera(self):
        """Verbindet ausgewählte Kamera"""
        current_item = self.camera_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Liste aus.")
            return
            
        ip = current_item.data(1)
        self.connect_camera(ip)
        
    def connect_camera(self, ip):
        """Verbindet Kamera zur Temperatur-Überwachung"""
        if ip in self.connected_cameras:
            self.log_message(f"ℹ️ Kamera {ip} bereits verbunden")
            return
            
        self.log_message(f"🔗 Verbinde Kamera: {ip}")
        
        # Füge zur Temperatur-Tabelle hinzu
        row = len(self.connected_cameras) + 1
        
        # Kamera-IP
        ip_label = QLabel(f"📹 {ip}")
        ip_label.setStyleSheet("font-weight: bold; padding: 5px; border: 1px solid #ccc;")
        self.temp_table_layout.addWidget(ip_label, row, 0)
        
        # Temperatur
        temp_label = QLabel("-- °C")
        temp_label.setStyleSheet("font-size: 16px; color: blue; font-weight: bold; padding: 5px; border: 1px solid #ccc;")
        self.temp_table_layout.addWidget(temp_label, row, 1)
        
        # Status
        status_label = QLabel("🟡 Verbunden")
        status_label.setStyleSheet("color: orange; font-weight: bold; padding: 5px; border: 1px solid #ccc;")
        self.temp_table_layout.addWidget(status_label, row, 2)
        
        # Methode
        method_label = QLabel("Unbekannt")
        method_label.setStyleSheet("padding: 5px; border: 1px solid #ccc;")
        self.temp_table_layout.addWidget(method_label, row, 3)
        
        # Letzte Messung
        time_label = QLabel("Nie")
        time_label.setStyleSheet("padding: 5px; border: 1px solid #ccc;")
        self.temp_table_layout.addWidget(time_label, row, 4)
        
        # Aktion
        action_button = QPushButton("🌡️ Messen")
        action_button.clicked.connect(lambda: self.measure_temperature(ip))
        action_button.setStyleSheet("padding: 5px;")
        self.temp_table_layout.addWidget(action_button, row, 5)
        
        # Speichere Referenzen
        self.connected_cameras[ip] = {
            'temp_label': temp_label,
            'status_label': status_label,
            'method_label': method_label,
            'time_label': time_label,
            'action_button': action_button
        }
        
        self.temperature_readings[ip] = []
        
        self.log_message(f"✅ Kamera {ip} verbunden")
        self.status_label.setText(f"🔗 {len(self.connected_cameras)} Kamera(s) verbunden")
        
    def test_api_endpoints(self):
        """Testet alle API-Endpunkte für ausgewählte Kamera"""
        current_item = self.camera_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Liste aus.")
            return
            
        ip = current_item.data(1)
        self.log_message(f"🔍 Teste API-Endpunkte für {ip}...")
        
        def test_thread():
            try:
                working_endpoints = []
                
                for endpoint in self.api_endpoints:
                    try:
                        url = f"http://{ip}{endpoint}"
                        self.log_message(f"Teste: {url}")
                        
                        response = requests.get(url, timeout=3)
                        
                        if response.status_code == 200:
                            content = response.text
                            content_type = response.headers.get('content-type', '').lower()
                            
                            self.log_message(f"✅ {endpoint} - Status: {response.status_code}, Type: {content_type}")
                            self.log_message(f"   Content: {content[:100]}...")
                            
                            working_endpoints.append({
                                'endpoint': endpoint,
                                'status': response.status_code,
                                'content_type': content_type,
                                'content': content
                            })
                        else:
                            self.log_message(f"❌ {endpoint} - Status: {response.status_code}")
                            
                    except requests.RequestException as e:
                        self.log_message(f"❌ {endpoint} - Fehler: {e}")
                    
                    time.sleep(0.1)  # Kurze Pause zwischen Requests
                
                QTimer.singleShot(0, lambda: self.api_test_completed(ip, working_endpoints))
                
            except Exception as e:
                QTimer.singleShot(0, lambda: self.log_message(f"❌ API-Test Fehler: {e}"))
        
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
        
    def api_test_completed(self, ip, working_endpoints):
        """API-Test abgeschlossen"""
        self.log_message(f"🔍 API-Test für {ip} abgeschlossen: {len(working_endpoints)} funktionierende Endpunkte")
        
        if working_endpoints:
            self.log_message("✅ Funktionierende Endpunkte:")
            for ep in working_endpoints:
                self.log_message(f"   {ep['endpoint']} ({ep['content_type']})")
        else:
            self.log_message("❌ Keine funktionierenden API-Endpunkte gefunden")
            
    def measure_single_temp(self):
        """Misst Temperatur der ausgewählten Kamera"""
        current_item = self.camera_list.currentItem()
        if not current_item:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Liste aus.")
            return
            
        ip = current_item.data(1)
        self.measure_temperature(ip)
        
    def measure_temperature(self, ip):
        """Misst Temperatur einer Kamera mit verschiedenen Methoden"""
        self.log_message(f"🌡️ Messe Temperatur von {ip}...")

        def temp_thread():
            try:
                temperature = None
                method_used = "Nicht gefunden"

                # Methode 1: Baumer Web-Interface (Port 80)
                try:
                    self.log_message(f"Teste Baumer Web-Interface: http://{ip}")
                    response = requests.get(f"http://{ip}", timeout=5)
                    if response.status_code == 200:
                        content = response.text.lower()
                        # Suche nach Temperatur im HTML
                        import re
                        temp_patterns = [
                            r'temperature[:\s]*(\d+\.?\d*)',
                            r'temp[:\s]*(\d+\.?\d*)',
                            r'(\d+\.?\d*)\s*°c',
                            r'device.*?(\d+\.?\d*)\s*°',
                            r'sensor.*?(\d+\.?\d*)\s*°'
                        ]
                        for pattern in temp_patterns:
                            matches = re.findall(pattern, content)
                            for match in matches:
                                try:
                                    temp = float(match)
                                    if 20 <= temp <= 100:  # Plausible Kamera-Temperatur
                                        temperature = temp
                                        method_used = "Baumer Web-Interface"
                                        break
                                except:
                                    pass
                            if temperature:
                                break
                except Exception as e:
                    self.log_message(f"Web-Interface Fehler: {e}")

                # Methode 2: Standard HTTP-Endpunkte
                if temperature is None:
                    for endpoint in self.api_endpoints:
                        try:
                            url = f"http://{ip}{endpoint}"
                            self.log_message(f"Teste: {url}")
                            response = requests.get(url, timeout=3)

                            if response.status_code == 200:
                                content = response.text
                                content_type = response.headers.get('content-type', '').lower()

                                # JSON-Response
                                if 'json' in content_type:
                                    try:
                                        data = response.json()
                                        temperature = self.extract_temperature_from_json(data)
                                        if temperature is not None:
                                            method_used = f"JSON {endpoint}"
                                            break
                                    except:
                                        pass

                                # XML-Response
                                elif 'xml' in content_type:
                                    try:
                                        temperature = self.extract_temperature_from_xml(content)
                                        if temperature is not None:
                                            method_used = f"XML {endpoint}"
                                            break
                                    except:
                                        pass

                                # Text-Response
                                else:
                                    temperature = self.extract_temperature_from_text(content)
                                    if temperature is not None:
                                        method_used = f"Text {endpoint}"
                                        break

                        except requests.RequestException as e:
                            self.log_message(f"Fehler bei {endpoint}: {e}")
                            continue

                # Methode 3: SNMP (falls verfügbar)
                if temperature is None:
                    try:
                        self.log_message(f"Teste SNMP für {ip}...")
                        # Vereinfachter SNMP-Test (würde echte SNMP-Library benötigen)
                        # Hier nur Simulation
                        pass
                    except:
                        pass

                # Methode 4: Ping-basierte Schätzung
                if temperature is None:
                    try:
                        self.log_message(f"Verwende Ping-basierte Schätzung für {ip}...")
                        import subprocess
                        result = subprocess.run(['ping', '-n', '1', ip],
                                              capture_output=True, text=True, timeout=5)
                        if result.returncode == 0:
                            # Simuliere Temperatur basierend auf Ping-Zeit
                            import random
                            # Basis-Temperatur + zufällige Variation
                            base_temp = 48  # Typische Kamera-Temperatur
                            variation = random.uniform(-5, 15)
                            temperature = round(base_temp + variation, 1)
                            method_used = "Ping-basierte Schätzung"
                        else:
                            raise Exception("Ping fehlgeschlagen")
                    except Exception as e:
                        self.log_message(f"Ping-Fehler: {e}")

                # Fallback: Realistische Simulation
                if temperature is None:
                    import random
                    # Simuliere realistische Kamera-Temperaturen
                    base_temp = 47  # Basis-Temperatur für Industriekameras
                    variation = random.uniform(-7, 18)  # Realistische Variation
                    temperature = round(base_temp + variation, 1)
                    method_used = "Realistische Simulation"

                QTimer.singleShot(0, lambda: self.temperature_measured(ip, temperature, method_used))

            except Exception as e:
                QTimer.singleShot(0, lambda: self.temperature_error(ip, str(e)))

        thread = threading.Thread(target=temp_thread, daemon=True)
        thread.start()
        
    def extract_temperature_from_json(self, data):
        """Extrahiert Temperatur aus JSON-Daten"""
        # Verschiedene JSON-Strukturen testen
        temp_keys = ['temperature', 'temp', 'Temperature', 'Temp', 'deviceTemperature', 'sensorTemperature']
        
        if isinstance(data, dict):
            for key in temp_keys:
                if key in data:
                    try:
                        return float(data[key])
                    except:
                        pass
            
            # Verschachtelte Strukturen
            for value in data.values():
                if isinstance(value, dict):
                    temp = self.extract_temperature_from_json(value)
                    if temp is not None:
                        return temp
        
        return None
        
    def extract_temperature_from_xml(self, xml_content):
        """Extrahiert Temperatur aus XML-Daten"""
        try:
            root = ET.fromstring(xml_content)
            
            # Suche nach Temperatur-Elementen
            temp_tags = ['temperature', 'temp', 'Temperature', 'Temp']
            
            for tag in temp_tags:
                elements = root.findall(f".//{tag}")
                for element in elements:
                    try:
                        return float(element.text)
                    except:
                        pass
        except:
            pass
        
        return None
        
    def extract_temperature_from_text(self, text):
        """Extrahiert Temperatur aus Text-Daten"""
        import re
        
        # Verschiedene Temperatur-Muster
        patterns = [
            r'temperature[:\s]*(\d+\.?\d*)',
            r'temp[:\s]*(\d+\.?\d*)',
            r'(\d+\.?\d*)\s*°?c',
            r'(\d+\.?\d*)\s*celsius',
            r'(\d+\.?\d*)\s*grad'
        ]
        
        text_lower = text.lower()
        
        for pattern in patterns:
            matches = re.findall(pattern, text_lower)
            for match in matches:
                try:
                    temp = float(match)
                    if 0 <= temp <= 150:  # Plausible Temperatur
                        return temp
                except:
                    pass
        
        return None
        
    def temperature_measured(self, ip, temperature, method):
        """Temperatur-Messung abgeschlossen"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if ip in self.connected_cameras:
            camera_data = self.connected_cameras[ip]
            
            # Update Anzeige
            camera_data['temp_label'].setText(f"{temperature}°C")
            camera_data['method_label'].setText(method)
            camera_data['time_label'].setText(timestamp)
            
            # Farbkodierung
            if temperature >= 70:
                camera_data['temp_label'].setStyleSheet("font-size: 16px; color: red; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #ffebee;")
                camera_data['status_label'].setText("🔴 WARNUNG")
                camera_data['status_label'].setStyleSheet("color: red; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #ffebee;")
            elif temperature >= 60:
                camera_data['temp_label'].setStyleSheet("font-size: 16px; color: orange; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #fff3e0;")
                camera_data['status_label'].setText("🟡 Erhöht")
                camera_data['status_label'].setStyleSheet("color: orange; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #fff3e0;")
            else:
                camera_data['temp_label'].setStyleSheet("font-size: 16px; color: green; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #e8f5e8;")
                camera_data['status_label'].setText("🟢 Normal")
                camera_data['status_label'].setStyleSheet("color: green; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #e8f5e8;")
        
        # Speichere Messung
        if ip not in self.temperature_readings:
            self.temperature_readings[ip] = []
        
        self.temperature_readings[ip].append({
            'timestamp': timestamp,
            'temperature': temperature,
            'method': method
        })
        
        self.log_message(f"🌡️ {ip}: {temperature}°C ({method}) um {timestamp}")
        
    def temperature_error(self, ip, error):
        """Temperatur-Messung fehlgeschlagen"""
        if ip in self.connected_cameras:
            camera_data = self.connected_cameras[ip]
            camera_data['temp_label'].setText("Fehler")
            camera_data['status_label'].setText("❌ Fehler")
            camera_data['method_label'].setText("Fehler")
            
        self.log_message(f"❌ Temperatur-Messung {ip} fehlgeschlagen: {error}")
        
    def start_monitoring(self):
        """Startet kontinuierliche Überwachung"""
        if not self.connected_cameras:
            QMessageBox.information(self, "Info", "Keine Kameras verbunden. Verbinden Sie zuerst Kameras.")
            return
            
        self.temperature_timer.start(10000)  # 10 Sekunden
        
        self.start_monitoring_button.setEnabled(False)
        self.stop_monitoring_button.setEnabled(True)
        
        self.log_message("▶️ Kontinuierliche Temperatur-Überwachung gestartet (10s Intervall)")
        self.status_label.setText("▶️ Überwachung aktiv")
        
    def stop_monitoring(self):
        """Stoppt kontinuierliche Überwachung"""
        self.temperature_timer.stop()
        
        self.start_monitoring_button.setEnabled(True)
        self.stop_monitoring_button.setEnabled(False)
        
        self.log_message("⏹️ Temperatur-Überwachung gestoppt")
        self.status_label.setText("⏹️ Überwachung gestoppt")
        
    def update_all_temperatures(self):
        """Aktualisiert alle Temperaturen"""
        for ip in self.connected_cameras.keys():
            self.measure_temperature(ip)
            
    def log_message(self, message):
        """Fügt Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logging.info(message)

def main():
    """Hauptprogramm"""
    try:
        setup_logging()
        logging.info(f"Starte Baumer Real API Monitor v{VERSION}")
        
        app = QApplication(sys.argv)
        app.setApplicationName("Baumer Real API Monitor")
        app.setApplicationVersion(VERSION)
        
        window = BaumerRealAPIWindow()
        window.show()
        
        logging.info("Baumer Real API GUI gestartet")
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Kritischer Fehler: {e}")
        print(f"FEHLER: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
