import logging
import os
from datetime import datetime

# Konfigurationskonstanten
DEFAULT_TEMP_INTERVAL = 30  # Sekunden zwischen Temperaturmessungen
MAX_TEMP_HISTORY_DAYS = 30  # Maximale Anzahl Tage für Temperaturhistorie
DATABASE_PATH = "data/temperature_data.db"
LOG_PATH = "logs"

# Temperatur-Grenzwerte
TEMP_WARNING_THRESHOLD = 60.0  # °C
TEMP_CRITICAL_THRESHOLD = 70.0  # °C

# GUI-Konfiguration
PLOT_UPDATE_INTERVAL = 5000  # ms
PLOT_MAX_POINTS = 1000  # Maximale Anzahl Punkte im Live-Plot

def setup_logging():
    """Logging-Konfiguration"""
    os.makedirs(LOG_PATH, exist_ok=True)
    
    log_filename = os.path.join(LOG_PATH, f"baumer_temp_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )

def ensure_data_directory():
    """Erstellt Datenverzeichnis falls nicht vorhanden"""
    os.makedirs(os.path.dirname(DATABASE_PATH), exist_ok=True)
