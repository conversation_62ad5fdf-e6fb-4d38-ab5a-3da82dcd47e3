<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.CamInfoListIterator Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00887.html">CamInfoListIterator</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00884.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.CamInfoListIterator Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00088.html">Supporting Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides iterator functionality for the <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a>.  
 <a href="a00887.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.CamInfoListIterator:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00887.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a50b440da3ad2cefd5a5c2c09e3826e7b"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00887.html#a50b440da3ad2cefd5a5c2c09e3826e7b">__init__</a> (self, *args)</td></tr>
<tr class="separator:a50b440da3ad2cefd5a5c2c09e3826e7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c0f7b3446b84a55ceb127dd1951a684"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00887.html#a3c0f7b3446b84a55ceb127dd1951a684">__eq__</a> (self, &quot;CamInfoListIterator&quot; object)</td></tr>
<tr class="memdesc:a3c0f7b3446b84a55ceb127dd1951a684"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the "==" operator.  <a href="#a3c0f7b3446b84a55ceb127dd1951a684">More...</a><br /></td></tr>
<tr class="separator:a3c0f7b3446b84a55ceb127dd1951a684"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a249adfd21e3a4d0f3f84af5fe79cfa12"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00887.html#a249adfd21e3a4d0f3f84af5fe79cfa12">__ne__</a> (self, &quot;CamInfoListIterator&quot; object)</td></tr>
<tr class="memdesc:a249adfd21e3a4d0f3f84af5fe79cfa12"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the "=!" operator.  <a href="#a249adfd21e3a4d0f3f84af5fe79cfa12">More...</a><br /></td></tr>
<tr class="separator:a249adfd21e3a4d0f3f84af5fe79cfa12"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6d64ea10ac1c8cc494217b4d6f0c074"><td class="memItemLeft" align="right" valign="top">&quot;CamInfo&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00887.html#ae6d64ea10ac1c8cc494217b4d6f0c074">__ref__</a> (self)</td></tr>
<tr class="memdesc:ae6d64ea10ac1c8cc494217b4d6f0c074"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the reference.  <a href="#ae6d64ea10ac1c8cc494217b4d6f0c074">More...</a><br /></td></tr>
<tr class="separator:ae6d64ea10ac1c8cc494217b4d6f0c074"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:aef55df7d9561f35e7c1a62f219281af5"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00887.html#aef55df7d9561f35e7c1a62f219281af5">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:aef55df7d9561f35e7c1a62f219281af5"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#aef55df7d9561f35e7c1a62f219281af5">More...</a><br /></td></tr>
<tr class="separator:aef55df7d9561f35e7c1a62f219281af5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Provides iterator functionality for the <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a>. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a50b440da3ad2cefd5a5c2c09e3826e7b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a50b440da3ad2cefd5a5c2c09e3826e7b">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.CamInfoListIterator.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">   \brief    Constructor
</pre> <dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>object</b> (optional) A <a class="el" href="a00819.html" title="Provides iterator functionality for the FeatureList.">FeatureListIterator</a> object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a3c0f7b3446b84a55ceb127dd1951a684"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c0f7b3446b84a55ceb127dd1951a684">&#9670;&nbsp;</a></span>__eq__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.CamInfoListIterator.__eq__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;CamInfoListIterator&quot;&#160;</td>
          <td class="paramname"><em>object</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Provides the "==" operator. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">object</td><td>A <a class="el" href="a00887.html" title="Provides iterator functionality for the CamInfoList.">CamInfoListIterator</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="a249adfd21e3a4d0f3f84af5fe79cfa12"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a249adfd21e3a4d0f3f84af5fe79cfa12">&#9670;&nbsp;</a></span>__ne__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.CamInfoListIterator.__ne__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;CamInfoListIterator&quot;&#160;</td>
          <td class="paramname"><em>object</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Provides the "=!" operator. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">object</td><td>A <a class="el" href="a00887.html" title="Provides iterator functionality for the CamInfoList.">CamInfoListIterator</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="ae6d64ea10ac1c8cc494217b4d6f0c074"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae6d64ea10ac1c8cc494217b4d6f0c074">&#9670;&nbsp;</a></span>__ref__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;CamInfo&quot; neoapi.CamInfoListIterator.__ref__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Provides the reference. </p>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00883.html" title="Camera info container class which offers basic information about an available camera If neoapi....">CamInfo</a> object </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="aef55df7d9561f35e7c1a62f219281af5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aef55df7d9561f35e7c1a62f219281af5">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.CamInfoListIterator.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
