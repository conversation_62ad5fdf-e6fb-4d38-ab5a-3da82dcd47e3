#!/usr/bin/env python3
"""
Einfacher GUI-Test für das Baumer Temperatur Monitor System
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import QTimer

class SimpleTestWindow(QMainWindow):
    """Einfaches Test-Fenster"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Temperatur Monitor - Test")
        self.setGeometry(100, 100, 600, 400)
        
        # Zentrales Widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout
        layout = QVBoxLayout(central_widget)
        
        # Titel
        title_label = QLabel("Baumer Temperatur Monitor - Test")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # Status
        self.status_label = QLabel("System bereit")
        layout.addWidget(self.status_label)
        
        # Test-Button
        self.test_button = QPushButton("Test Kamera-Discovery")
        self.test_button.clicked.connect(self.test_discovery)
        layout.addWidget(self.test_button)
        
        # Log-Ausgabe
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        layout.addWidget(self.log_text)
        
        # Timer für Status-Updates
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        self.timer.start(1000)
        
        self.log("GUI erfolgreich initialisiert")
        
    def log(self, message):
        """Fügt Nachricht zum Log hinzu"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")
        print(f"LOG: {message}")
        
    def get_timestamp(self):
        """Gibt aktuellen Zeitstempel zurück"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
        
    def test_discovery(self):
        """Testet Kamera-Discovery"""
        self.test_button.setEnabled(False)
        self.test_button.setText("Teste...")
        self.status_label.setText("Teste Kamera-Discovery...")
        
        try:
            self.log("Starte Discovery-Test...")
            
            # Teste Socket-Erstellung
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.close()
            self.log("✓ Socket-Test erfolgreich")
            
            # Teste HTTP-Requests
            import requests
            try:
                response = requests.get("http://httpbin.org/status/200", timeout=3)
                self.log("✓ HTTP-Test erfolgreich")
            except:
                self.log("⚠ HTTP-Test fehlgeschlagen (Netzwerk)")
            
            # Teste UPnP (vereinfacht)
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.settimeout(2)
                sock.sendto(b"TEST", ('239.255.255.250', 1900))
                sock.close()
                self.log("✓ UPnP-Socket-Test erfolgreich")
            except Exception as e:
                self.log(f"⚠ UPnP-Test fehlgeschlagen: {e}")
            
            self.log("Discovery-Test abgeschlossen")
            self.status_label.setText("Test abgeschlossen")
            
        except Exception as e:
            self.log(f"✗ Fehler beim Discovery-Test: {e}")
            self.status_label.setText(f"Test-Fehler: {e}")
            
        finally:
            self.test_button.setEnabled(True)
            self.test_button.setText("Test Kamera-Discovery")
            
    def update_status(self):
        """Aktualisiert Status"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        if "bereit" in self.status_label.text().lower():
            self.status_label.setText(f"System bereit - {current_time}")

def main():
    """Hauptfunktion"""
    print("Starte GUI-Test...")
    
    try:
        app = QApplication(sys.argv)
        app.setApplicationName("Baumer Test")
        
        window = SimpleTestWindow()
        window.show()
        
        print("GUI-Fenster geöffnet")
        print("Drücken Sie Ctrl+C zum Beenden")
        
        sys.exit(app.exec_())
        
    except KeyboardInterrupt:
        print("\nProgramm durch Benutzer beendet")
        sys.exit(0)
    except Exception as e:
        print(f"FEHLER: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
