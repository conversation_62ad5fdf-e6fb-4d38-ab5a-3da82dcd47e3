<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Machine vision basics — EMVA, GenICam, SFNC, what?</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Machine vision basics — EMVA, GenICam, SFNC, what? </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><h1><a class="anchor" id="autotoc_md93"></a>
Introduction</h1>
<p>Compared to a typical USB video device (UVC) such as a standard webcam, modern machine vision cameras provide much more functionality and a lot more configuration. To enable interoperability between different camera devices and software applications the <b>EMVA — the European Machine Vision Alliance</b> is maintaining a set of standards. The big advantage for the customer is, that makes it trivial to switch from one camera to another model or even to another interface without changes to the software application controlling it.</p>
<h1><a class="anchor" id="autotoc_md94"></a>
GenTL — Generic Transportation Layer</h1>
<p>Machine vision cameras connect via different interfaces to a host computer. Baumer provides cameras which connect via USB3 <b>U3V — USB3 Vision</b> and <b>GigE — Gigabit Ethernet</b>. The GenTL standard ensures that the software application can easily work with both interfaces.</p>
<h1><a class="anchor" id="autotoc_md95"></a>
GenICam — Generic Interface for Cameras</h1>
<p>The GenICam standard (also called GenApi) defines the mechanism on how a camera announces its features and capabilities to the software controlling it. This is done via an XML-file which is downloaded from the camera while it is connected. It provides the software with all the information how features can be used and provided attributes for each feature.</p>
<p>The neoAPI encapsulates the GenAPI and provided you with an easy way to access the features and it's attributes. See the example below to get an idea about how to handle GenICam features</p>
<p>The Feature access mode helps you to understand what you can or can't do. They are defined in the following order:</p>
<ul>
<li>Not implemented — the camera does not support the feature</li>
<li>Not available — the feature might be implemented but temporarily not available</li>
<li>Write only — the feature is only writable, but not readable</li>
<li>Read only — the feature is only readable, but not writable</li>
<li>Readable and writable — full read and write access to the feature</li>
</ul>
<blockquote class="doxtable">
<p><b>Attention</b></p>
<p>Please be aware, that the value and the access mode of a camera features can change at any point, for example switching on the AutoExposure will set the ExposureTime feature to read only and change its value as required. </p>
</blockquote>
<p><br />
</p>
<div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line"></div><div class="line">cam = <a class="code" href="a00863.html">neoapi.Cam</a>()                  <span class="comment"># Create the camera object</span></div><div class="line">cam.Connect()                       <span class="comment"># Connect to a physical camera device</span></div><div class="line">value = cam.f.Gain.value            <span class="comment"># Get the current value of a feature</span></div><div class="line">max   = cam.f.Gain.GetMax()         <span class="comment"># Get the maximum allowed value</span></div><div class="line">desc  = cam.f.Gain.GetDescription() <span class="comment"># Get the description of a feature</span></div><div class="line"></div><div class="line"><span class="comment"># Check if a feature is available for a camera and if it is writable</span></div><div class="line"><span class="keywordflow">if</span> (cam.f.Gain.IsAvailable() <span class="keywordflow">and</span> cam.f.Gain.IsWriteable()) :</div><div class="line">    cam.f.Gain.value = cam.f.Gain.GetMin()</div></div><!-- fragment --> <div class="caption">A neoAPI Python example showing how to work with features. The neoAPI provides you with convenient auto-complete for <br />
the feature names and the available attributes as well as relevant feature documentation.</div><h1><a class="anchor" id="autotoc_md96"></a>
SFNC — Standard Features Naming Convention</h1>
<p>The last important standard to know with is the SFNC. This standard ensures all manufacturers of camera devices adhere to a unified convention for the names of the features. So each manufacturer who implements for example an auto exposure will name the feature “ExposureAuto” and adhere to the standard values “Off”, “Once” and “Continuous”. Again, this is important because it ensures you don't have to change your software to work with different cameras.</p>
<p>As cameras and features are constantly evolving the SFNC is evolving as well. Each camera does therefore adhere to a specific version of the SFNC. This Version can be requested from the camera to understand what you are dealing with.</p>
<p>Furthermore, manufacturers have the option to include non SFNC features. Those are typically features which will be standardised in future versions of the SFNC. These features are used in the same way as the SFNC features and each manufacturer will provide information about those features typically in the camera documentation.</p>
<p>The neoAPI helps you with the SFNC features by providing auto-complete for the feature names and its attributes and includes the feature description so it can be viewed inside the IDE (like standard method documentations) and in the HTML documentation.</p>
<h1><a class="anchor" id="autotoc_md97"></a>
Further information</h1>
<ul>
<li><a href="https://www.emva.org/wp-content/uploads/GenICam_Standard_v2_1_1.pdf">GenICamTM GenApi Standard Document in PDF format</a></li>
<li><a href="https://www.emva.org/wp-content/uploads/GenICam_SFNC_v2_5.pdf">GenICamTM Standard Features Naming Convention (SFNC)</a> </li>
</ul>
</div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
