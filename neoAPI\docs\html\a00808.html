<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Member List</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00811.html">Feature</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">neoapi.Feature Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="a00811.html">neoapi.Feature</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a403a136c5a7b4be1caf7a51c48919c77">__init__</a>(self, *args)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#ad8c6f3618922f4ca5dcd34d852688a67">Execute</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a500821f9f5bc1adde068befc917873c6">GetBool</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a625b369e4dc91cda7b3c41f8d4bbefde">GetDescription</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a598660ec01fe189e8cc2ba6e219e8d67">GetDisplayName</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a120d6ffd8f44ade71ce041d6320751d8">GetDouble</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#ae53c3fcfa6b3fc21aad9f5e72141dd5b">GetDoubleInc</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#ac956e51d81c4c1c3b63eea4c9ea476d6">GetDoubleMax</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#aa57c67d9d005ef0a56b9c493c4736fe6">GetDoubleMin</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#aab30d096e2fd16d4053d7c75441d7a7d">GetDoublePrecision</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a251c3b500b730799f531cebe1e4ab652">GetEnumValueList</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a92227cda96f3db405a9f5092cb5a99fc">GetInt</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a470ae7fcbfa89f856ce7412e6bbc96fb">GetInterface</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a13e5ac4d86f0dd5701b0cc618fd5b9ea">GetIntInc</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#ae3dbe0271eede2408067c5191851fe37">GetIntMax</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a41f0e484b51eb348742c03dcec66a54c">GetIntMin</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#afd2ab067299afe1dfc06b10704bfaa6d">GetMaxStringLength</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a81195421dded0f9e1e98999c517161f4">GetName</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a151f1a287ed06c1ddb8f486f6a775c0d">GetRegister</a>(self, &quot;bytearray&quot; buffer, &quot;int&quot; length)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a0392d7a753c6ae498924d5e1547c409e">GetRegisterAddress</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a96a8ba2656df57ac888d28745015d4f2">GetRegisterLength</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a52b73bcde3be16e21543aaf168956176">GetRepresentation</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a4928a45027855b138d35805013a2b89d">GetSelectedFeatureList</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a4896531d4272849e7552eec94cafb6da">GetString</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a5d1a2836faa0fae35d3dfe2208716759">GetToolTip</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#aef22ab95afe291708798dc606b3ff046">GetUnit</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a65ecf847fa6ac9130f09d042bafdafa4">GetValue</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#aec69f7f7673a58d0730969f1d9c8056d">GetVisibility</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#acd8890c4c4e5d90ff9aabd069c1e9af4">IsAvailable</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a5f725a3ccd37f41f5f154784075adea7">IsDone</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a6012ab5cf826fbf4e47147e56f2c00cd">IsReadable</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a85c27bf170bda573befcdbe84de2cd07">IsSelector</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a0c3ef92f53d96f95324e130c56ecb1bb">IsWritable</a>(self)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a5b4b8294c5c3def133e1e6070689d81d">SetBool</a>(self, &quot;bool&quot; value)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#a9be4d0cfa11eec57556f4b7b3bcbc626">SetDouble</a>(self, &quot;float&quot; value)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a75e03ac71173b851ae10ccdba70d0399">SetInt</a>(self, &quot;int&quot; value)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#afee1c8bcca5a1e4edbee79c441217fdd">SetRegister</a>(self, &quot;bytearray&quot; buffer, &quot;int&quot; length)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#ab8402ad968e3bd0c4ef41a517224576f">SetString</a>(self, &quot;str&quot; value)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#af9ace9058d51b65cbecd9297a245c9f6">SetValue</a>(self, value)</td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00811.html#a7433fbf6a07e587ba3dc7bedff7e420f">thisown</a></td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00811.html#ad9353d112a25ec31e5fee21dd4b0e50b">value</a></td><td class="entry"><a class="el" href="a00811.html">neoapi.Feature</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
