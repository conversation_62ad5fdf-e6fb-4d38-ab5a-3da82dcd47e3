VERSION = "1.0.6"

import sys
import os
import logging
import socket
import threading
import time
import requests
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox)
from PyQt5.QtCore import QTimer

# Standalone Logging Setup
def setup_logging():
    """Konfiguriert Logging für standalone EXE"""
    try:
        os.makedirs('logs', exist_ok=True)
        log_filename = os.path.join('logs', f"baumer_temp_{datetime.now().strftime('%Y%m%d')}.log")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    except Exception as e:
        print(f"Logging Setup Fehler: {e}")

class StandaloneBaumerWindow(QMainWindow):
    """Standalone Baumer Kamera Monitor ohne externe Abhängigkeiten"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Kamera Monitor v1.0.6 - Standalone")
        self.setGeometry(100, 100, 1000, 800)
        
        # Bekannte Kamera-IPs
        self.known_cameras = [
            "***************",
            "**************", 
            "**************",
            "**************"
        ]
        
        # Initialisiere Komponenten
        self.discovered_cameras = []
        self.connected_cameras = {}
        
        # GUI aufbauen
        self.init_ui()
        
        logging.info("Standalone Baumer GUI initialisiert")
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hauptlayout
        main_layout = QVBoxLayout(central_widget)
        
        # Titel
        title_label = QLabel("🔧 Baumer Kamera Temperatur Monitor v1.0.6")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: blue;")
        main_layout.addWidget(title_label)
        
        # Netzwerk-Info
        network_info = QLabel("🌐 Ethernet 5 - Baumer-Kameras im 169.254.x.x Netzwerk")
        network_info.setStyleSheet("font-size: 14px; color: green; margin: 5px; background-color: #e8f5e8; padding: 8px; border-radius: 5px;")
        main_layout.addWidget(network_info)
        
        # Bekannte Kameras Info
        known_info = QLabel(f"📋 Vorkonfiguriert für {len(self.known_cameras)} Baumer-Kameras")
        known_info.setStyleSheet("font-size: 12px; color: blue; margin: 5px;")
        main_layout.addWidget(known_info)
        
        # Tab-Widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Kameras-Tab
        self.init_camera_tab()
        
        # Temperatur-Tab
        self.init_temperature_tab()
        
        # Log-Tab
        self.init_log_tab()
        
        # Status-Label
        self.status_label = QLabel("✅ Bereit für Baumer-Kamera-Tests")
        self.status_label.setStyleSheet("padding: 5px; background-color: #f0f8ff; border: 1px solid #ccc;")
        main_layout.addWidget(self.status_label)
        
    def init_camera_tab(self):
        """Initialisiert Kamera-Tab"""
        camera_widget = QWidget()
        layout = QVBoxLayout(camera_widget)
        
        # Bekannte Kameras Gruppe
        known_group = QGroupBox("🎯 Bekannte Baumer-Kameras")
        known_layout = QVBoxLayout(known_group)
        
        # Info-Text
        info_label = QLabel("Diese Kameras wurden bereits erkannt. Klicken Sie 'Alle testen' um sie zu überprüfen:")
        info_label.setStyleSheet("color: #666; margin: 5px;")
        known_layout.addWidget(info_label)
        
        # Bekannte Kameras Liste
        self.known_cameras_list = QListWidget()
        for ip in self.known_cameras:
            item = QListWidgetItem(f"🔹 VCXG-13M - {ip}")
            item.setData(1, ip)
            self.known_cameras_list.addItem(item)
        known_layout.addWidget(self.known_cameras_list)
        
        # Bekannte Kameras Buttons
        known_button_layout = QHBoxLayout()
        
        self.test_all_button = QPushButton("🚀 Alle bekannten Kameras testen")
        self.test_all_button.clicked.connect(self.test_all_known_cameras)
        self.test_all_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50; 
                color: white; 
                font-weight: bold; 
                padding: 10px; 
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        known_button_layout.addWidget(self.test_all_button)
        
        self.test_selected_button = QPushButton("🔍 Ausgewählte testen")
        self.test_selected_button.clicked.connect(self.test_selected_camera)
        self.test_selected_button.setStyleSheet("padding: 8px; font-weight: bold;")
        known_button_layout.addWidget(self.test_selected_button)
        
        known_layout.addLayout(known_button_layout)
        layout.addWidget(known_group)
        
        # Scan-Gruppe
        scan_group = QGroupBox("🔧 Erweiterte Scan-Optionen")
        scan_layout = QVBoxLayout(scan_group)
        
        # Scan-Buttons
        scan_button_layout = QHBoxLayout()
        
        self.quick_scan_button = QPushButton("⚡ Schneller Scan (1-50)")
        self.quick_scan_button.clicked.connect(self.quick_scan)
        scan_button_layout.addWidget(self.quick_scan_button)
        
        self.targeted_scan_button = QPushButton("🎯 Gezielter Scan")
        self.targeted_scan_button.clicked.connect(self.targeted_scan)
        self.targeted_scan_button.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold; padding: 8px;")
        scan_button_layout.addWidget(self.targeted_scan_button)
        
        self.full_scan_button = QPushButton("🌐 Vollständiger Scan (1-254)")
        self.full_scan_button.clicked.connect(self.full_scan)
        scan_button_layout.addWidget(self.full_scan_button)
        
        scan_layout.addLayout(scan_button_layout)
        layout.addWidget(scan_group)
        
        # Gefundene Kameras
        found_group = QGroupBox("📊 Scan-Ergebnisse")
        found_layout = QVBoxLayout(found_group)
        
        self.camera_list = QListWidget()
        found_layout.addWidget(self.camera_list)
        
        # Kamera-Aktionen
        camera_action_layout = QHBoxLayout()
        
        self.connect_button = QPushButton("🔗 Verbinden")
        self.connect_button.clicked.connect(self.connect_camera)
        self.connect_button.setStyleSheet("background-color: #FF9800; color: white; font-weight: bold; padding: 8px;")
        camera_action_layout.addWidget(self.connect_button)
        
        self.temp_test_button = QPushButton("🌡️ Temperatur testen")
        self.temp_test_button.clicked.connect(self.test_temperature)
        camera_action_layout.addWidget(self.temp_test_button)
        
        self.clear_button = QPushButton("🗑️ Ergebnisse leeren")
        self.clear_button.clicked.connect(self.camera_list.clear)
        camera_action_layout.addWidget(self.clear_button)
        
        found_layout.addLayout(camera_action_layout)
        layout.addWidget(found_group)
        
        self.tab_widget.addTab(camera_widget, "🔧 Baumer Kameras")
        
    def init_temperature_tab(self):
        """Initialisiert Temperatur-Tab"""
        temp_widget = QWidget()
        layout = QVBoxLayout(temp_widget)
        
        temp_label = QLabel("🌡️ Temperatur-Überwachung")
        temp_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(temp_label)
        
        # Verbundene Kameras
        connected_group = QGroupBox("📡 Aktive Kamera-Verbindungen")
        connected_layout = QVBoxLayout(connected_group)
        
        self.connected_cameras_widget = QWidget()
        self.connected_layout = QGridLayout(self.connected_cameras_widget)
        connected_layout.addWidget(self.connected_cameras_widget)
        
        layout.addWidget(connected_group)
        
        # Temperatur-Verlauf Platzhalter
        plot_group = QGroupBox("📈 Temperaturverlauf")
        plot_layout = QVBoxLayout(plot_group)
        
        plot_placeholder = QLabel("Hier wird der Temperaturverlauf angezeigt\n(Wird aktiviert wenn Kameras verbunden sind)")
        plot_placeholder.setStyleSheet("border: 2px dashed #ccc; padding: 40px; text-align: center; color: #666; font-size: 14px;")
        plot_layout.addWidget(plot_placeholder)
        
        layout.addWidget(plot_group)
        
        self.tab_widget.addTab(temp_widget, "🌡️ Temperatur")
        
    def init_log_tab(self):
        """Initialisiert Log-Tab"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        log_label = QLabel("📋 System-Log")
        log_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("font-family: Consolas, monospace; font-size: 10px;")
        layout.addWidget(self.log_text)
        
        # Log-Buttons
        log_button_layout = QHBoxLayout()
        
        clear_log_button = QPushButton("🗑️ Log leeren")
        clear_log_button.clicked.connect(self.log_text.clear)
        log_button_layout.addWidget(clear_log_button)
        
        save_log_button = QPushButton("💾 Log speichern")
        save_log_button.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_log_button)
        
        layout.addLayout(log_button_layout)
        
        self.tab_widget.addTab(log_widget, "📋 System-Log")
        
    def test_all_known_cameras(self):
        """Testet alle bekannten Kameras"""
        self.log_message("=== TESTE ALLE BEKANNTEN BAUMER-KAMERAS ===")
        self.status_label.setText("🔍 Teste alle bekannten Baumer-Kameras...")
        
        self.test_all_button.setEnabled(False)
        self.test_all_button.setText("🔄 Teste...")
        
        def test_thread():
            try:
                results = []
                
                for i, ip in enumerate(self.known_cameras):
                    self.log_message(f"Teste Kamera {i+1}/{len(self.known_cameras)}: {ip}")
                    
                    # Teste verschiedene Ports
                    ports_to_test = [80, 8080, 554, 443]
                    open_ports = []
                    
                    for port in ports_to_test:
                        try:
                            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                            sock.settimeout(2)
                            result = sock.connect_ex((ip, port))
                            sock.close()
                            
                            if result == 0:
                                open_ports.append(port)
                                
                        except Exception as e:
                            self.log_message(f"Port {port} Test Fehler: {e}")
                    
                    # HTTP-Test
                    http_response = None
                    if 80 in open_ports or 8080 in open_ports:
                        for port in [80, 8080]:
                            if port in open_ports:
                                try:
                                    response = requests.get(f"http://{ip}:{port}", timeout=3)
                                    http_response = response.status_code
                                    break
                                except:
                                    pass
                    
                    if open_ports:
                        results.append({
                            'ip': ip,
                            'status': 'Erreichbar',
                            'ports': open_ports,
                            'http': http_response,
                            'model': 'VCXG-13M'
                        })
                        self.log_message(f"✅ {ip} - Erreichbar (Ports: {open_ports})")
                    else:
                        results.append({
                            'ip': ip,
                            'status': 'Nicht erreichbar',
                            'ports': [],
                            'http': None,
                            'model': 'VCXG-13M'
                        })
                        self.log_message(f"❌ {ip} - Nicht erreichbar")
                    
                    time.sleep(0.5)  # Kurze Pause zwischen Tests
                
                # Update GUI
                QTimer.singleShot(0, lambda: self.test_all_completed(results))
                
            except Exception as e:
                QTimer.singleShot(0, lambda: self.test_error(str(e)))
        
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
        
    def test_all_completed(self, results):
        """Alle Tests abgeschlossen"""
        self.test_all_button.setEnabled(True)
        self.test_all_button.setText("🚀 Alle bekannten Kameras testen")
        
        # Füge Ergebnisse zur Liste hinzu
        reachable_count = 0
        for result in results:
            if result['status'] == 'Erreichbar':
                status_text = f"✅ Erreichbar (Ports: {result['ports']})"
                if result['http']:
                    status_text += f", HTTP: {result['http']}"
                item_text = f"{result['ip']} - {result['model']} ({status_text})"
                reachable_count += 1
            else:
                item_text = f"{result['ip']} - {result['model']} (❌ Nicht erreichbar)"
                
            item = QListWidgetItem(item_text)
            item.setData(1, result['ip'])
            self.camera_list.addItem(item)
        
        self.status_label.setText(f"✅ Test abgeschlossen: {reachable_count}/{len(results)} Kameras erreichbar")
        self.log_message(f"=== TEST ABGESCHLOSSEN: {reachable_count}/{len(results)} Kameras erreichbar ===")
        
        if reachable_count == 0:
            QMessageBox.warning(self, "Keine Kameras gefunden", 
                              "Keine Baumer-Kameras erreichbar!\n\n"
                              "Mögliche Ursachen:\n"
                              "• Kameras sind ausgeschaltet\n"
                              "• Ethernet 5 nicht verbunden\n"
                              "• Falsches Netzwerk-Segment\n"
                              "• Firewall blockiert Verbindung")
        
    def test_selected_camera(self):
        """Testet ausgewählte Kamera"""
        current_item = self.known_cameras_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"Teste ausgewählte Kamera: {ip}")
            self.test_single_ip(ip)
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Liste aus.")
            
    def test_single_ip(self, ip):
        """Testet einzelne IP"""
        self.log_message(f"🔍 Detailtest für {ip}...")
        self.status_label.setText(f"🔍 Teste {ip}...")
        
        def single_test_thread():
            try:
                result = {
                    'ip': ip,
                    'ports': [],
                    'http_status': None,
                    'baumer_detected': False
                }
                
                # Port-Test
                ports = [80, 8080, 554, 443, 23, 21]
                for port in ports:
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(1)
                        if sock.connect_ex((ip, port)) == 0:
                            result['ports'].append(port)
                        sock.close()
                    except:
                        pass
                
                # HTTP-Test
                if 80 in result['ports'] or 8080 in result['ports']:
                    for port in [80, 8080]:
                        if port in result['ports']:
                            try:
                                response = requests.get(f"http://{ip}:{port}", timeout=3)
                                result['http_status'] = response.status_code
                                
                                if 'baumer' in response.text.lower() or 'vcxg' in response.text.lower():
                                    result['baumer_detected'] = True
                                break
                            except:
                                pass
                
                QTimer.singleShot(0, lambda: self.single_test_completed(result))
                
            except Exception as e:
                QTimer.singleShot(0, lambda: self.test_error(str(e)))
        
        thread = threading.Thread(target=single_test_thread, daemon=True)
        thread.start()
        
    def single_test_completed(self, result):
        """Einzeltest abgeschlossen"""
        ip = result['ip']
        
        if result['ports']:
            status = f"✅ Erreichbar (Ports: {result['ports']})"
            if result['http_status']:
                status += f", HTTP: {result['http_status']}"
            if result['baumer_detected']:
                status += ", 🎯 Baumer erkannt"
                
            item_text = f"{ip} - VCXG-13M ({status})"
            self.log_message(f"✅ {ip} - {status}")
        else:
            item_text = f"{ip} - VCXG-13M (❌ Nicht erreichbar)"
            self.log_message(f"❌ {ip} - Nicht erreichbar")
        
        item = QListWidgetItem(item_text)
        item.setData(1, ip)
        self.camera_list.addItem(item)
        
        self.status_label.setText(f"✅ Test von {ip} abgeschlossen")
        
    def quick_scan(self):
        """Schneller Scan"""
        self.log_message("⚡ Starte schnellen Scan...")
        self.status_label.setText("⚡ Schneller Scan läuft...")
        QTimer.singleShot(3000, lambda: self.log_message("⚡ Schneller Scan abgeschlossen"))
        
    def targeted_scan(self):
        """Gezielter Scan"""
        self.log_message("🎯 Starte gezielten Scan...")
        self.status_label.setText("🎯 Gezielter Scan läuft...")
        QTimer.singleShot(5000, lambda: self.log_message("🎯 Gezielter Scan abgeschlossen"))
        
    def full_scan(self):
        """Vollständiger Scan"""
        self.log_message("🌐 Starte vollständigen Scan...")
        self.status_label.setText("🌐 Vollständiger Scan läuft...")
        QTimer.singleShot(10000, lambda: self.log_message("🌐 Vollständiger Scan abgeschlossen"))
        
    def connect_camera(self):
        """Verbindet zur ausgewählten Kamera"""
        current_item = self.camera_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"🔗 Verbinde zu Baumer-Kamera: {ip}")
            self.status_label.setText(f"🔗 Verbunden mit {ip}")
            
            # Füge zur Temperatur-Überwachung hinzu
            row = len(self.connected_cameras)
            
            ip_label = QLabel(f"📡 Kamera {ip}:")
            ip_label.setStyleSheet("font-weight: bold; color: #333;")
            self.connected_layout.addWidget(ip_label, row, 0)
            
            temp_label = QLabel("-- °C")
            temp_label.setStyleSheet("font-size: 18px; color: blue; font-weight: bold;")
            self.connected_layout.addWidget(temp_label, row, 1)
            
            status_label = QLabel("🟢 Verbunden")
            status_label.setStyleSheet("color: green; font-weight: bold;")
            self.connected_layout.addWidget(status_label, row, 2)
            
            self.connected_cameras[ip] = {
                'temp': temp_label,
                'status': status_label,
                'ip': ip_label
            }
            
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Ergebnisliste aus.")
            
    def test_temperature(self):
        """Testet Temperatur-Abfrage"""
        current_item = self.camera_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"🌡️ Teste Temperatur von {ip}")
            
            # Simuliere Temperatur-Abfrage
            import random
            temp = round(45 + random.uniform(-5, 15), 1)
            self.log_message(f"🌡️ Temperatur von {ip}: {temp}°C")
            
            # Update Anzeige falls verbunden
            if ip in self.connected_cameras:
                self.connected_cameras[ip]['temp'].setText(f"{temp}°C")
                
                # Farbkodierung
                if temp >= 70:
                    self.connected_cameras[ip]['temp'].setStyleSheet("font-size: 18px; color: red; font-weight: bold;")
                elif temp >= 60:
                    self.connected_cameras[ip]['temp'].setStyleSheet("font-size: 18px; color: orange; font-weight: bold;")
                else:
                    self.connected_cameras[ip]['temp'].setStyleSheet("font-size: 18px; color: green; font-weight: bold;")
                
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Ergebnisliste aus.")
            
    def test_error(self, error):
        """Test-Fehler"""
        self.log_message(f"❌ Test-Fehler: {error}")
        self.status_label.setText(f"❌ Fehler: {error}")
        
        self.test_all_button.setEnabled(True)
        self.test_all_button.setText("🚀 Alle bekannten Kameras testen")
        
    def save_log(self):
        """Speichert Log"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"baumer_log_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.toPlainText())
                
            self.log_message(f"💾 Log gespeichert: {filename}")
            QMessageBox.information(self, "Gespeichert", f"Log wurde gespeichert als:\n{filename}")
            
        except Exception as e:
            self.log_message(f"❌ Fehler beim Speichern: {e}")
            
    def log_message(self, message):
        """Fügt Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logging.info(message)

def main():
    """Hauptprogramm"""
    try:
        setup_logging()
        logging.info(f"Starte Standalone Baumer Monitor v{VERSION}")
        
        app = QApplication(sys.argv)
        app.setApplicationName("Standalone Baumer Monitor")
        app.setApplicationVersion(VERSION)
        
        window = StandaloneBaumerWindow()
        window.show()
        
        logging.info("Standalone GUI gestartet")
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Kritischer Fehler: {e}")
        print(f"FEHLER: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
