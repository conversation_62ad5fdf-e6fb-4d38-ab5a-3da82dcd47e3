VERSION = "1.0.5"

import sys
import os
import logging

# Füge src-Verzeichnis zum Python-Pfad hinzu
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox)
from PyQt5.QtCore import QTimer
from core.config import setup_logging

class Ethernet5MainWindow(QMainWindow):
    """Hauptfenster speziell für Ethernet 5 - Baumer-Kameras im 169.254.x.x Netzwerk"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Kamera Monitor - Ethernet 5 (169.254.x.x)")
        self.setGeometry(100, 100, 1000, 800)
        
        # Bekannte Kamera-IPs
        self.known_cameras = [
            "***************",
            "**************", 
            "**************",
            "**************"
        ]
        
        # Initialisiere Komponenten
        self.discovered_cameras = []
        self.connected_cameras = {}
        
        # GUI aufbauen
        self.init_ui()
        
        logging.info("Ethernet 5 GUI initialisiert für bekannte Baumer-Kameras")
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hauptlayout
        main_layout = QVBoxLayout(central_widget)
        
        # Titel
        title_label = QLabel("Baumer Kamera Temperatur Monitor v1.0.5")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: blue;")
        main_layout.addWidget(title_label)
        
        # Netzwerk-Info
        network_info = QLabel("🔗 Ethernet 5 - Baumer-Kameras im 169.254.x.x Netzwerk (APIPA)")
        network_info.setStyleSheet("font-size: 14px; color: green; margin: 5px; background-color: #f0f8ff; padding: 5px;")
        main_layout.addWidget(network_info)
        
        # Bekannte Kameras Info
        known_info = QLabel(f"📋 Bekannte Kameras: {len(self.known_cameras)} Stück")
        known_info.setStyleSheet("font-size: 12px; color: blue; margin: 5px;")
        main_layout.addWidget(known_info)
        
        # Tab-Widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Kameras-Tab
        self.init_camera_tab()
        
        # Temperatur-Tab
        self.init_temperature_tab()
        
        # Log-Tab
        self.init_log_tab()
        
        # Status-Label
        self.status_label = QLabel("Bereit für Baumer-Kamera-Tests über Ethernet 5")
        main_layout.addWidget(self.status_label)
        
    def init_camera_tab(self):
        """Initialisiert Kamera-Tab"""
        camera_widget = QWidget()
        layout = QVBoxLayout(camera_widget)
        
        # Bekannte Kameras Gruppe
        known_group = QGroupBox("Bekannte Baumer-Kameras (Ethernet 5)")
        known_layout = QVBoxLayout(known_group)
        
        # Info-Text
        info_label = QLabel("Diese Kameras wurden bereits erkannt. Klicken Sie 'Alle testen' um sie zu überprüfen:")
        known_layout.addWidget(info_label)
        
        # Bekannte Kameras Liste
        self.known_cameras_list = QListWidget()
        for ip in self.known_cameras:
            item = QListWidgetItem(f"VCXG-13M - {ip} (Bekannte Kamera)")
            item.setData(1, ip)
            self.known_cameras_list.addItem(item)
        known_layout.addWidget(self.known_cameras_list)
        
        # Bekannte Kameras Buttons
        known_button_layout = QHBoxLayout()
        
        self.test_all_button = QPushButton("Alle bekannten Kameras testen")
        self.test_all_button.clicked.connect(self.test_all_known_cameras)
        self.test_all_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        known_button_layout.addWidget(self.test_all_button)
        
        self.test_selected_button = QPushButton("Ausgewählte testen")
        self.test_selected_button.clicked.connect(self.test_selected_camera)
        known_button_layout.addWidget(self.test_selected_button)
        
        known_layout.addLayout(known_button_layout)
        layout.addWidget(known_group)
        
        # Scan-Gruppe
        scan_group = QGroupBox("Netzwerk-Scan Optionen")
        scan_layout = QVBoxLayout(scan_group)
        
        # Netzwerk-Auswahl
        network_select_layout = QHBoxLayout()
        network_select_layout.addWidget(QLabel("Scan-Bereich:"))
        
        self.network_combo = QComboBox()
        self.network_combo.addItems([
            "169.254.x.x (Ethernet 5 - APIPA) ⭐",
            "169.254.174.x (Spezifisch für .161)",
            "169.254.48.x (Spezifisch für .177)",
            "169.254.144.x (Spezifisch für .84)",
            "169.254.154.x (Spezifisch für .96)",
            "Vollständiger 169.254.x.x Scan"
        ])
        self.network_combo.currentTextChanged.connect(self.on_network_changed)
        network_select_layout.addWidget(self.network_combo)
        scan_layout.addLayout(network_select_layout)
        
        # Scan-Buttons
        scan_button_layout = QHBoxLayout()
        
        self.quick_scan_button = QPushButton("Schneller Scan (1-50)")
        self.quick_scan_button.clicked.connect(self.quick_scan)
        scan_button_layout.addWidget(self.quick_scan_button)
        
        self.targeted_scan_button = QPushButton("Gezielter Scan (bekannte Bereiche)")
        self.targeted_scan_button.clicked.connect(self.targeted_scan)
        self.targeted_scan_button.setStyleSheet("background-color: #2196F3; color: white; font-weight: bold;")
        scan_button_layout.addWidget(self.targeted_scan_button)
        
        self.full_scan_button = QPushButton("Vollständiger Scan (1-254)")
        self.full_scan_button.clicked.connect(self.full_scan)
        scan_button_layout.addWidget(self.full_scan_button)
        
        scan_layout.addLayout(scan_button_layout)
        layout.addWidget(scan_group)
        
        # Gefundene Kameras
        found_group = QGroupBox("Scan-Ergebnisse")
        found_layout = QVBoxLayout(found_group)
        
        self.camera_list = QListWidget()
        found_layout.addWidget(self.camera_list)
        
        # Kamera-Aktionen
        camera_action_layout = QHBoxLayout()
        
        self.connect_button = QPushButton("Verbinden")
        self.connect_button.clicked.connect(self.connect_camera)
        camera_action_layout.addWidget(self.connect_button)
        
        self.temp_test_button = QPushButton("Temperatur testen")
        self.temp_test_button.clicked.connect(self.test_temperature)
        camera_action_layout.addWidget(self.temp_test_button)
        
        self.clear_button = QPushButton("Ergebnisse leeren")
        self.clear_button.clicked.connect(self.camera_list.clear)
        camera_action_layout.addWidget(self.clear_button)
        
        found_layout.addLayout(camera_action_layout)
        layout.addWidget(found_group)
        
        self.tab_widget.addTab(camera_widget, "Baumer Kameras")
        
    def init_temperature_tab(self):
        """Initialisiert Temperatur-Tab"""
        temp_widget = QWidget()
        layout = QVBoxLayout(temp_widget)
        
        temp_label = QLabel("Temperatur-Überwachung")
        temp_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(temp_label)
        
        # Verbundene Kameras
        self.connected_cameras_widget = QWidget()
        self.connected_layout = QGridLayout(self.connected_cameras_widget)
        layout.addWidget(self.connected_cameras_widget)
        
        # Temperatur-Verlauf Platzhalter
        plot_placeholder = QLabel("Hier wird der Temperaturverlauf angezeigt\n(Wird aktiviert wenn Kameras verbunden sind)")
        plot_placeholder.setStyleSheet("border: 1px solid gray; padding: 20px; text-align: center; min-height: 200px;")
        layout.addWidget(plot_placeholder)
        
        self.tab_widget.addTab(temp_widget, "Temperatur-Überwachung")
        
    def init_log_tab(self):
        """Initialisiert Log-Tab"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        log_label = QLabel("System-Log")
        log_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        layout.addWidget(self.log_text)
        
        # Log-Buttons
        log_button_layout = QHBoxLayout()
        
        clear_log_button = QPushButton("Log leeren")
        clear_log_button.clicked.connect(self.log_text.clear)
        log_button_layout.addWidget(clear_log_button)
        
        save_log_button = QPushButton("Log speichern")
        save_log_button.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_log_button)
        
        layout.addLayout(log_button_layout)
        
        self.tab_widget.addTab(log_widget, "System-Log")
        
    def on_network_changed(self, text):
        """Netzwerk-Bereich geändert"""
        self.log_message(f"Scan-Bereich gewählt: {text}")
        self.status_label.setText(f"Scan-Bereich: {text}")
        
    def test_all_known_cameras(self):
        """Testet alle bekannten Kameras"""
        self.log_message("=== TESTE ALLE BEKANNTEN KAMERAS ===")
        self.status_label.setText("Teste alle bekannten Baumer-Kameras...")
        
        self.test_all_button.setEnabled(False)
        self.test_all_button.setText("Teste...")
        
        import threading
        import time
        import socket
        
        def test_thread():
            try:
                results = []
                
                for ip in self.known_cameras:
                    self.log_message(f"Teste Kamera: {ip}")
                    
                    # Teste verschiedene Ports
                    ports_to_test = [80, 8080, 554, 443]
                    open_ports = []
                    
                    for port in ports_to_test:
                        try:
                            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                            sock.settimeout(2)
                            result = sock.connect_ex((ip, port))
                            sock.close()
                            
                            if result == 0:
                                open_ports.append(port)
                                
                        except:
                            pass
                    
                    if open_ports:
                        results.append({
                            'ip': ip,
                            'status': 'Erreichbar',
                            'ports': open_ports,
                            'model': 'VCXG-13M'
                        })
                        self.log_message(f"✓ {ip} - Erreichbar (Ports: {open_ports})")
                    else:
                        results.append({
                            'ip': ip,
                            'status': 'Nicht erreichbar',
                            'ports': [],
                            'model': 'VCXG-13M'
                        })
                        self.log_message(f"✗ {ip} - Nicht erreichbar")
                    
                    time.sleep(0.5)  # Kurze Pause zwischen Tests
                
                # Update GUI
                QTimer.singleShot(0, lambda: self.test_all_completed(results))
                
            except Exception as e:
                QTimer.singleShot(0, lambda: self.test_error(str(e)))
        
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
        
    def test_all_completed(self, results):
        """Alle Tests abgeschlossen"""
        self.test_all_button.setEnabled(True)
        self.test_all_button.setText("Alle bekannten Kameras testen")
        
        # Füge Ergebnisse zur Liste hinzu
        reachable_count = 0
        for result in results:
            if result['status'] == 'Erreichbar':
                item_text = f"{result['ip']} - {result['model']} (Erreichbar - Ports: {result['ports']})"
                reachable_count += 1
            else:
                item_text = f"{result['ip']} - {result['model']} (Nicht erreichbar)"
                
            item = QListWidgetItem(item_text)
            item.setData(1, result['ip'])
            self.camera_list.addItem(item)
        
        self.status_label.setText(f"Test abgeschlossen: {reachable_count}/{len(results)} Kameras erreichbar")
        self.log_message(f"=== TEST ABGESCHLOSSEN: {reachable_count}/{len(results)} Kameras erreichbar ===")
        
    def test_selected_camera(self):
        """Testet ausgewählte Kamera"""
        current_item = self.known_cameras_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"Teste ausgewählte Kamera: {ip}")
            self.test_single_ip(ip)
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Liste aus.")
            
    def test_single_ip(self, ip):
        """Testet einzelne IP"""
        self.log_message(f"Detailtest für {ip}...")
        self.status_label.setText(f"Teste {ip}...")
        
        import threading
        import socket
        import requests
        
        def single_test_thread():
            try:
                result = {
                    'ip': ip,
                    'ports': [],
                    'http_status': None,
                    'baumer_detected': False
                }
                
                # Port-Test
                ports = [80, 8080, 554, 443, 23, 21]
                for port in ports:
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(1)
                        if sock.connect_ex((ip, port)) == 0:
                            result['ports'].append(port)
                        sock.close()
                    except:
                        pass
                
                # HTTP-Test
                if 80 in result['ports'] or 8080 in result['ports']:
                    for port in [80, 8080]:
                        if port in result['ports']:
                            try:
                                response = requests.get(f"http://{ip}:{port}", timeout=3)
                                result['http_status'] = response.status_code
                                
                                if 'baumer' in response.text.lower() or 'vcxg' in response.text.lower():
                                    result['baumer_detected'] = True
                                break
                            except:
                                pass
                
                QTimer.singleShot(0, lambda: self.single_test_completed(result))
                
            except Exception as e:
                QTimer.singleShot(0, lambda: self.test_error(str(e)))
        
        thread = threading.Thread(target=single_test_thread, daemon=True)
        thread.start()
        
    def single_test_completed(self, result):
        """Einzeltest abgeschlossen"""
        ip = result['ip']
        
        if result['ports']:
            status = f"Erreichbar (Ports: {result['ports']})"
            if result['http_status']:
                status += f", HTTP: {result['http_status']}"
            if result['baumer_detected']:
                status += ", Baumer erkannt"
                
            item_text = f"{ip} - VCXG-13M ({status})"
            self.log_message(f"✓ {ip} - {status}")
        else:
            item_text = f"{ip} - VCXG-13M (Nicht erreichbar)"
            self.log_message(f"✗ {ip} - Nicht erreichbar")
        
        item = QListWidgetItem(item_text)
        item.setData(1, ip)
        self.camera_list.addItem(item)
        
        self.status_label.setText(f"Test von {ip} abgeschlossen")
        
    def quick_scan(self):
        """Schneller Scan"""
        self.start_network_scan("Schnell", 50)
        
    def targeted_scan(self):
        """Gezielter Scan der bekannten Bereiche"""
        self.start_targeted_scan()
        
    def full_scan(self):
        """Vollständiger Scan"""
        self.start_network_scan("Vollständig", 254)
        
    def start_targeted_scan(self):
        """Startet gezielten Scan"""
        self.log_message("=== GEZIELTER SCAN (bekannte Bereiche) ===")
        
        # Extrahiere Subnetz-Bereiche aus bekannten IPs
        subnets = set()
        for ip in self.known_cameras:
            parts = ip.split('.')
            subnet = f"{parts[0]}.{parts[1]}.{parts[2]}"
            subnets.add(subnet)
        
        self.log_message(f"Scanne Bereiche: {list(subnets)}")
        self.status_label.setText(f"Gezielter Scan läuft... ({len(subnets)} Bereiche)")
        
        # Implementierung folgt...
        QTimer.singleShot(3000, lambda: self.log_message("Gezielter Scan abgeschlossen"))
        
    def start_network_scan(self, scan_type, ip_count):
        """Startet Netzwerk-Scan"""
        self.log_message(f"=== {scan_type.upper()}-SCAN ===")
        # Implementierung folgt...
        
    def connect_camera(self):
        """Verbindet zur ausgewählten Kamera"""
        current_item = self.camera_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"Verbinde zu Baumer-Kamera: {ip}")
            self.status_label.setText(f"Verbunden mit {ip}")
            
            # Füge zur Temperatur-Überwachung hinzu
            row = len(self.connected_cameras)
            
            ip_label = QLabel(f"Kamera {ip}:")
            ip_label.setStyleSheet("font-weight: bold;")
            self.connected_layout.addWidget(ip_label, row, 0)
            
            temp_label = QLabel("-- °C")
            temp_label.setStyleSheet("font-size: 16px; color: blue;")
            self.connected_layout.addWidget(temp_label, row, 1)
            
            status_label = QLabel("Verbunden")
            status_label.setStyleSheet("color: green;")
            self.connected_layout.addWidget(status_label, row, 2)
            
            self.connected_cameras[ip] = {
                'temp': temp_label,
                'status': status_label,
                'ip': ip_label
            }
            
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus.")
            
    def test_temperature(self):
        """Testet Temperatur-Abfrage"""
        current_item = self.camera_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"Teste Temperatur von {ip}")
            
            # Simuliere Temperatur-Abfrage
            import random
            temp = round(45 + random.uniform(-5, 15), 1)
            self.log_message(f"Temperatur von {ip}: {temp}°C")
            
            # Update Anzeige falls verbunden
            if ip in self.connected_cameras:
                self.connected_cameras[ip]['temp'].setText(f"{temp}°C")
                
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus.")
            
    def test_error(self, error):
        """Test-Fehler"""
        self.log_message(f"Test-Fehler: {error}")
        self.status_label.setText(f"Fehler: {error}")
        
    def save_log(self):
        """Speichert Log"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"ethernet5_log_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.toPlainText())
                
            self.log_message(f"Log gespeichert: {filename}")
            QMessageBox.information(self, "Gespeichert", f"Log wurde gespeichert als:\n{filename}")
            
        except Exception as e:
            self.log_message(f"Fehler beim Speichern: {e}")
            
    def log_message(self, message):
        """Fügt Nachricht zum Log hinzu"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logging.info(message)

def main():
    """Hauptprogramm"""
    try:
        setup_logging()
        logging.info(f"Starte Ethernet 5 Baumer Monitor v{VERSION}")
        
        app = QApplication(sys.argv)
        app.setApplicationName("Ethernet 5 Baumer Monitor")
        app.setApplicationVersion(VERSION)
        
        window = Ethernet5MainWindow()
        window.show()
        
        logging.info("Ethernet 5 GUI gestartet für bekannte Baumer-Kameras")
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Kritischer Fehler: {e}")
        print(f"FEHLER: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
