<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.NeoEvent Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00807.html">NeoEvent</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00804.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.NeoEvent Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00086.html">Cam Interface Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides access to events This class provides an easy way to work with events.  
 <a href="a00807.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.NeoEvent:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00807.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a89454b88a614ca5d5b686dc4ba1dc33a"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00807.html#a89454b88a614ca5d5b686dc4ba1dc33a">__init__</a> (self, *args)</td></tr>
<tr class="separator:a89454b88a614ca5d5b686dc4ba1dc33a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a319a9dbb64925123137c58a8466196b4"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00807.html#a319a9dbb64925123137c58a8466196b4">IsEmpty</a> (self)</td></tr>
<tr class="memdesc:a319a9dbb64925123137c58a8466196b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Check if the event is empty or filled with data.  <a href="#a319a9dbb64925123137c58a8466196b4">More...</a><br /></td></tr>
<tr class="separator:a319a9dbb64925123137c58a8466196b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8226bcc574b36e9a55f015a0706589e6"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00807.html#a8226bcc574b36e9a55f015a0706589e6">GetName</a> (self)</td></tr>
<tr class="memdesc:a8226bcc574b36e9a55f015a0706589e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the name of the <a class="el" href="a00807.html" title="Provides access to events This class provides an easy way to work with events.">NeoEvent</a> object.  <a href="#a8226bcc574b36e9a55f015a0706589e6">More...</a><br /></td></tr>
<tr class="separator:a8226bcc574b36e9a55f015a0706589e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f0432aaf71c2d0cd3d78de6f5d206f7"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00807.html#a6f0432aaf71c2d0cd3d78de6f5d206f7">GetTimestamp</a> (self)</td></tr>
<tr class="memdesc:a6f0432aaf71c2d0cd3d78de6f5d206f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the timestamp of the <a class="el" href="a00807.html" title="Provides access to events This class provides an easy way to work with events.">NeoEvent</a> object.  <a href="#a6f0432aaf71c2d0cd3d78de6f5d206f7">More...</a><br /></td></tr>
<tr class="separator:a6f0432aaf71c2d0cd3d78de6f5d206f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2f11de0214e14c0f913e156cf87a38d2"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00807.html#a2f11de0214e14c0f913e156cf87a38d2">GetId</a> (self)</td></tr>
<tr class="memdesc:a2f11de0214e14c0f913e156cf87a38d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the id of the <a class="el" href="a00807.html" title="Provides access to events This class provides an easy way to work with events.">NeoEvent</a> object.  <a href="#a2f11de0214e14c0f913e156cf87a38d2">More...</a><br /></td></tr>
<tr class="separator:a2f11de0214e14c0f913e156cf87a38d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abee99c08a13d2851ad39e03385e2a6a0"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00807.html#abee99c08a13d2851ad39e03385e2a6a0">GetCameraId</a> (self)</td></tr>
<tr class="memdesc:abee99c08a13d2851ad39e03385e2a6a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the id of the camera.  <a href="#abee99c08a13d2851ad39e03385e2a6a0">More...</a><br /></td></tr>
<tr class="separator:abee99c08a13d2851ad39e03385e2a6a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a3ffddffba67360a348acc72c67975b38"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00807.html#a3ffddffba67360a348acc72c67975b38">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:a3ffddffba67360a348acc72c67975b38"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#a3ffddffba67360a348acc72c67975b38">More...</a><br /></td></tr>
<tr class="separator:a3ffddffba67360a348acc72c67975b38"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Provides access to events This class provides an easy way to work with events. </p>
<p>It provides access to camera events as well as plug and play events. The Class has a set of methods to get information about an event. More information about the events of your specific camera can be found in the documentation of your camera available at the member area of the <a href="https://vt.baumer.com">Baumer Website</a>. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a89454b88a614ca5d5b686dc4ba1dc33a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a89454b88a614ca5d5b686dc4ba1dc33a">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.NeoEvent.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">   \brief      Constructor
</pre> <dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>object</b> (optional) A <a class="el" href="a00807.html" title="Provides access to events This class provides an easy way to work with events.">NeoEvent</a> object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a319a9dbb64925123137c58a8466196b4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a319a9dbb64925123137c58a8466196b4">&#9670;&nbsp;</a></span>IsEmpty()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.NeoEvent.IsEmpty </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Check if the event is empty or filled with data. </p>
<dl class="section return"><dt>Returns</dt><dd>False if the event is filled with data, otherwise true </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00859.html#a457e6738d020688c89c4f76f6283b538" title="Get an event from the camera The GetEvent method is used to retrieve an event from the camera to work...">Cam.GetEvent()</a> </dd>
<dd>
<a class="el" href="a00859.html#a5533479e8e16bc1a5aea0a5c7a2dde5c" title="Get a plug and play event from the camera The GetPnPEvent method is used to retrieve an plug and play...">Cam.GetPnPEvent()</a> </dd></dl>

</div>
</div>
<a id="a8226bcc574b36e9a55f015a0706589e6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8226bcc574b36e9a55f015a0706589e6">&#9670;&nbsp;</a></span>GetName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.NeoEvent.GetName </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the name of the <a class="el" href="a00807.html" title="Provides access to events This class provides an easy way to work with events.">NeoEvent</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object, empty if the event does not contain any data. </dd></dl>

</div>
</div>
<a id="a6f0432aaf71c2d0cd3d78de6f5d206f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6f0432aaf71c2d0cd3d78de6f5d206f7">&#9670;&nbsp;</a></span>GetTimestamp()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.NeoEvent.GetTimestamp </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the timestamp of the <a class="el" href="a00807.html" title="Provides access to events This class provides an easy way to work with events.">NeoEvent</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The timestamp of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object, zero if the event does not contain any data. </dd></dl>

</div>
</div>
<a id="a2f11de0214e14c0f913e156cf87a38d2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2f11de0214e14c0f913e156cf87a38d2">&#9670;&nbsp;</a></span>GetId()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.NeoEvent.GetId </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the id of the <a class="el" href="a00807.html" title="Provides access to events This class provides an easy way to work with events.">NeoEvent</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The id of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object, zero if the event does not contain any data. </dd></dl>

</div>
</div>
<a id="abee99c08a13d2851ad39e03385e2a6a0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abee99c08a13d2851ad39e03385e2a6a0">&#9670;&nbsp;</a></span>GetCameraId()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.NeoEvent.GetCameraId </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the id of the camera. </p>
<dl class="section return"><dt>Returns</dt><dd>The id of the camera, empty if the event does not contain any data. </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a3ffddffba67360a348acc72c67975b38"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ffddffba67360a348acc72c67975b38">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.NeoEvent.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
