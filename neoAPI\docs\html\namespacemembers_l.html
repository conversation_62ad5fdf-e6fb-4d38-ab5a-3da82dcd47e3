<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented namespace members with links to the namespaces they belong to:</div>

<h3><a id="index_l"></a>- l -</h3><ul>
<li>LineFormat_LVDS
: <a class="el" href="a00091.html#aba2c2439b3b0e20eee4d5a1dd26b4f79">neoapi</a>
</li>
<li>LineFormat_NoConnect
: <a class="el" href="a00091.html#a089b77120248fbf605e7a9dc8c9dfba7">neoapi</a>
</li>
<li>LineFormat_OpenDrain
: <a class="el" href="a00091.html#aad1b70fba60cd035d8df9b9d8d3fb980">neoapi</a>
</li>
<li>LineFormat_OpenSource
: <a class="el" href="a00091.html#afa5902dd5e57d5815927462eee377411">neoapi</a>
</li>
<li>LineFormat_OptoCoupled
: <a class="el" href="a00091.html#a322c3b469cafebe73970d5db801c7ed5">neoapi</a>
</li>
<li>LineFormat_PushPull
: <a class="el" href="a00091.html#aa5819681c83e6b56fc70aca50e6bec24">neoapi</a>
</li>
<li>LineFormat_RS422
: <a class="el" href="a00091.html#ace288ab3cfa3c2355f0e7cf3cfb5205b">neoapi</a>
</li>
<li>LineFormat_TriState
: <a class="el" href="a00091.html#af7c41bce487421ff2f5d42615d86c6fb">neoapi</a>
</li>
<li>LineFormat_TTL
: <a class="el" href="a00091.html#aab147bedf1944870ac935c2f2da4fcdb">neoapi</a>
</li>
<li>LineMode_Input
: <a class="el" href="a00091.html#a2ffc6daf1140215eacba127d4c6e8e5e">neoapi</a>
</li>
<li>LineMode_Output
: <a class="el" href="a00091.html#a063852c884391d0bce5e1f9105a1eb82">neoapi</a>
</li>
<li>LinePWMConfigurationMode_Off
: <a class="el" href="a00091.html#aea16af4d2bce8d38c3aea4e15c4a28cc">neoapi</a>
</li>
<li>LinePWMConfigurationMode_On
: <a class="el" href="a00091.html#ac20e669741089fb3f2625037f098903d">neoapi</a>
</li>
<li>LinePWMMode_FixedFrequency
: <a class="el" href="a00091.html#a11ee0fd1bbaa4cb30563f82a99526fe0">neoapi</a>
</li>
<li>LinePWMMode_Off
: <a class="el" href="a00091.html#af06c1b8e2fbd453a35315b928cd44b6d">neoapi</a>
</li>
<li>LinePWMMode_OnePulse
: <a class="el" href="a00091.html#a4eda2a56f44011351221a9bff6bd33b4">neoapi</a>
</li>
<li>LineSelector_CC1
: <a class="el" href="a00091.html#ac2e96d22657c0b6eae28537b132ec907">neoapi</a>
</li>
<li>LineSelector_CC2
: <a class="el" href="a00091.html#a3e9a1d7139238faa1c0ce36a2571056a">neoapi</a>
</li>
<li>LineSelector_CC3
: <a class="el" href="a00091.html#a5aaa31d43f14a63698e2e062a5b96860">neoapi</a>
</li>
<li>LineSelector_CC4
: <a class="el" href="a00091.html#a65ddbb4b0ad22cd51952b80e5c9621dc">neoapi</a>
</li>
<li>LineSelector_Line0
: <a class="el" href="a00091.html#a41be78ca0516a622ea5e4debe5124bec">neoapi</a>
</li>
<li>LineSelector_Line1
: <a class="el" href="a00091.html#a7405732de0d69ab8697de38ca7b696aa">neoapi</a>
</li>
<li>LineSelector_Line2
: <a class="el" href="a00091.html#a6cea06ff797117c67e51facf1718964f">neoapi</a>
</li>
<li>LineSelector_Line3
: <a class="el" href="a00091.html#a5fbd156f9ee98d245c06afd77d9f18a7">neoapi</a>
</li>
<li>LineSelector_Line4
: <a class="el" href="a00091.html#a3bee984094d8daa28e79c0441ce6cb0d">neoapi</a>
</li>
<li>LineSelector_Line5
: <a class="el" href="a00091.html#ac73f93f5d031c8d0f2769be13345bfa5">neoapi</a>
</li>
<li>LineSelector_Line6
: <a class="el" href="a00091.html#aef74b3991c913d208b4a9feed6756a5a">neoapi</a>
</li>
<li>LineSelector_Line7
: <a class="el" href="a00091.html#ad4925ba6ad8615b96d969af9760d1d91">neoapi</a>
</li>
<li>LineSelector_LinkTrigger0
: <a class="el" href="a00091.html#ab4cdaaadabe266cf3f48d700f4416600">neoapi</a>
</li>
<li>LineSelector_LinkTrigger1
: <a class="el" href="a00091.html#a7e2b92d2bfa3bd60e4c49cf42e456526">neoapi</a>
</li>
<li>LineSelector_LinkTrigger2
: <a class="el" href="a00091.html#a9b2c7041b807d687b3c370522424ced2">neoapi</a>
</li>
<li>LineSource_AcquisitionActive
: <a class="el" href="a00091.html#a9cb08c627c28d2793d7e0025c502b37f">neoapi</a>
</li>
<li>LineSource_AcquisitionTrigger
: <a class="el" href="a00091.html#a1478618e0a1b81ded011704960526251">neoapi</a>
</li>
<li>LineSource_AcquisitionTriggerMissed
: <a class="el" href="a00091.html#aaa233c5d90354c82a902256ba8e23712">neoapi</a>
</li>
<li>LineSource_AcquisitionTriggerWait
: <a class="el" href="a00091.html#a615840bc2560865fc830c89cf64c008f">neoapi</a>
</li>
<li>LineSource_Counter0Active
: <a class="el" href="a00091.html#a3c4c0e1800b433c29f67fdddbb8eacb5">neoapi</a>
</li>
<li>LineSource_Counter1Active
: <a class="el" href="a00091.html#a3809e22ab5044f876dc2ce000f0035e3">neoapi</a>
</li>
<li>LineSource_Counter2Active
: <a class="el" href="a00091.html#a31708bed5a67f01c131a28ce71795f4c">neoapi</a>
</li>
<li>LineSource_Encoder0
: <a class="el" href="a00091.html#a87fcfcbe3930085b9d5d6ac02e059513">neoapi</a>
</li>
<li>LineSource_Encoder1
: <a class="el" href="a00091.html#a48ad51a253b0d17832f7aae0bde0ff28">neoapi</a>
</li>
<li>LineSource_Encoder2
: <a class="el" href="a00091.html#a25b20c023a8d2816940760e8fc565d06">neoapi</a>
</li>
<li>LineSource_ExposureActive
: <a class="el" href="a00091.html#ae023ac3afc4c47ae6f437b6c21bf76f6">neoapi</a>
</li>
<li>LineSource_FrameActive
: <a class="el" href="a00091.html#a8a9aa6e4da0cbbcbb38321bad30cadbb">neoapi</a>
</li>
<li>LineSource_FrameTrigger
: <a class="el" href="a00091.html#ab990415840e6d92934d019cd2d7237aa">neoapi</a>
</li>
<li>LineSource_FrameTriggerMissed
: <a class="el" href="a00091.html#ad8f94295a7067034d493addfca5a300b">neoapi</a>
</li>
<li>LineSource_FrameTriggerWait
: <a class="el" href="a00091.html#a51962d997c3f94fd754835f54fdb6721">neoapi</a>
</li>
<li>LineSource_Line0
: <a class="el" href="a00091.html#a118eebd019e105cc4b93ae81519b2988">neoapi</a>
</li>
<li>LineSource_Line1
: <a class="el" href="a00091.html#a1fdf7158788869a2258ac8d64aad5131">neoapi</a>
</li>
<li>LineSource_LineActive
: <a class="el" href="a00091.html#afb158025102941671511939090d64853">neoapi</a>
</li>
<li>LineSource_LineTrigger
: <a class="el" href="a00091.html#a347cf1c4fdf7ef71584856553b63d157">neoapi</a>
</li>
<li>LineSource_LineTriggerMissed
: <a class="el" href="a00091.html#a4012a739ea88226e944d38789a4bc694">neoapi</a>
</li>
<li>LineSource_LineTriggerWait
: <a class="el" href="a00091.html#a89bcc1c4d83a43256be22de1b235d380">neoapi</a>
</li>
<li>LineSource_LogicBlock0
: <a class="el" href="a00091.html#ad899807021c4c057a47f659dcf5c33c4">neoapi</a>
</li>
<li>LineSource_LogicBlock1
: <a class="el" href="a00091.html#a27766fdc0300f2130ddae0a495b7c974">neoapi</a>
</li>
<li>LineSource_LogicBlock2
: <a class="el" href="a00091.html#a3320d1e5f26180444136a5625c8f4905">neoapi</a>
</li>
<li>LineSource_Off
: <a class="el" href="a00091.html#ad0c0c00fb8267a0bf85d32bbf60fdfdd">neoapi</a>
</li>
<li>LineSource_ReadoutActive
: <a class="el" href="a00091.html#a236d8f0b0cf1234aecc7fa42c9fe695c">neoapi</a>
</li>
<li>LineSource_SequencerOutput0
: <a class="el" href="a00091.html#a8cb8e1f509d0e79ad6111f86eb99baaa">neoapi</a>
</li>
<li>LineSource_SequencerOutput1
: <a class="el" href="a00091.html#a9cbd5e7b3f0304784aaae10ae87d19d8">neoapi</a>
</li>
<li>LineSource_SequencerOutput2
: <a class="el" href="a00091.html#a42e26545d53c9acbf679a496b1f392e6">neoapi</a>
</li>
<li>LineSource_SoftwareSignal0
: <a class="el" href="a00091.html#a407030543a7ab344757586121b2c5dc8">neoapi</a>
</li>
<li>LineSource_SoftwareSignal1
: <a class="el" href="a00091.html#a1e70acdf4c7071666b508083d4cb0c8a">neoapi</a>
</li>
<li>LineSource_SoftwareSignal2
: <a class="el" href="a00091.html#afc4dfb391b84a4fbfa92cf29911deb4c">neoapi</a>
</li>
<li>LineSource_Stream0TransferActive
: <a class="el" href="a00091.html#a838fa29a1d78ecf8cfc7f4c7edea9ea5">neoapi</a>
</li>
<li>LineSource_Stream0TransferOverflow
: <a class="el" href="a00091.html#ac9570dcf05c6641698ce165c25c4f129">neoapi</a>
</li>
<li>LineSource_Stream0TransferPaused
: <a class="el" href="a00091.html#ada51240b9159d20725977593b632ef63">neoapi</a>
</li>
<li>LineSource_Stream0TransferStopped
: <a class="el" href="a00091.html#adacbb4e357becd9fd4b0011c04facd5e">neoapi</a>
</li>
<li>LineSource_Stream0TransferStopping
: <a class="el" href="a00091.html#a4a68159ac665d638bda45213775af419">neoapi</a>
</li>
<li>LineSource_Stream1TransferActive
: <a class="el" href="a00091.html#a7d0915c12694b56bdd3548f5b8b2e859">neoapi</a>
</li>
<li>LineSource_Stream1TransferOverflow
: <a class="el" href="a00091.html#ac34717aa497c661c7a5ad3829521f78b">neoapi</a>
</li>
<li>LineSource_Stream1TransferPaused
: <a class="el" href="a00091.html#a564dd4af0c9879c062af03e76139666d">neoapi</a>
</li>
<li>LineSource_Stream1TransferStopped
: <a class="el" href="a00091.html#a184759448382d7d73974b8fe74bd974d">neoapi</a>
</li>
<li>LineSource_Stream1TransferStopping
: <a class="el" href="a00091.html#acb1cca6c300aabcc1efc0df26368ba7d">neoapi</a>
</li>
<li>LineSource_Timer0Active
: <a class="el" href="a00091.html#a9709c482c807ee9e6752e04010f8bec2">neoapi</a>
</li>
<li>LineSource_Timer1Active
: <a class="el" href="a00091.html#a666861ddf3b0b0c7908b81bd59f3766d">neoapi</a>
</li>
<li>LineSource_Timer2Active
: <a class="el" href="a00091.html#aded911218dae461a5130300e26f59561">neoapi</a>
</li>
<li>LineSource_Timer3Active
: <a class="el" href="a00091.html#abc2ae59dbcffeedcff19ec1b745f9175">neoapi</a>
</li>
<li>LineSource_TriggerOverlapped
: <a class="el" href="a00091.html#aae85bf078527690baad4a9a9bfc46b55">neoapi</a>
</li>
<li>LineSource_TriggerReady
: <a class="el" href="a00091.html#a4546dd3b4dff50e24ede82b2ec2a2a0e">neoapi</a>
</li>
<li>LineSource_TriggerSkipped
: <a class="el" href="a00091.html#ad17e0395cc132aca03f3fdd0a4110cd1">neoapi</a>
</li>
<li>LineSource_UserOutput0
: <a class="el" href="a00091.html#aee9d71dc7838623932b238561364127f">neoapi</a>
</li>
<li>LineSource_UserOutput1
: <a class="el" href="a00091.html#abb932fa1054ef8aca61a2f31095476e3">neoapi</a>
</li>
<li>LineSource_UserOutput2
: <a class="el" href="a00091.html#a03105c7f7ae6a56e9dfa8399f0248468">neoapi</a>
</li>
<li>LineSource_UserOutput3
: <a class="el" href="a00091.html#a5dd7e63cbdb692360af1e975f5a90001">neoapi</a>
</li>
<li>LineSource_UserOutput4
: <a class="el" href="a00091.html#a94c200d91f4abae766fc77bc418a71ac">neoapi</a>
</li>
<li>LUTContent_Gamma
: <a class="el" href="a00091.html#a6d93907d2afe79bc7b30e547495ef064">neoapi</a>
</li>
<li>LUTContent_UserdefinedLUT
: <a class="el" href="a00091.html#affe6fe4add4584950f20179130948ee7">neoapi</a>
</li>
<li>LUTSelector_Blue
: <a class="el" href="a00091.html#a2ceed058d2c13bfef18fb1d9761c7162">neoapi</a>
</li>
<li>LUTSelector_Green
: <a class="el" href="a00091.html#aaf1cdea7fe07c41f32fc9e3e947009fd">neoapi</a>
</li>
<li>LUTSelector_Luminance
: <a class="el" href="a00091.html#a0ec388b52aa3054a1903c0eb5846c9f3">neoapi</a>
</li>
<li>LUTSelector_Red
: <a class="el" href="a00091.html#a3fac4cf3d02db683246972cd12bb2644">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
