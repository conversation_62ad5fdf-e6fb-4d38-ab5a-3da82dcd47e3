<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members - Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_p"></a>- p -</h3><ul>
<li>PartialScanEnabled()
: <a class="el" href="a00763.html#aff38d7f74bf5ae4db30a12e9cffdccc5">neoapi.FeatureAccess</a>
</li>
<li>PayloadSize()
: <a class="el" href="a00763.html#acb6860b412cde670b2fa83c5441557ea">neoapi.FeatureAccess</a>
</li>
<li>PhysicalPixelSizeX()
: <a class="el" href="a00763.html#a27e5a171cb12503f24a446de9cfd3e89">neoapi.FeatureAccess</a>
</li>
<li>PhysicalPixelSizeY()
: <a class="el" href="a00763.html#ad84d6cc4ca32f1a7304ca0b19e05cd6a">neoapi.FeatureAccess</a>
</li>
<li>PIN()
: <a class="el" href="a00763.html#a57be283f22e2eb33a106596c945bb996">neoapi.FeatureAccess</a>
</li>
<li>PixelCorrectionEnable()
: <a class="el" href="a00763.html#ae2fd6d8b238401d32f77f7ce167d537c">neoapi.FeatureAccess</a>
</li>
<li>PixelCorrectionThreshold()
: <a class="el" href="a00763.html#a4ac443a258f524927fe170f5ce77a513">neoapi.FeatureAccess</a>
</li>
<li>PixelFormat()
: <a class="el" href="a00763.html#a38d685a1b86fc5e71e8df31ccd39596a">neoapi.FeatureAccess</a>
</li>
<li>PtpClockAccuracy()
: <a class="el" href="a00763.html#ab6f309df0a00fe78dbb8d343c2debfa1">neoapi.FeatureAccess</a>
</li>
<li>PtpClockID()
: <a class="el" href="a00763.html#a6961cc992a9cb02049c84e18785f8254">neoapi.FeatureAccess</a>
</li>
<li>PtpClockOffset()
: <a class="el" href="a00763.html#a7956bb8faf4c0329d0babd21d1107f66">neoapi.FeatureAccess</a>
</li>
<li>PtpClockOffsetMode()
: <a class="el" href="a00763.html#a07f59ef870dbfe4cd445d0dd12b5686a">neoapi.FeatureAccess</a>
</li>
<li>PtpClockOffsetSet()
: <a class="el" href="a00763.html#a12d588ee2f5ece213eff559b8d0b6771">neoapi.FeatureAccess</a>
</li>
<li>PtpDataSetLatch()
: <a class="el" href="a00763.html#aa63b895a60fc9364adff86acfb58901a">neoapi.FeatureAccess</a>
</li>
<li>PtpDriftOffset()
: <a class="el" href="a00763.html#a10fbe8296828afd82c6e75b06d1be852">neoapi.FeatureAccess</a>
</li>
<li>PtpDriftOffsetMode()
: <a class="el" href="a00763.html#a3c049c5cbfac9f4ca6ee4f2d1b81750b">neoapi.FeatureAccess</a>
</li>
<li>PtpDriftOffsetSet()
: <a class="el" href="a00763.html#a79f0d278b421c39b6559aa850faa5526">neoapi.FeatureAccess</a>
</li>
<li>PtpEnable()
: <a class="el" href="a00763.html#a3dd5e557824b965d311898dcfe16ff8d">neoapi.FeatureAccess</a>
</li>
<li>PtpGrandmasterClockID()
: <a class="el" href="a00763.html#a5c3d324f38f348bf2f4dbe2431b5f327">neoapi.FeatureAccess</a>
</li>
<li>PtpKi()
: <a class="el" href="a00763.html#ae2ad0f96eb85cf5e8c68afa36436ad92">neoapi.FeatureAccess</a>
</li>
<li>PtpKp()
: <a class="el" href="a00763.html#a713f53f907d58ae0ad67beecc3694928">neoapi.FeatureAccess</a>
</li>
<li>PtpMode()
: <a class="el" href="a00763.html#a28b5957af9cfd8a6bce0e10a510c8184">neoapi.FeatureAccess</a>
</li>
<li>PtpOffsetFromMaster()
: <a class="el" href="a00763.html#a0793c83bc5d89a143156b6a2abd940ce">neoapi.FeatureAccess</a>
</li>
<li>PtpParentClockID()
: <a class="el" href="a00763.html#ac1c53490d42ff0044c647184ddff35be">neoapi.FeatureAccess</a>
</li>
<li>PtpServoStatus()
: <a class="el" href="a00763.html#a65c79eeec1ce610604912f81d9a7c98e">neoapi.FeatureAccess</a>
</li>
<li>PtpServoStatusThreshold()
: <a class="el" href="a00763.html#ae0f356959074b5f396bf7d8326d9e902">neoapi.FeatureAccess</a>
</li>
<li>PtpStatus()
: <a class="el" href="a00763.html#a3790f04d0b9a700ba08ff7f43b8ae29b">neoapi.FeatureAccess</a>
</li>
<li>PtpSyncMessageInterval()
: <a class="el" href="a00763.html#abdf8a3cd1d2fef2e44d781e207f46b35">neoapi.FeatureAccess</a>
</li>
<li>PtpSyncMessageIntervalStatus()
: <a class="el" href="a00763.html#a2266dee6e0cb04e3e8b90dc937861039">neoapi.FeatureAccess</a>
</li>
<li>PtpTimestampOffset()
: <a class="el" href="a00763.html#a0843692070755930d9953a94c6810e76">neoapi.FeatureAccess</a>
</li>
<li>PtpTimestampOffsetMode()
: <a class="el" href="a00763.html#a90f3e9c1bc794f27337c65f8ee70ff3a">neoapi.FeatureAccess</a>
</li>
<li>PtpTimestampOffsetSet()
: <a class="el" href="a00763.html#a7a226224deecd7f471ccd7e2bf3db63d">neoapi.FeatureAccess</a>
</li>
<li>PtpUseControllerTestSettings()
: <a class="el" href="a00763.html#ab5c69d3169dea59393a48dba500b878a">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
