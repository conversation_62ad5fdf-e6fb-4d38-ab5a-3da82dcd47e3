<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class List</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">Class List</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here are the classes, structs, unions and interfaces with brief descriptions:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9658;</span><span class="icona"><span class="icon">N</span></span><a class="el" href="a00091.html" target="_self">neoapi</a></td><td class="desc">Copyright 2018 Baumer Optronic </td></tr>
<tr id="row_0_0_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00823.html" target="_self">BaseFeature</a></td><td class="desc">Base <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> class providing the interface to be used independent of <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> data-type </td></tr>
<tr id="row_0_1_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00839.html" target="_self">BoolFeature</a></td><td class="desc">Class providing the 'IBoolean' interface </td></tr>
<tr id="row_0_2_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00879.html" target="_self">BufferBase</a></td><td class="desc">Base class to derive from for use as user buffer </td></tr>
<tr id="row_0_3_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00095.html" target="_self">CAcquisitionMode</a></td><td class="desc">Sets the acquisition mode of the device </td></tr>
<tr id="row_0_4_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00099.html" target="_self">CAcquisitionStatusSelector</a></td><td class="desc">Selects the internal acquisition signal to read using AcquisitionStatus </td></tr>
<tr id="row_0_5_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00863.html" target="_self">Cam</a></td><td class="desc">Main camera class &mdash; connect, set features, retrieve images This class provides all methods to work with a camera </td></tr>
<tr id="row_0_6_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00859.html" target="_self">CamBase</a></td><td class="desc">Base camera class from which other camera classes inherit functionality This class provides all methods to work with a camera </td></tr>
<tr id="row_0_7_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00883.html" target="_self">CamInfo</a></td><td class="desc">Camera info container class which offers basic information about an available camera If <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">neoapi.CamInfoList</a> is called it will return a list of cameras, you can use the <a class="el" href="a00883.html" title="Camera info container class which offers basic information about an available camera If neoapi....">neoapi.CamInfo()</a> class to get information about a camera in this list </td></tr>
<tr id="row_0_8_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00891.html" target="_self">CamInfoList</a></td><td class="desc">Provides a list of physically connected cameras available to be used/connected with neoAPI You can use this class if you don't know what camera(s) might be connected during program operation </td></tr>
<tr id="row_0_9_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00887.html" target="_self">CamInfoListIterator</a></td><td class="desc">Provides iterator functionality for the <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a> </td></tr>
<tr id="row_0_10_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00103.html" target="_self">CApertureStatus</a></td><td class="desc">Reads the status of the aperture </td></tr>
<tr id="row_0_11_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00107.html" target="_self">CAutoFeatureRegionMode</a></td><td class="desc">Controls the mode of the selected Auto <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> Region </td></tr>
<tr id="row_0_12_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00111.html" target="_self">CAutoFeatureRegionReference</a></td><td class="desc">Selects the Reference Region of interest </td></tr>
<tr id="row_0_13_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00115.html" target="_self">CAutoFeatureRegionSelector</a></td><td class="desc">Selects the region of interest to control </td></tr>
<tr id="row_0_14_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00123.html" target="_self">CBalanceWhiteAuto</a></td><td class="desc">Controls the mode for automatic white balancing between the color channels </td></tr>
<tr id="row_0_15_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00127.html" target="_self">CBalanceWhiteAutoStatus</a></td><td class="desc">Returns the status of BalanceWhiteAuto </td></tr>
<tr id="row_0_16_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00131.html" target="_self">CBaudrate</a></td><td class="desc">Sets the baud rate of the RS232 interface </td></tr>
<tr id="row_0_17_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00135.html" target="_self">CBinningHorizontalMode</a></td><td class="desc">Sets the mode to use to combine horizontal photo-sensitive cells together when BinningHorizontal is used </td></tr>
<tr id="row_0_18_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00139.html" target="_self">CBinningSelector</a></td><td class="desc">Selects which binning engine is controlled by the BinningHorizontal and BinningVertical features </td></tr>
<tr id="row_0_19_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00143.html" target="_self">CBinningVerticalMode</a></td><td class="desc">Sets the mode to use to combine vertical photo-sensitive cells together when BinningVertical is used </td></tr>
<tr id="row_0_20_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00147.html" target="_self">CBlackLevelSelector</a></td><td class="desc">Selects which Black Level is controlled by the various Black Level features </td></tr>
<tr id="row_0_21_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00151.html" target="_self">CBlackSunSuppression</a></td><td class="desc">Controls the sensor internal feature for avoiding the black sun effect </td></tr>
<tr id="row_0_22_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00715.html" target="_self">CboCalibrationDataConfigurationMode</a></td><td class="desc">Controls if the calibration data configuration mode is active </td></tr>
<tr id="row_0_23_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00719.html" target="_self">CboCalibrationMatrixSelector</a></td><td class="desc">Selects the calibration matrix </td></tr>
<tr id="row_0_24_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00723.html" target="_self">CboCalibrationMatrixValueSelector</a></td><td class="desc">Value selector of calibration matrix </td></tr>
<tr id="row_0_25_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00727.html" target="_self">CboCalibrationVectorSelector</a></td><td class="desc">Selects the calibration vector </td></tr>
<tr id="row_0_26_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00731.html" target="_self">CboCalibrationVectorValueSelector</a></td><td class="desc">Value selector of calibration vector </td></tr>
<tr id="row_0_27_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00735.html" target="_self">CboGeometryDistortionValueSelector</a></td><td class="desc">Value Selector of geometry distortion </td></tr>
<tr id="row_0_28_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00119.html" target="_self">CBOPFShift</a></td><td class="desc">Selects the shift factor for 8bit pixel format calculated from 12 bit mode </td></tr>
<tr id="row_0_29_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00155.html" target="_self">CBoSequencerEnable</a></td><td class="desc">Enables the sequencer for special multi-frame mode </td></tr>
<tr id="row_0_30_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00159.html" target="_self">CBoSequencerIOSelector</a></td><td class="desc">Selects the Sequencers output lines </td></tr>
<tr id="row_0_31_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00163.html" target="_self">CBoSequencerMode</a></td><td class="desc">Specifies the running mode of the sequencer </td></tr>
<tr id="row_0_32_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00167.html" target="_self">CBoSequencerSensorDigitizationTaps</a></td><td class="desc">Sets the number of digitized samples outputted simultaneously by the camera A/D conversion stage for the sequencer </td></tr>
<tr id="row_0_33_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00171.html" target="_self">CBoSequencerStart</a></td><td class="desc">Starts or stopps the configured sequence </td></tr>
<tr id="row_0_34_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00739.html" target="_self">CboSerialConfigBaudRate</a></td><td class="desc">Serial interface clock frequency </td></tr>
<tr id="row_0_35_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00743.html" target="_self">CboSerialConfigDataBits</a></td><td class="desc">Number of data bits </td></tr>
<tr id="row_0_36_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00747.html" target="_self">CboSerialConfigParity</a></td><td class="desc">Serial interface parity </td></tr>
<tr id="row_0_37_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00751.html" target="_self">CboSerialConfigStopBits</a></td><td class="desc">Number of stop bits </td></tr>
<tr id="row_0_38_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00755.html" target="_self">CboSerialMode</a></td><td class="desc">States the interface mode of the serial interface </td></tr>
<tr id="row_0_39_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00759.html" target="_self">CboSerialSelector</a></td><td class="desc">Selects which serial interface to configure </td></tr>
<tr id="row_0_40_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00175.html" target="_self">CBrightnessAutoPriority</a></td><td class="desc">Sets the highest priority auto feature to adjust the brightness </td></tr>
<tr id="row_0_41_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00179.html" target="_self">CBrightnessCorrection</a></td><td class="desc">Enables the Brightness Correction </td></tr>
<tr id="row_0_42_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00183.html" target="_self">CCalibrationMatrixColorSelector</a></td><td class="desc">Selects the color calibration matrix </td></tr>
<tr id="row_0_43_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00187.html" target="_self">CCalibrationMatrixValueSelector</a></td><td class="desc">Selects the gain factor of the selected calibration matrix </td></tr>
<tr id="row_0_44_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00191.html" target="_self">CChunkSelector</a></td><td class="desc">Selects which Chunk to enable or control </td></tr>
<tr id="row_0_45_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00195.html" target="_self">CClConfiguration</a></td><td class="desc">This Camera Link specific feature describes the configuration used by the camera </td></tr>
<tr id="row_0_46_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00199.html" target="_self">CClTimeSlotsCount</a></td><td class="desc">This Camera Link specific feature describes the time multiplexing of the camera link connection to transfer more than the configuration allows, in one single clock </td></tr>
<tr id="row_0_47_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00203.html" target="_self">CColorTransformationAuto</a></td><td class="desc">Controls the mode for automatic adjusting the gains of the active transformation matrix </td></tr>
<tr id="row_0_48_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00207.html" target="_self">CColorTransformationFactoryListSelector</a></td><td class="desc">Selects the color transformation factory list tuned to the given color temeperature </td></tr>
<tr id="row_0_49_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00211.html" target="_self">CColorTransformationSelector</a></td><td class="desc">Selects which Color Transformation module is controlled by the various Color Transformation features </td></tr>
<tr id="row_0_50_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00215.html" target="_self">CColorTransformationValueSelector</a></td><td class="desc">Selects the Gain factor or Offset of the Transformation matrix to access in the selected Color Transformation module </td></tr>
<tr id="row_0_51_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00219.html" target="_self">CComponentSelector</a></td><td class="desc">Selects a component to activate/deactivate its data streaming </td></tr>
<tr id="row_0_52_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00223.html" target="_self">CCounterEventActivation</a></td><td class="desc">Selects the Activation mode Event Source signal </td></tr>
<tr id="row_0_53_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00227.html" target="_self">CCounterEventSource</a></td><td class="desc">Select the events that will be the source to increment the Counter </td></tr>
<tr id="row_0_54_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00231.html" target="_self">CCounterResetActivation</a></td><td class="desc">Selects the Activation mode of the Counter Reset Source signal </td></tr>
<tr id="row_0_55_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00235.html" target="_self">CCounterResetSource</a></td><td class="desc">Selects the signals that will be the source to reset the Counter </td></tr>
<tr id="row_0_56_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00239.html" target="_self">CCounterSelector</a></td><td class="desc">Selects which Counter to configure </td></tr>
<tr id="row_0_57_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00243.html" target="_self">CCustomDataConfigurationMode</a></td><td class="desc">Controls if the custom data configuration mode is active </td></tr>
<tr id="row_0_58_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00247.html" target="_self">CDecimationHorizontalMode</a></td><td class="desc">Sets the mode used to reduce the horizontal resolution when DecimationHorizontal is used </td></tr>
<tr id="row_0_59_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00251.html" target="_self">CDecimationVerticalMode</a></td><td class="desc">Sets the mode used to reduce the Vertical resolution when DecimationVertical is used </td></tr>
<tr id="row_0_60_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00255.html" target="_self">CDefectPixelListSelector</a></td><td class="desc">Selects which Defect Pixel List to control </td></tr>
<tr id="row_0_61_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00259.html" target="_self">CDeviceCharacterSet</a></td><td class="desc">Character set used by the strings of the device </td></tr>
<tr id="row_0_62_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00263.html" target="_self">CDeviceClockSelector</a></td><td class="desc">Selects the clock frequency to access from the device </td></tr>
<tr id="row_0_63_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00267.html" target="_self">CDeviceFrontUARTSource</a></td><td class="desc">Source control for frontside UART interface </td></tr>
<tr id="row_0_64_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00271.html" target="_self">CDeviceLicense</a></td><td class="desc">Returns if the license at the device is valid or not for the license type, selected by the DeviceLicenseTypeSelector feature </td></tr>
<tr id="row_0_65_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00275.html" target="_self">CDeviceLicenseTypeSelector</a></td><td class="desc">Selects the available License types </td></tr>
<tr id="row_0_66_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00279.html" target="_self">CDeviceLinkHeartbeatMode</a></td><td class="desc">Activate or deactivate the Link's heartbeat </td></tr>
<tr id="row_0_67_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00283.html" target="_self">CDeviceLinkSelector</a></td><td class="desc">Selects which Link of the device to control </td></tr>
<tr id="row_0_68_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00287.html" target="_self">CDeviceLinkThroughputLimitMode</a></td><td class="desc">Controls if the DeviceLinkThroughputLimit is active </td></tr>
<tr id="row_0_69_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00291.html" target="_self">CDeviceRegistersEndianness</a></td><td class="desc">Endianness of the registers of the device </td></tr>
<tr id="row_0_70_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00295.html" target="_self">CDeviceScanType</a></td><td class="desc">Scan type of the sensor of the device </td></tr>
<tr id="row_0_71_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00299.html" target="_self">CDeviceSensorSelector</a></td><td class="desc">Selects which sensor is controlled by the various sensor specific features </td></tr>
<tr id="row_0_72_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00303.html" target="_self">CDeviceSensorType</a></td><td class="desc">Specifies the type of the sensor </td></tr>
<tr id="row_0_73_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00307.html" target="_self">CDeviceSensorVersion</a></td><td class="desc">Specifies the version of the CMOSIS sensor </td></tr>
<tr id="row_0_74_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00311.html" target="_self">CDeviceSerialPortBaudRate</a></td><td class="desc">This feature controls the baud rate used by the selected serial port </td></tr>
<tr id="row_0_75_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00315.html" target="_self">CDeviceSerialPortSelector</a></td><td class="desc">Selects which serial port of the device to control </td></tr>
<tr id="row_0_76_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00319.html" target="_self">CDeviceStreamChannelEndianness</a></td><td class="desc">Endianness of multi-byte pixel data for this stream </td></tr>
<tr id="row_0_77_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00323.html" target="_self">CDeviceStreamChannelType</a></td><td class="desc">Reports the type of the stream channel </td></tr>
<tr id="row_0_78_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00331.html" target="_self">CDeviceTapGeometry</a></td><td class="desc">This device tap geometry feature describes the geometrical properties characterizing the taps of a camera as presented at the output of the device </td></tr>
<tr id="row_0_79_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00335.html" target="_self">CDeviceTemperatureSelector</a></td><td class="desc">Selects the location within the device, where the temperature will be measured </td></tr>
<tr id="row_0_80_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00339.html" target="_self">CDeviceTemperatureStatus</a></td><td class="desc">Returns the current temperature status of the device </td></tr>
<tr id="row_0_81_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00343.html" target="_self">CDeviceTemperatureStatusTransitionSelector</a></td><td class="desc">Selects which temperature transition is controlled by the feature DeviceTemperatureStatusTransition </td></tr>
<tr id="row_0_82_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00327.html" target="_self">CDeviceTLType</a></td><td class="desc">Transport Layer type of the device </td></tr>
<tr id="row_0_83_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00347.html" target="_self">CDeviceType</a></td><td class="desc">Returns the device type </td></tr>
<tr id="row_0_84_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00351.html" target="_self">CEventNotification</a></td><td class="desc">Activate or deactivate the notification to the host application of the occurrence of the selected Event </td></tr>
<tr id="row_0_85_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00355.html" target="_self">CEventSelector</a></td><td class="desc">Selects which Event to signal to the host application </td></tr>
<tr id="row_0_86_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00359.html" target="_self">CExposureAuto</a></td><td class="desc">Sets the automatic exposure mode when ExposureMode is Timed </td></tr>
<tr id="row_0_87_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00363.html" target="_self">CExposureMode</a></td><td class="desc">Sets the operation mode of the Exposure </td></tr>
<tr id="row_0_88_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00367.html" target="_self">CFileOpenMode</a></td><td class="desc">Selects the access mode in which a file is opened in the device </td></tr>
<tr id="row_0_89_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00371.html" target="_self">CFileOperationSelector</a></td><td class="desc">Selects the target operation for the selected file in the device </td></tr>
<tr id="row_0_90_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00375.html" target="_self">CFileOperationStatus</a></td><td class="desc">Represents the file operation execution status </td></tr>
<tr id="row_0_91_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00379.html" target="_self">CFileSelector</a></td><td class="desc">Selects the target file in the device </td></tr>
<tr id="row_0_92_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00383.html" target="_self">CFocalLengthStatus</a></td><td class="desc">Reads the status of the focal length </td></tr>
<tr id="row_0_93_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00387.html" target="_self">CFocusStatus</a></td><td class="desc">Reads the status of the focus </td></tr>
<tr id="row_0_94_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00391.html" target="_self">CGainAuto</a></td><td class="desc">Sets the automatic gain control (AGC) mode </td></tr>
<tr id="row_0_95_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00395.html" target="_self">CGainSelector</a></td><td class="desc">Selects which Gain is controlled by the various Gain features </td></tr>
<tr id="row_0_96_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00399.html" target="_self">CGenDCStreamingMode</a></td><td class="desc">Controls the device's streaming format </td></tr>
<tr id="row_0_97_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00403.html" target="_self">CGenDCStreamingStatus</a></td><td class="desc">Returns whether the current device data streaming format is GenDC </td></tr>
<tr id="row_0_98_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00407.html" target="_self">CGevCCP</a></td><td class="desc">Controls the device access privilege of an application </td></tr>
<tr id="row_0_99_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00411.html" target="_self">CGevGVCPExtendedStatusCodesSelector</a></td><td class="desc">Selects the GigE Vision version to control extended status codes for </td></tr>
<tr id="row_0_100_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00415.html" target="_self">CGevIPConfigurationStatus</a></td><td class="desc">Reports the current IP configuration status </td></tr>
<tr id="row_0_101_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00419.html" target="_self">CGevSupportedOptionSelector</a></td><td class="desc">Selects the GEV option to interrogate for existing support </td></tr>
<tr id="row_0_102_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00423.html" target="_self">CHDRGainRatioSelector</a></td><td class="desc">Selects the gain ratio for HDR mode </td></tr>
<tr id="row_0_103_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00427.html" target="_self">CHDRTonemappingCurvePresetSelector</a></td><td class="desc">Selects the predefined transfer curve for global tone-mapping of the calculated HDR image </td></tr>
<tr id="row_0_104_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00431.html" target="_self">CImageCompressionJPEGFormatOption</a></td><td class="desc">When JPEG is selected as the compression format, a device might optionally offer better control over JPEG-specific options through this feature </td></tr>
<tr id="row_0_105_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00435.html" target="_self">CImageCompressionMode</a></td><td class="desc">Enable a specific image compression mode as the base mode for image transfer </td></tr>
<tr id="row_0_106_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00439.html" target="_self">CImageCompressionRateOption</a></td><td class="desc">Two rate controlling options are offered: fixed bit rate or fixed quality </td></tr>
<tr id="row_0_107_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00443.html" target="_self">CInterfaceSpeedMode</a></td><td class="desc">Returns the interface speed mode as string </td></tr>
<tr id="row_0_108_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00455.html" target="_self">CLineFormat</a></td><td class="desc">Controls the current electrical format of the selected physical input or output Line </td></tr>
<tr id="row_0_109_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00459.html" target="_self">CLineMode</a></td><td class="desc">Controls if the physical Line is used to Input or Output a signal </td></tr>
<tr id="row_0_110_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00463.html" target="_self">CLinePWMConfigurationMode</a></td><td class="desc">Enables the line PWM configuration mode </td></tr>
<tr id="row_0_111_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00467.html" target="_self">CLinePWMMode</a></td><td class="desc">Enables the line PWM configuration mode </td></tr>
<tr id="row_0_112_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00471.html" target="_self">CLineSelector</a></td><td class="desc">Selects the physical line (or pin) of the external device connector or the virtual line of the Transport Layer to configure </td></tr>
<tr id="row_0_113_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00475.html" target="_self">CLineSource</a></td><td class="desc">Selects which internal acquisition or I/O source signal to output on the selected Line </td></tr>
<tr id="row_0_114_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00447.html" target="_self">CLUTContent</a></td><td class="desc">Specifies the content of the selected LUT </td></tr>
<tr id="row_0_115_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00451.html" target="_self">CLUTSelector</a></td><td class="desc">Selects which LUT to control </td></tr>
<tr id="row_0_116_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00479.html" target="_self">CMemoryActivePart</a></td><td class="desc">Returns the active memory part to write the images in </td></tr>
<tr id="row_0_117_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00483.html" target="_self">CMemoryMode</a></td><td class="desc">Controls the mode to use the memory </td></tr>
<tr id="row_0_118_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00487.html" target="_self">CMemoryPartIncrementSource</a></td><td class="desc">Selects the source to switch the active memory part </td></tr>
<tr id="row_0_119_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00491.html" target="_self">CMemoryPartMode</a></td><td class="desc">Selects the mode to use for the selected memory part </td></tr>
<tr id="row_0_120_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00495.html" target="_self">CMemoryPartSelector</a></td><td class="desc">Selects on of the available memory parts </td></tr>
<tr id="row_0_121_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00795.html" target="_self">ColorMatrix</a></td><td class="desc">Class for the Color Transformation Matrix This class provides methods to configure the Color Matrix for color cameras </td></tr>
<tr id="row_0_122_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00843.html" target="_self">CommandFeature</a></td><td class="desc">Class providing the 'ICommand' interface </td></tr>
<tr id="row_0_123_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00799.html" target="_self">ConverterSettings</a></td><td class="desc"><a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> post processing settings This class provides methods to configure image conversions </td></tr>
<tr id="row_0_124_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00499.html" target="_self">COpticControllerSelector</a></td><td class="desc">Selects which optic controller to configure </td></tr>
<tr id="row_0_125_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00503.html" target="_self">COpticControllerStatus</a></td><td class="desc">Reads the status of the optic controller </td></tr>
<tr id="row_0_126_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00507.html" target="_self">CPartialScanEnabled</a></td><td class="desc">Enables the partial scan readout </td></tr>
<tr id="row_0_127_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00511.html" target="_self">CPixelFormat</a></td><td class="desc">Format of the pixels provided by the device </td></tr>
<tr id="row_0_128_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00515.html" target="_self">CPtpClockAccuracy</a></td><td class="desc">Indicates the expected accuracy of the device PTP clock when it is the grandmaster, or in the event it becomes the grandmaster </td></tr>
<tr id="row_0_129_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00519.html" target="_self">CPtpClockOffsetMode</a></td><td class="desc">Sets the mode to handle PtpClockOffset for command PtpClockOffsetSet </td></tr>
<tr id="row_0_130_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00523.html" target="_self">CPtpDriftOffsetMode</a></td><td class="desc">Sets the mode to handle PtpDriftOffset for command PtpDriftOffsetSet </td></tr>
<tr id="row_0_131_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00527.html" target="_self">CPtpMode</a></td><td class="desc">Selects the PTP clock type the device will act as </td></tr>
<tr id="row_0_132_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00531.html" target="_self">CPtpServoStatus</a></td><td class="desc">Returns the latched state of the clock servo </td></tr>
<tr id="row_0_133_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00535.html" target="_self">CPtpStatus</a></td><td class="desc">Returns the latched state of the PTP clock </td></tr>
<tr id="row_0_134_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00539.html" target="_self">CPtpSyncMessageIntervalStatus</a></td><td class="desc">Returns if the latched sync message interval from the PTP master clock is supported by the device </td></tr>
<tr id="row_0_135_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00543.html" target="_self">CPtpTimestampOffsetMode</a></td><td class="desc">Sets the mode to handle PtpTimestampOffset for command PtpTimestampOffsetSet </td></tr>
<tr id="row_0_136_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00547.html" target="_self">CReadOutBuffering</a></td><td class="desc">Selects the number of image buffers filled with data of sensor output </td></tr>
<tr id="row_0_137_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00551.html" target="_self">CReadoutMode</a></td><td class="desc">Specifies the operation mode of the readout for the acquisition </td></tr>
<tr id="row_0_138_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00555.html" target="_self">CRegionAcquisitionMode</a></td><td class="desc">Returns the acquisition mode of the regions </td></tr>
<tr id="row_0_139_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00559.html" target="_self">CRegionConfigurationMode</a></td><td class="desc">Returns the configuration mode of the regions </td></tr>
<tr id="row_0_140_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00563.html" target="_self">CRegionMode</a></td><td class="desc">Controls if the selected Region of interest is active and streaming </td></tr>
<tr id="row_0_141_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00567.html" target="_self">CRegionSelector</a></td><td class="desc">Selects the Region of interest to control </td></tr>
<tr id="row_0_142_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00571.html" target="_self">CRegionTransferMode</a></td><td class="desc">Returns the transfer mode of the regions </td></tr>
<tr id="row_0_143_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00579.html" target="_self">CSensorADDigitization</a></td><td class="desc">Controls the sensors AD digitization in bits per pixels </td></tr>
<tr id="row_0_144_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00583.html" target="_self">CSensorCutConfigurationMode</a></td><td class="desc">Controls if the sensor adjustment configuration mode is active </td></tr>
<tr id="row_0_145_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00587.html" target="_self">CSensorDigitizationTaps</a></td><td class="desc">Number of digitized samples outputted simultaneously by the camera A/D conversion stage </td></tr>
<tr id="row_0_146_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00591.html" target="_self">CSensorShutterMode</a></td><td class="desc">Specifies the shutter mode of the device </td></tr>
<tr id="row_0_147_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00595.html" target="_self">CSensorTaps</a></td><td class="desc">Number of taps of the camera sensor </td></tr>
<tr id="row_0_148_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00599.html" target="_self">CSequencerConfigurationMode</a></td><td class="desc">Controls if the sequencer configuration mode is active </td></tr>
<tr id="row_0_149_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00603.html" target="_self">CSequencerFeatureSelector</a></td><td class="desc">Selects which sequencer features to control </td></tr>
<tr id="row_0_150_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00607.html" target="_self">CSequencerMode</a></td><td class="desc">Controls if the sequencer mechanism is active </td></tr>
<tr id="row_0_151_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00611.html" target="_self">CSequencerTriggerActivation</a></td><td class="desc">Specifies the activation mode of the sequencer trigger </td></tr>
<tr id="row_0_152_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00615.html" target="_self">CSequencerTriggerSource</a></td><td class="desc">Specifies the internal signal or physical input line to use as the sequencer trigger source </td></tr>
<tr id="row_0_153_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00619.html" target="_self">CShadingSelector</a></td><td class="desc">Selects the Shading Port Address </td></tr>
<tr id="row_0_154_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00623.html" target="_self">CSharpeningMode</a></td><td class="desc">Selects the Sharpening Mode </td></tr>
<tr id="row_0_155_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00575.html" target="_self">CSIControl</a></td><td class="desc">Controls the streaming operation </td></tr>
<tr id="row_0_156_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00627.html" target="_self">CSourceID</a></td><td class="desc">Returns a unique Identifier value that correspond to the selected Source </td></tr>
<tr id="row_0_157_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00631.html" target="_self">CSourceSelector</a></td><td class="desc">Selects the source to control </td></tr>
<tr id="row_0_158_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00635.html" target="_self">CSwitchPortSelector</a></td><td class="desc">Selects the port for the port related features </td></tr>
<tr id="row_0_159_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00639.html" target="_self">CTestPattern</a></td><td class="desc">Selects the type of test pattern that is generated by the device as image source </td></tr>
<tr id="row_0_160_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00643.html" target="_self">CTestPatternGeneratorSelector</a></td><td class="desc">Selects which test pattern generator is controlled by the TestPattern feature </td></tr>
<tr id="row_0_161_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00647.html" target="_self">CTestPayloadFormatMode</a></td><td class="desc">This feature allows setting a device in test mode and to output a specific payload format for validation of data streaming </td></tr>
<tr id="row_0_162_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00651.html" target="_self">CTimerSelector</a></td><td class="desc">Selects which Timer to configure </td></tr>
<tr id="row_0_163_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00655.html" target="_self">CTimerTriggerActivation</a></td><td class="desc">Selects the activation mode of the trigger to start the Timer </td></tr>
<tr id="row_0_164_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00659.html" target="_self">CTimerTriggerSource</a></td><td class="desc">Selects the source of the trigger to start the Timer </td></tr>
<tr id="row_0_165_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00663.html" target="_self">CTransferControlMode</a></td><td class="desc">Selects the control method for the transfers </td></tr>
<tr id="row_0_166_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00667.html" target="_self">CTransferOperationMode</a></td><td class="desc">Selects the operation mode of the transfer </td></tr>
<tr id="row_0_167_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00671.html" target="_self">CTransferSelector</a></td><td class="desc">Selects which stream transfers are currently controlled by the selected Transfer features </td></tr>
<tr id="row_0_168_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00675.html" target="_self">CTransferStatusSelector</a></td><td class="desc">Selects which status of the transfer module to read </td></tr>
<tr id="row_0_169_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00679.html" target="_self">CTriggerActivation</a></td><td class="desc">Specifies the activation mode of the trigger </td></tr>
<tr id="row_0_170_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00683.html" target="_self">CTriggerMode</a></td><td class="desc">Controls if the selected trigger is active </td></tr>
<tr id="row_0_171_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00687.html" target="_self">CTriggerOverlap</a></td><td class="desc">Specifies the type trigger overlap permitted with the previous frame or line </td></tr>
<tr id="row_0_172_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00691.html" target="_self">CTriggerSelector</a></td><td class="desc">Selects the type of trigger to configure </td></tr>
<tr id="row_0_173_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00695.html" target="_self">CTriggerSource</a></td><td class="desc">Specifies the internal signal or physical input Line to use as the trigger source </td></tr>
<tr id="row_0_174_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00699.html" target="_self">CUserOutputSelector</a></td><td class="desc">Selects which bit of the User Output register will be set by UserOutputValue </td></tr>
<tr id="row_0_175_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00703.html" target="_self">CUserSetDefault</a></td><td class="desc">Selects the feature User Set to load and make active by default when the device is reset </td></tr>
<tr id="row_0_176_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00707.html" target="_self">CUserSetFeatureSelector</a></td><td class="desc">Selects which individual UserSet feature to control </td></tr>
<tr id="row_0_177_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00711.html" target="_self">CUserSetSelector</a></td><td class="desc">Selects the feature User Set to load, save or configure </td></tr>
<tr id="row_0_178_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00827.html" target="_self">DoubleFeature</a></td><td class="desc">Class providing the 'IFloat' interface </td></tr>
<tr id="row_0_179_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00847.html" target="_self">EnumerationFeature</a></td><td class="desc">Base class providing the 'IEnumeration' interface </td></tr>
<tr id="row_0_180_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00811.html" target="_self">Feature</a></td><td class="desc">Provides access to camera features This class provides an easy way to work with camera features </td></tr>
<tr id="row_0_181_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00763.html" target="_self">FeatureAccess</a></td><td class="desc">Class to controll GenICam features </td></tr>
<tr id="row_0_182_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00779.html" target="_self">FeatureAccessException</a></td><td class="desc"><a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> not accessible Exception </td></tr>
<tr id="row_0_183_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00815.html" target="_self">FeatureList</a></td><td class="desc">Provides list functionality for camera features </td></tr>
<tr id="row_0_184_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00819.html" target="_self">FeatureListIterator</a></td><td class="desc">Provides iterator functionality for the <a class="el" href="a00815.html" title="Provides list functionality for camera features.">FeatureList</a> </td></tr>
<tr id="row_0_185_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00867.html" target="_self">FeatureStack</a></td><td class="desc">A collection of camera features The <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a> provides you with the ability to write many GenICam <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> values quickly to a camera </td></tr>
<tr id="row_0_186_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00787.html" target="_self">FileAccessException</a></td><td class="desc">File not accessible Exception </td></tr>
<tr id="row_0_187_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00855.html" target="_self">Image</a></td><td class="desc">Provides an object to get access to image data and its properties The <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> object is used to retrieve, store and convert images obtained from a camera </td></tr>
<tr id="row_0_188_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00803.html" target="_self">ImageInfo</a></td><td class="desc">Provides an object to get access to image properties even before streaming The <a class="el" href="a00803.html" title="Provides an object to get access to image properties even before streaming The ImageInfo object give ...">ImageInfo</a> object give access to basic image information like width and height </td></tr>
<tr id="row_0_189_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00831.html" target="_self">IntegerFeature</a></td><td class="desc">Class providing the 'IInteger' interface </td></tr>
<tr id="row_0_190_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00791.html" target="_self">InvalidArgumentException</a></td><td class="desc">Invalid Arguments Exception </td></tr>
<tr id="row_0_191_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00807.html" target="_self">NeoEvent</a></td><td class="desc">Provides access to events This class provides an easy way to work with events </td></tr>
<tr id="row_0_192_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00875.html" target="_self">NeoEventCallback</a></td><td class="desc">Event callback class to derive from an get event data </td></tr>
<tr id="row_0_193_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00767.html" target="_self">NeoException</a></td><td class="desc">Base neoAPI Exception class </td></tr>
<tr id="row_0_194_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00871.html" target="_self">NeoImageCallback</a></td><td class="desc"><a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> callback class to derive from an get image data </td></tr>
<tr id="row_0_195_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00899.html" target="_self">NeoTrace</a></td><td class="desc">Trace class which offers the possibility to enable trace for different targets </td></tr>
<tr id="row_0_196_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00895.html" target="_self">NeoTraceCallback</a></td><td class="desc">Trace callback class to derive from an get Trace messages </td></tr>
<tr id="row_0_197_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00775.html" target="_self">NoAccessException</a></td><td class="desc">Camera not accessible Exception </td></tr>
<tr id="row_0_198_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00783.html" target="_self">NoImageBufferException</a></td><td class="desc">Requesting an image while holding all available image resources </td></tr>
<tr id="row_0_199_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00771.html" target="_self">NotConnectedException</a></td><td class="desc">No camera connected Exception </td></tr>
<tr id="row_0_200_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00851.html" target="_self">RegisterFeature</a></td><td class="desc">Base class providing the 'IRegister' interface </td></tr>
<tr id="row_0_201_" style="display:none;"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00835.html" target="_self">StringFeature</a></td><td class="desc">Class providing the 'IString' interface </td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
