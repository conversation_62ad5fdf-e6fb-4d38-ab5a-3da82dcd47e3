<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.FeatureStack Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00867.html">FeatureStack</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00864.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.FeatureStack Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00088.html">Supporting Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>A collection of camera features The <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a> provides you with the ability to write many GenICam <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> values quickly to a camera.  
 <a href="a00867.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.FeatureStack:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00867.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a15d0a7489021ebe2e9663c5e7d8c2497"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00867.html#a15d0a7489021ebe2e9663c5e7d8c2497">__init__</a> (self, *args)</td></tr>
<tr class="memdesc:a15d0a7489021ebe2e9663c5e7d8c2497"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#a15d0a7489021ebe2e9663c5e7d8c2497">More...</a><br /></td></tr>
<tr class="separator:a15d0a7489021ebe2e9663c5e7d8c2497"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6bb971602bd5d990e484b9ba12994a31"><td class="memItemLeft" align="right" valign="top">&quot;FeatureStack&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00867.html#a6bb971602bd5d990e484b9ba12994a31">Add</a> (self, *args)</td></tr>
<tr class="memdesc:a6bb971602bd5d990e484b9ba12994a31"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add a feature/value pair to the <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a>.  <a href="#a6bb971602bd5d990e484b9ba12994a31">More...</a><br /></td></tr>
<tr class="separator:a6bb971602bd5d990e484b9ba12994a31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a736e337fa52dd92bb4143cfde205b762"><td class="memItemLeft" align="right" valign="top">&quot;FeatureStack&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00867.html#a736e337fa52dd92bb4143cfde205b762">Clear</a> (self)</td></tr>
<tr class="memdesc:a736e337fa52dd92bb4143cfde205b762"><td class="mdescLeft">&#160;</td><td class="mdescRight">Remove all features from the collection.  <a href="#a736e337fa52dd92bb4143cfde205b762">More...</a><br /></td></tr>
<tr class="separator:a736e337fa52dd92bb4143cfde205b762"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a748784192fb01e27b6d4c6e3718b23ff"><td class="memItemLeft" align="right" valign="top">&quot;FeatureStack&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00867.html#a748784192fb01e27b6d4c6e3718b23ff">SetReplaceMode</a> (self, &quot;bool&quot; replace=True)</td></tr>
<tr class="memdesc:a748784192fb01e27b6d4c6e3718b23ff"><td class="mdescLeft">&#160;</td><td class="mdescRight">In replace mode register accesses are merged together.  <a href="#a748784192fb01e27b6d4c6e3718b23ff">More...</a><br /></td></tr>
<tr class="separator:a748784192fb01e27b6d4c6e3718b23ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a16d9bc334da35fa5b8b19cb356d2490c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00867.html#a16d9bc334da35fa5b8b19cb356d2490c">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:a16d9bc334da35fa5b8b19cb356d2490c"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#a16d9bc334da35fa5b8b19cb356d2490c">More...</a><br /></td></tr>
<tr class="separator:a16d9bc334da35fa5b8b19cb356d2490c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A collection of camera features The <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a> provides you with the ability to write many GenICam <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> values quickly to a camera. </p>
<p>Instead of sending/waiting for validation for each single feature the <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a> allows to send many Features in one operation and only validate the whole operation once </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a15d0a7489021ebe2e9663c5e7d8c2497"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a15d0a7489021ebe2e9663c5e7d8c2497">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.FeatureStack.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructor. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a6bb971602bd5d990e484b9ba12994a31"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6bb971602bd5d990e484b9ba12994a31">&#9670;&nbsp;</a></span>Add()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;FeatureStack&quot; neoapi.FeatureStack.Add </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Add a feature/value pair to the <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a>. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>name</b> The name of the feature to write (SFNC Name)<br />
 <b>value</b> (optional) The value which should be written to the feature </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a> object</dd></dl>
<div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line">fs = <a class="code" href="a00867.html">neoapi.FeatureStack</a>()   <span class="comment"># create object</span></div><div class="line">fs.Add(<span class="stringliteral">&quot;Gain&quot;</span>, 1.84)         <span class="comment"># add some feature/value pairs</span></div><div class="line">fs.Add(<span class="stringliteral">&quot;ExposureTime&quot;</span>, 400)</div><div class="line">fs.Add(<span class="stringliteral">&quot;Width&quot;</span>, 640)</div><div class="line">fs.Add(<span class="stringliteral">&quot;Height&quot;</span>, 480)</div><div class="line"></div><div class="line">cam = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line">cam.Connect()</div><div class="line">cam.WriteFeatureStack(fs)    <span class="comment"># write to the camera</span></div><div class="line">print(cam.f.Gain.value)      <span class="comment"># check that the feature was set</span></div></div><!-- fragment --> 
</div>
</div>
<a id="a736e337fa52dd92bb4143cfde205b762"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a736e337fa52dd92bb4143cfde205b762">&#9670;&nbsp;</a></span>Clear()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;FeatureStack&quot; neoapi.FeatureStack.Clear </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Remove all features from the collection. </p>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a> object </dd></dl>

</div>
</div>
<a id="a748784192fb01e27b6d4c6e3718b23ff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a748784192fb01e27b6d4c6e3718b23ff">&#9670;&nbsp;</a></span>SetReplaceMode()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;FeatureStack&quot; neoapi.FeatureStack.SetReplaceMode </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;bool&quot;&#160;</td>
          <td class="paramname"><em>replace</em> = <code>True</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>In replace mode register accesses are merged together. </p>
<p>Only the merge result is written to the camera Use the replace mode to speed up feature changes. In some scenarios, it is desired to write each value separately. This may be the case if some values are to be applied before a SW trigger and some after </p><dl class="section note"><dt>Note</dt><dd>The ReplaceMode is deactivated by default </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">replace</td><td>Combine multiple register accesses if possible </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a> object </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a16d9bc334da35fa5b8b19cb356d2490c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16d9bc334da35fa5b8b19cb356d2490c">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.FeatureStack.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
