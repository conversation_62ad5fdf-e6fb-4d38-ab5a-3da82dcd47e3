import socket
import threading
import time
import logging
import requests
from typing import List, Dict
import xml.etree.ElementTree as ET
import ipaddress
import subprocess
import platform

class CameraDiscovery:
    """Entdeckt Baumer-Kameras im Netzwerk"""

    def __init__(self):
        self.discovered_cameras = []
        self.discovery_running = False
        self.selected_interface = None
        self.available_interfaces = self.get_network_interfaces()

    def get_network_interfaces(self) -> List[Dict]:
        """Gibt verfügbare Netzwerkkarten zurück"""
        interfaces = []

        try:
            # Verwende System-Befehle zur Netzwerkkarten-Erkennung
            if platform.system() == "Windows":
                interfaces = self._get_windows_interfaces()
            else:
                interfaces = self._get_unix_interfaces()

            # Fallback falls keine Interfaces gefunden
            if not interfaces:
                logging.warning("Keine Netzwerk-Interfaces gefunden, verwende Standard-Bereiche")
                interfaces = self._get_default_networks()

        except Exception as e:
            logging.error(f"<PERSON><PERSON> beim Ermitteln der Netzwerkkarten: {e}")
            interfaces = self._get_default_networks()

        logging.info(f"{len(interfaces)} Netzwerk-Interfaces gefunden")
        return interfaces

    def _get_windows_interfaces(self) -> List[Dict]:
        """Ermittelt Windows-Netzwerkkarten"""
        interfaces = []

        try:
            # Verwende ipconfig /all für detaillierte Informationen
            result = subprocess.run(['ipconfig', '/all'], capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                current_adapter = None
                current_ip = None
                current_subnet = None

                for line in lines:
                    line_stripped = line.strip()

                    # Erkenne Adapter-Namen
                    if 'adapter' in line.lower() and ':' in line:
                        # Speichere vorherigen Adapter falls vorhanden
                        if current_adapter and current_ip:
                            self._add_interface(interfaces, current_adapter, current_ip, current_subnet)

                        current_adapter = line.split(':')[0].strip()
                        current_ip = None
                        current_subnet = None
                        logging.debug(f"Gefundener Adapter: {current_adapter}")

                    # Erkenne IPv4-Adressen
                    elif 'IPv4' in line and current_adapter:
                        try:
                            ip_part = line.split(':')[1].strip()
                            # Entferne (Preferred) oder ähnliche Zusätze
                            ip = ip_part.split('(')[0].strip()

                            if self._is_valid_ip(ip):
                                current_ip = ip
                                logging.debug(f"Gefundene IP für {current_adapter}: {ip}")
                        except Exception as e:
                            logging.debug(f"IPv4-Parsing Fehler: {e}")

                    # Erkenne Subnetzmasken
                    elif 'Subnet Mask' in line or 'Subnetzmaske' in line:
                        try:
                            subnet = line.split(':')[1].strip()
                            if subnet:
                                current_subnet = subnet
                                logging.debug(f"Gefundene Subnetzmaske: {subnet}")
                        except Exception as e:
                            logging.debug(f"Subnet-Parsing Fehler: {e}")

                # Füge letzten Adapter hinzu
                if current_adapter and current_ip:
                    self._add_interface(interfaces, current_adapter, current_ip, current_subnet)

            # Fallback: Verwende einfaches ipconfig
            if not interfaces:
                logging.info("Fallback auf einfaches ipconfig")
                result = subprocess.run(['ipconfig'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    current_adapter = None

                    for line in lines:
                        line_stripped = line.strip()

                        if 'adapter' in line.lower() and ':' in line:
                            current_adapter = line.split(':')[0].strip()
                        elif 'IPv4' in line and current_adapter:
                            try:
                                ip = line.split(':')[1].strip()
                                if self._is_valid_ip(ip):
                                    self._add_interface(interfaces, current_adapter, ip, '*************')
                            except Exception as e:
                                logging.debug(f"Fallback IP-Parsing Fehler: {e}")

        except Exception as e:
            logging.error(f"Windows Interface-Erkennung Fehler: {e}")

        logging.info(f"Windows: {len(interfaces)} Netzwerk-Interfaces erkannt")
        return interfaces

    def _is_valid_ip(self, ip: str) -> bool:
        """Prüft ob IP-Adresse gültig und verwendbar ist"""
        if not ip or ip == '127.0.0.1':
            return False
        if ip.startswith('169.254'):  # APIPA
            return False
        if ip.startswith('0.') or ip.startswith('255.'):
            return False

        try:
            # Prüfe IP-Format
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                num = int(part)
                if num < 0 or num > 255:
                    return False
            return True
        except:
            return False

    def _add_interface(self, interfaces: List[Dict], adapter_name: str, ip: str, subnet_mask: str = None):
        """Fügt Interface zur Liste hinzu"""
        try:
            if not subnet_mask:
                subnet_mask = '*************'  # Standard /24

            # Berechne Netzwerk-Bereich
            network_base = '.'.join(ip.split('.')[:-1])

            interface_info = {
                'name': adapter_name,
                'ip': ip,
                'netmask': subnet_mask,
                'network_base': network_base,
                'description': f"{adapter_name} ({ip})"
            }

            # Prüfe auf Duplikate
            for existing in interfaces:
                if existing['ip'] == ip:
                    return  # Bereits vorhanden

            interfaces.append(interface_info)
            logging.info(f"Interface hinzugefügt: {adapter_name} - {ip}")

        except Exception as e:
            logging.debug(f"Fehler beim Hinzufügen des Interface: {e}")

    def _get_unix_interfaces(self) -> List[Dict]:
        """Ermittelt Unix/Linux-Netzwerkkarten"""
        interfaces = []

        try:
            # Verwende ifconfig oder ip
            commands = [['ifconfig'], ['ip', 'addr']]

            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        # Einfache Parsing-Logik
                        lines = result.stdout.split('\n')
                        for line in lines:
                            if 'inet ' in line and '127.0.0.1' not in line:
                                try:
                                    parts = line.split()
                                    ip = None
                                    for i, part in enumerate(parts):
                                        if part == 'inet' and i + 1 < len(parts):
                                            ip = parts[i + 1].split('/')[0]
                                            break

                                    if ip and not ip.startswith('169.254'):
                                        network_base = '.'.join(ip.split('.')[:-1])
                                        interfaces.append({
                                            'name': 'auto',
                                            'ip': ip,
                                            'netmask': '*************',
                                            'network_base': network_base,
                                            'description': f"Interface ({ip})"
                                        })
                                except Exception as e:
                                    logging.debug(f"Unix IP-Parsing Fehler: {e}")
                        break
                except Exception as e:
                    logging.debug(f"Unix Befehl {cmd} Fehler: {e}")

        except Exception as e:
            logging.debug(f"Unix Interface-Erkennung Fehler: {e}")

        return interfaces

    def _get_default_networks(self) -> List[Dict]:
        """Gibt Standard-Netzwerk-Bereiche zurück"""
        return [
            {'name': 'auto', 'ip': 'auto', 'network_base': '169.245', 'description': '169.245.x.x (Baumer Kameras) ⭐'},
            {'name': 'auto', 'ip': 'auto', 'network_base': '172.28.145', 'description': '172.28.145.x (Ihr PC)'},
            {'name': 'auto', 'ip': 'auto', 'network_base': '192.168.1', 'description': '192.168.1.x (Standard)'},
            {'name': 'auto', 'ip': 'auto', 'network_base': '192.168.0', 'description': '192.168.0.x'},
            {'name': 'auto', 'ip': 'auto', 'network_base': '10.0.0', 'description': '10.0.0.x'},
            {'name': 'auto', 'ip': 'auto', 'network_base': '172.16.0', 'description': '172.16.0.x'}
        ]

    def get_all_adapters_info(self) -> List[Dict]:
        """Gibt alle verfügbaren Adapter zurück (auch ohne IP)"""
        all_adapters = []

        try:
            result = subprocess.run(['ipconfig', '/all'], capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                current_adapter = None
                current_data = {}

                for line in lines:
                    line_stripped = line.strip()

                    if 'adapter' in line.lower() and ':' in line:
                        # Speichere vorherigen Adapter
                        if current_adapter:
                            adapter_info = {
                                'name': current_adapter,
                                'ip': current_data.get('ip', 'Nicht konfiguriert'),
                                'netmask': current_data.get('subnet', '*************'),
                                'network_base': self._get_network_base_from_ip(current_data.get('ip')),
                                'description': f"{current_adapter} ({current_data.get('ip', 'Keine IP')})",
                                'has_ip': bool(current_data.get('ip') and self._is_valid_ip(current_data.get('ip', '')))
                            }
                            all_adapters.append(adapter_info)

                        current_adapter = line.split(':')[0].strip()
                        current_data = {}

                    elif 'IPv4' in line and current_adapter:
                        try:
                            ip_part = line.split(':')[1].strip()
                            ip = ip_part.split('(')[0].strip()
                            current_data['ip'] = ip
                        except:
                            pass

                    elif ('Subnet Mask' in line or 'Subnetzmaske' in line) and current_adapter:
                        try:
                            subnet = line.split(':')[1].strip()
                            current_data['subnet'] = subnet
                        except:
                            pass

                # Letzten Adapter hinzufügen
                if current_adapter:
                    adapter_info = {
                        'name': current_adapter,
                        'ip': current_data.get('ip', 'Nicht konfiguriert'),
                        'netmask': current_data.get('subnet', '*************'),
                        'network_base': self._get_network_base_from_ip(current_data.get('ip')),
                        'description': f"{current_adapter} ({current_data.get('ip', 'Keine IP')})",
                        'has_ip': bool(current_data.get('ip') and self._is_valid_ip(current_data.get('ip', '')))
                    }
                    all_adapters.append(adapter_info)

        except Exception as e:
            logging.error(f"Fehler beim Abrufen aller Adapter: {e}")

        return all_adapters

    def _get_network_base_from_ip(self, ip: str) -> str:
        """Berechnet Netzwerk-Bereich aus IP"""
        if not ip or not self._is_valid_ip(ip):
            return '192.168.1'  # Standard
        try:
            return '.'.join(ip.split('.')[:-1])
        except:
            return '192.168.1'

    def set_network_interface(self, interface_info: Dict):
        """Setzt die zu verwendende Netzwerkkarte"""
        self.selected_interface = interface_info
        logging.info(f"Netzwerkkarte ausgewählt: {interface_info.get('description', 'Unbekannt')}")

    def get_current_network_base(self) -> str:
        """Gibt aktuellen Netzwerk-Bereich zurück"""
        if self.selected_interface:
            return self.selected_interface.get('network_base', '192.168.1')
        elif self.available_interfaces:
            return self.available_interfaces[0].get('network_base', '192.168.1')
        else:
            return '192.168.1'

    def discover_cameras_upnp(self) -> List[Dict]:
        """UPnP-basierte Kamera-Entdeckung"""
        cameras = []
        sock = None

        try:
            logging.debug("Starte UPnP Discovery...")

            # UPnP SSDP Multicast
            ssdp_request = (
                "M-SEARCH * HTTP/1.1\r\n"
                "HOST: ***************:1900\r\n"
                "MAN: \"ssdp:discover\"\r\n"
                "ST: upnp:rootdevice\r\n"
                "MX: 3\r\n\r\n"
            )

            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(5)

            try:
                sock.sendto(ssdp_request.encode(), ('***************', 1900))
                logging.debug("UPnP Request gesendet")
            except Exception as e:
                logging.warning(f"UPnP Request senden fehlgeschlagen: {e}")
                return cameras

            response_count = 0
            while response_count < 10:  # Begrenze Anzahl Responses
                try:
                    data, addr = sock.recvfrom(1024)
                    response = data.decode('utf-8', errors='ignore')
                    response_count += 1

                    logging.debug(f"UPnP Response von {addr[0]}: {response[:100]}...")

                    # Prüfe auf Baumer-Geräte oder Kamera-Keywords
                    response_lower = response.lower()
                    if any(keyword in response_lower for keyword in ['baumer', 'vcxg', 'camera', 'imaging']):
                        camera_info = {
                            'ip': addr[0],
                            'type': 'upnp',
                            'response': response
                        }
                        cameras.append(camera_info)
                        logging.info(f"UPnP Kamera gefunden: {addr[0]}")

                except socket.timeout:
                    logging.debug("UPnP Timeout erreicht")
                    break
                except Exception as e:
                    logging.debug(f"UPnP Response Fehler: {e}")
                    break

        except Exception as e:
            logging.error(f"UPnP Discovery Fehler: {e}")
        finally:
            if sock:
                try:
                    sock.close()
                except:
                    pass

        logging.info(f"UPnP Discovery abgeschlossen: {len(cameras)} Geräte gefunden")
        return cameras
        
    def discover_cameras_ip_scan(self, network_base: str = None) -> List[Dict]:
        """IP-Bereich-Scan für Kameras mit Thread-Pool"""
        cameras = []
        import concurrent.futures
        import threading

        # Verwende ausgewählten Netzwerk-Bereich
        if network_base is None:
            network_base = self.get_current_network_base()

        logging.info(f"Starte IP-Scan für Netzwerk: {network_base}.x")
        cameras_lock = threading.Lock()

        def check_ip(ip):
            try:
                logging.debug(f"Prüfe IP: {ip}")
                # Teste HTTP-Verbindung auf typischen Kamera-Ports
                for port in [80, 8080, 554]:
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(1)
                        result = sock.connect_ex((ip, port))
                        sock.close()

                        if result == 0:
                            # Versuche HTTP-Request für Geräte-Info
                            try:
                                response = requests.get(f"http://{ip}:{port}/device_info", timeout=2)
                                if response.status_code == 200:
                                    camera_info = {
                                        'ip': ip,
                                        'port': port,
                                        'type': 'http',
                                        'info': response.text
                                    }
                                    with cameras_lock:
                                        cameras.append(camera_info)
                                    logging.info(f"HTTP Kamera gefunden: {ip}:{port}")
                                    break
                            except Exception as e:
                                logging.debug(f"HTTP-Request Fehler für {ip}:{port}: {e}")
                    except Exception as e:
                        logging.debug(f"Socket-Fehler für {ip}:{port}: {e}")

            except Exception as e:
                logging.debug(f"IP-Scan Fehler für {ip}: {e}")

        try:
            # Verwende ThreadPoolExecutor für bessere Kontrolle
            with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
                # Erstelle IP-Liste
                ip_list = [f"{network_base}.{i}" for i in range(1, 255)]

                # Starte Scan mit begrenzter Thread-Anzahl
                futures = [executor.submit(check_ip, ip) for ip in ip_list]

                # Warte auf Completion mit Timeout
                for future in concurrent.futures.as_completed(futures, timeout=30):
                    try:
                        future.result()
                    except Exception as e:
                        logging.debug(f"Thread-Fehler: {e}")

        except concurrent.futures.TimeoutError:
            logging.warning("IP-Scan Timeout erreicht")
        except Exception as e:
            logging.error(f"IP-Scan Fehler: {e}")

        logging.info(f"IP-Scan abgeschlossen: {len(cameras)} Kameras gefunden")
        return cameras
        
    def get_camera_details(self, ip: str, port: int = 80) -> Dict:
        """Holt detaillierte Kamera-Informationen"""
        details = {
            'ip': ip,
            'model': 'Unbekannt',
            'serial': 'Unbekannt',
            'temperature_support': False
        }
        
        try:
            # Versuche verschiedene API-Endpunkte
            endpoints = [
                f"http://{ip}:{port}/api/device",
                f"http://{ip}:{port}/device_info",
                f"http://{ip}:{port}/cgi-bin/device_info"
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(endpoint, timeout=3)
                    if response.status_code == 200:
                        # Parse XML oder JSON Response
                        content = response.text
                        
                        # Suche nach Modell-Info
                        if 'VCXG' in content:
                            details['model'] = 'VCXG-13M'
                            details['temperature_support'] = True
                            
                        # Suche nach Seriennummer
                        if 'serial' in content.lower():
                            # Einfache Extraktion - kann je nach API angepasst werden
                            lines = content.split('\n')
                            for line in lines:
                                if 'serial' in line.lower():
                                    details['serial'] = line.split(':')[-1].strip()
                                    break
                                    
                        logging.info(f"Kamera-Details für {ip} abgerufen")
                        break
                        
                except requests.RequestException:
                    continue
                    
        except Exception as e:
            logging.error(f"Fehler beim Abrufen der Kamera-Details für {ip}: {e}")
            
        return details
        
    def start_continuous_discovery(self, interval: int = 300):
        """DEAKTIVIERT - Keine kontinuierliche Kamera-Entdeckung mehr"""
        logging.info("Kontinuierliche Discovery DEAKTIVIERT - nur manuelle Scans")
        self.discovery_running = False
        # Keine automatischen Scans mehr!
        
    def stop_discovery(self):
        """Stoppt kontinuierliche Entdeckung"""
        self.discovery_running = False
        
    def get_discovered_cameras(self) -> List[Dict]:
        """Gibt entdeckte Kameras zurück"""
        return self.discovered_cameras.copy()
