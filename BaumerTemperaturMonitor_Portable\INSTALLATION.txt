# Installation und Deployment

## Portable Paket erstellt:
- Datum: 2025-08-21 15:18:56
- EXE-Größe: 39.9 MB
- Python-Version: Eingebettet
- PyQt5: Eingebettet

## Deployment:
1. <PERSON><PERSON><PERSON> den kompletten Ordner "BaumerTemperaturMonitor_Portable"
2. <PERSON><PERSON>iel-PC: Doppelklick auf BaumerTemperaturMonitor.exe
3. Keine weitere Installation erforderlich

## Getestet auf:
- Windows 11 (Build-System)
- Ethernet 5 Netzwerk-Adapter
- 169.254.x.x IP-Bereich

## Nächste Schritte:
1. Testen Sie die EXE auf dem Ziel-PC
2. Prüfen Sie die Kamera-Verbindungen
3. Verwen<PERSON> Sie "Alle bekannten Kameras testen"
