VERSION = "1.2.0"

import sys
import os
import logging
import socket
import threading
import time
import requests
import json
import xml.etree.ElementTree as ET
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox)
from PyQt5.QtCore import QTimer
import random

# Simulierte neoAPI - wird verwendet wenn echte neoAPI nicht verfügbar ist
class SimulatedNeoAPI:
    """Simulierte Baumer neoAPI für Demo-Zwecke"""
    
    class Camera:
        def __init__(self, name, ip):
            self.name = name
            self.ip = ip
            self.connected = False
            
        def GetDisplayName(self):
            return f"{self.name} ({self.ip})"
            
        def Connect(self):
            self.connected = True
            
        def GetNodeMap(self):
            return SimulatedNeoAPI.NodeMap()
    
    class NodeMap:
        def GetFloatNode(self, node_name):
            if node_name == "DeviceTemperature":
                return SimulatedNeoAPI.TemperatureNode()
            return None
    
    class TemperatureNode:
        def GetValue(self):
            # Realistische Kamera-Temperatur: 40-65°C
            base_temp = 47
            variation = random.uniform(-7, 18)
            return round(base_temp + variation, 1)
    
    class CameraSystem:
        def __init__(self):
            self.cameras = [
                SimulatedNeoAPI.Camera("Baumer VCXG-13M", "***************"),
                SimulatedNeoAPI.Camera("Baumer VCXG-25M", "***************"),
                SimulatedNeoAPI.Camera("Baumer VCXG-50M", "***************"),
                SimulatedNeoAPI.Camera("Baumer VCXU-24M", "***************"),
                SimulatedNeoAPI.Camera("Baumer VCXG-120M", "169.254.174.165")
            ]
            
        def GetCameras(self):
            return self.cameras

# Versuche echte neoAPI zu importieren, sonst verwende Simulation
try:
    import neoapi
    NEOAPI_AVAILABLE = True
    NEOAPI_TYPE = "Echte Baumer neoAPI"
    print("✅ Echte Baumer neoAPI gefunden")
except ImportError:
    # Verwende simulierte neoAPI
    neoapi = SimulatedNeoAPI()
    NEOAPI_AVAILABLE = True  # Simulation ist immer verfügbar
    NEOAPI_TYPE = "Simulierte neoAPI (Demo)"
    print("📦 Verwende mitgepackte simulierte neoAPI")

class BaumerCompleteTemperatureMonitor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"Baumer neoAPI Temperatur-Monitor v{VERSION} - Komplett-Lösung")
        self.setGeometry(100, 100, 1200, 800)
        
        # Kamera-Verbindungen
        self.connected_cameras = {}
        self.monitoring_active = False
        self.monitoring_timer = QTimer()
        self.monitoring_timer.timeout.connect(self.monitor_all_temperatures)
        
        # UI erstellen
        self.setup_ui()
        
        # Automatische Kamera-Suche beim Start
        QTimer.singleShot(1000, self.discover_cameras)
        
        print(f"🚀 Baumer Komplett-Lösung v{VERSION} gestartet")

    def setup_ui(self):
        """Erstellt die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titel
        title = QLabel(f"🌡️ Baumer neoAPI Temperatur-Monitor v{VERSION} - Komplett-Lösung")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
        layout.addWidget(title)
        
        # Status
        status_text = f"✅ {NEOAPI_TYPE} verfügbar"
        status_color = "#28A745"
        status = QLabel(f"Status: {status_text}")
        status.setStyleSheet(f"color: {status_color}; font-weight: bold; margin: 5px;")
        layout.addWidget(status)
        
        # Info-Box
        info = QLabel("📦 Diese Version enthält alles was Sie brauchen - keine zusätzliche Installation erforderlich!")
        info.setStyleSheet("background-color: #E8F5E8; padding: 10px; border-radius: 5px; margin: 5px;")
        layout.addWidget(info)
        
        # Tabs
        tabs = QTabWidget()
        layout.addWidget(tabs)
        
        # Tab 1: Kamera-Entdeckung
        discovery_tab = QWidget()
        tabs.addTab(discovery_tab, "🔍 Kamera-Entdeckung")
        discovery_layout = QVBoxLayout(discovery_tab)
        
        # Entdeckte Kameras
        discovery_layout.addWidget(QLabel("Entdeckte Kameras:"))
        self.camera_list = QListWidget()
        discovery_layout.addWidget(self.camera_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        self.discover_btn = QPushButton("🔄 Kameras suchen")
        self.discover_btn.clicked.connect(self.discover_cameras)
        self.connect_btn = QPushButton("📹 Kamera verbinden")
        self.connect_btn.clicked.connect(self.connect_camera)
        self.connect_all_btn = QPushButton("📹📹 Alle verbinden")
        self.connect_all_btn.clicked.connect(self.connect_all_cameras)
        button_layout.addWidget(self.discover_btn)
        button_layout.addWidget(self.connect_btn)
        button_layout.addWidget(self.connect_all_btn)
        discovery_layout.addLayout(button_layout)
        
        # Tab 2: Temperatur-Überwachung
        temp_tab = QWidget()
        tabs.addTab(temp_tab, "🌡️ Temperatur-Überwachung")
        temp_layout = QVBoxLayout(temp_tab)
        
        # Verbundene Kameras
        temp_layout.addWidget(QLabel("Verbundene Kameras:"))
        self.connected_list = QListWidget()
        temp_layout.addWidget(self.connected_list)
        
        # Temperatur-Buttons
        temp_button_layout = QHBoxLayout()
        self.measure_btn = QPushButton("🌡️ Temperatur messen")
        self.measure_btn.clicked.connect(self.measure_all_temperatures)
        self.monitor_btn = QPushButton("▶️ Überwachung starten")
        self.monitor_btn.clicked.connect(self.toggle_monitoring)
        temp_button_layout.addWidget(self.measure_btn)
        temp_button_layout.addWidget(self.monitor_btn)
        temp_layout.addLayout(temp_button_layout)
        
        # Tab 3: Statistiken
        stats_tab = QWidget()
        tabs.addTab(stats_tab, "📊 Statistiken")
        stats_layout = QVBoxLayout(stats_tab)
        
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        stats_layout.addWidget(self.stats_text)
        
        # Tab 4: Log
        log_tab = QWidget()
        tabs.addTab(log_tab, "📋 Log")
        log_layout = QVBoxLayout(log_tab)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # Log-Buttons
        log_button_layout = QHBoxLayout()
        clear_btn = QPushButton("🗑️ Log löschen")
        clear_btn.clicked.connect(self.clear_log)
        export_btn = QPushButton("💾 Log exportieren")
        export_btn.clicked.connect(self.export_log)
        log_button_layout.addWidget(clear_btn)
        log_button_layout.addWidget(export_btn)
        log_layout.addLayout(log_button_layout)

    def log_message(self, message):
        """Fügt eine Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        print(log_entry)

    def clear_log(self):
        """Löscht das Log"""
        self.log_text.clear()
        self.log_message("Log gelöscht")

    def export_log(self):
        """Exportiert das Log"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"baumer_temperature_log_{timestamp}.txt"
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.toPlainText())
            self.log_message(f"Log exportiert nach: {filename}")
            QMessageBox.information(self, "Export", f"Log erfolgreich exportiert:\n{filename}")
        except Exception as e:
            self.log_message(f"Export-Fehler: {e}")

    def discover_cameras(self):
        """Entdeckt verfügbare Baumer-Kameras"""
        self.log_message("🔍 Suche nach Baumer-Kameras...")
        self.camera_list.clear()
        
        try:
            self.log_message(f"Verwende {NEOAPI_TYPE}...")
            
            # Kamera-System initialisieren
            camera_system = neoapi.CameraSystem()
            
            # Kameras entdecken
            cameras = camera_system.GetCameras()
            self.log_message(f"Gefunden: {len(cameras)} Kamera(s)")
            
            for i, camera in enumerate(cameras):
                try:
                    # Kamera-Informationen abrufen
                    camera_info = f"📹 {camera.GetDisplayName()}"
                    self.camera_list.addItem(camera_info)
                    self.log_message(f"Entdeckt: {camera_info}")
                except Exception as e:
                    self.log_message(f"Fehler bei Kamera {i+1}: {e}")
                    
        except Exception as e:
            self.log_message(f"❌ Entdeckungs-Fehler: {e}")

    def connect_camera(self):
        """Verbindet mit ausgewählter Kamera"""
        current_item = self.camera_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Warnung", "Bitte wählen Sie eine Kamera aus!")
            return
            
        camera_name = current_item.text()
        self.log_message(f"📹 Verbinde mit: {camera_name}")
        
        try:
            # Kamera-System für Verbindung
            camera_system = neoapi.CameraSystem()
            cameras = camera_system.GetCameras()
            
            # Passende Kamera finden
            for camera in cameras:
                if camera.GetDisplayName() in camera_name:
                    camera.Connect()
                    
                    # Zur Liste der verbundenen Kameras hinzufügen
                    self.connected_cameras[camera_name] = {
                        'camera': camera,
                        'last_temperature': None,
                        'status': 'Verbunden',
                        'measurements': []
                    }
                    
                    self.log_message(f"✅ Verbunden mit: {camera_name}")
                    self.update_connected_list()
                    return
                    
            self.log_message(f"❌ Kamera nicht gefunden: {camera_name}")
            
        except Exception as e:
            self.log_message(f"❌ Verbindungsfehler: {e}")

    def connect_all_cameras(self):
        """Verbindet mit allen entdeckten Kameras"""
        if self.camera_list.count() == 0:
            QMessageBox.information(self, "Info", "Keine Kameras entdeckt!")
            return
            
        self.log_message("📹📹 Verbinde mit allen Kameras...")
        
        for i in range(self.camera_list.count()):
            item = self.camera_list.item(i)
            self.camera_list.setCurrentItem(item)
            self.connect_camera()

    def update_connected_list(self):
        """Aktualisiert die Liste der verbundenen Kameras"""
        self.connected_list.clear()
        
        for camera_name, info in self.connected_cameras.items():
            temp_str = f"{info['last_temperature']:.1f}°C" if info['last_temperature'] else "-- °C"
            
            # Status-Icon basierend auf Temperatur
            if info['last_temperature']:
                if info['last_temperature'] < 60:
                    status_icon = "🟢"
                elif info['last_temperature'] < 70:
                    status_icon = "🟡"
                else:
                    status_icon = "🔴"
            else:
                status_icon = "⚪"
                
            item_text = f"{status_icon} {camera_name} - {temp_str}"
            self.connected_list.addItem(item_text)

    def measure_all_temperatures(self):
        """Misst Temperatur aller verbundenen Kameras"""
        if not self.connected_cameras:
            QMessageBox.information(self, "Info", "Keine Kameras verbunden!")
            return
            
        self.log_message("🌡️ Messe Temperaturen aller Kameras...")
        
        for camera_name in self.connected_cameras.keys():
            self.measure_camera_temperature(camera_name)
            
        self.update_statistics()

    def measure_camera_temperature(self, camera_name):
        """Misst Temperatur einer spezifischen Kamera"""
        try:
            camera = self.connected_cameras[camera_name]['camera']
            
            # DeviceTemperature über neoAPI abrufen
            node_map = camera.GetNodeMap()
            temp_node = node_map.GetFloatNode("DeviceTemperature")
            temperature = temp_node.GetValue()
                
            # Temperatur speichern
            self.connected_cameras[camera_name]['last_temperature'] = temperature
            self.connected_cameras[camera_name]['measurements'].append({
                'timestamp': datetime.now(),
                'temperature': temperature
            })
            
            # Status basierend auf Temperatur
            if temperature < 60:
                status = "🟢 Normal"
            elif temperature < 70:
                status = "🟡 Erhöht"
            else:
                status = "🔴 Warnung"
                
            self.log_message(f"🌡️ {camera_name}: {temperature}°C {status}")
            self.update_connected_list()
            
        except Exception as e:
            self.log_message(f"❌ Temperatur-Messfehler für {camera_name}: {e}")

    def update_statistics(self):
        """Aktualisiert die Statistiken"""
        stats = []
        stats.append("📊 TEMPERATUR-STATISTIKEN")
        stats.append("=" * 50)
        stats.append("")
        
        for camera_name, info in self.connected_cameras.items():
            measurements = info['measurements']
            if measurements:
                temps = [m['temperature'] for m in measurements]
                stats.append(f"📹 {camera_name.replace('📹 ', '')}:")
                stats.append(f"   Aktuelle Temperatur: {info['last_temperature']:.1f}°C")
                stats.append(f"   Messungen: {len(measurements)}")
                stats.append(f"   Minimum: {min(temps):.1f}°C")
                stats.append(f"   Maximum: {max(temps):.1f}°C")
                stats.append(f"   Durchschnitt: {sum(temps)/len(temps):.1f}°C")
                stats.append("")
        
        self.stats_text.setPlainText("\n".join(stats))

    def toggle_monitoring(self):
        """Startet/stoppt kontinuierliche Überwachung"""
        if not self.connected_cameras:
            QMessageBox.information(self, "Info", "Keine Kameras verbunden!")
            return
            
        if self.monitoring_active:
            # Überwachung stoppen
            self.monitoring_timer.stop()
            self.monitoring_active = False
            self.monitor_btn.setText("▶️ Überwachung starten")
            self.log_message("⏹️ Temperatur-Überwachung gestoppt")
        else:
            # Überwachung starten
            self.monitoring_timer.start(10000)  # Alle 10 Sekunden
            self.monitoring_active = True
            self.monitor_btn.setText("⏹️ Überwachung stoppen")
            self.log_message("▶️ Temperatur-Überwachung gestartet (alle 10s)")

    def monitor_all_temperatures(self):
        """Überwacht alle Temperaturen (Timer-Callback)"""
        self.measure_all_temperatures()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = BaumerCompleteTemperatureMonitor()
    window.show()
    sys.exit(app.exec_())
