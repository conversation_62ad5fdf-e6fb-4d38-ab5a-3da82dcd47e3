<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Member List</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00799.html">ConverterSettings</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">neoapi.ConverterSettings Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="a00799.html">neoapi.ConverterSettings</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="a00799.html#aae1871c57f0fbd241612a73b6a59761a">__init__</a>(self, *args)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>Demosaicing_Baumer5x5</b> (defined in <a class="el" href="a00799.html">neoapi.ConverterSettings</a>)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>Demosaicing_Bilinear3x3</b> (defined in <a class="el" href="a00799.html">neoapi.ConverterSettings</a>)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>Demosaicing_NearestNeighbor</b> (defined in <a class="el" href="a00799.html">neoapi.ConverterSettings</a>)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00799.html#a9b1fbaabcc5bfcc439fa3b2cbe520cd5">GetColorTransformationMatrix</a>(self)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00799.html#a8ecace113028dfb9de118d290c6ab773">GetDebayerFormat</a>(self)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00799.html#a9ff45bc0a71618600af8e20a0f6326cb">GetDemosaicingMethod</a>(self)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00799.html#a9a68d9109987c1181043cbd79a9e9685">GetSharpeningFactor</a>(self)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00799.html#afa1f29c2f30deed9d9bc001bbcf48187">GetSharpeningMode</a>(self)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00799.html#a1f9272dffefcf945aa1be073803e45c9">GetSharpeningSensitivityThreshold</a>(self)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00799.html#a3712de0c8e33df788d575470c67fdcc2">SetColorTransformationMatrix</a>(self, &quot;ColorMatrix&quot; matrix)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00799.html#a2a55273091ee46cfb3a92178007acba9">SetDebayerFormat</a>(self, &quot;str&quot; format)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00799.html#a65ae9893b226ed4e6c44673f0eeed82b">SetDemosaicingMethod</a>(self, &quot;int&quot; method)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00799.html#ab3ab541c2c53e4e4b3a5fb5e64ec222d">SetSharpeningFactor</a>(self, &quot;int&quot; factor)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00799.html#a0357a33e15928d69aeaaba69f0051184">SetSharpeningMode</a>(self, &quot;int&quot; mode)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00799.html#a392cb2ffe95118e605b5cc076a1802f3">SetSharpeningSensitivityThreshold</a>(self, &quot;int&quot; threshold)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>Sharpening_ActiveNoiseReduction</b> (defined in <a class="el" href="a00799.html">neoapi.ConverterSettings</a>)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>Sharpening_Adaptive</b> (defined in <a class="el" href="a00799.html">neoapi.ConverterSettings</a>)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>Sharpening_Global</b> (defined in <a class="el" href="a00799.html">neoapi.ConverterSettings</a>)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>Sharpening_Off</b> (defined in <a class="el" href="a00799.html">neoapi.ConverterSettings</a>)</td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00799.html#a23439d8bb5e3d744ef10cd429a4d5386">thisown</a></td><td class="entry"><a class="el" href="a00799.html">neoapi.ConverterSettings</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
