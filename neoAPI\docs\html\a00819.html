<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.FeatureListIterator Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00819.html">FeatureListIterator</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00816.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.FeatureListIterator Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00088.html">Supporting Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides iterator functionality for the <a class="el" href="a00815.html" title="Provides list functionality for camera features.">FeatureList</a>.  
 <a href="a00819.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.FeatureListIterator:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00819.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a93a9275cdf674ece85043f758d9c1100"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00819.html#a93a9275cdf674ece85043f758d9c1100">__init__</a> (self, *args)</td></tr>
<tr class="separator:a93a9275cdf674ece85043f758d9c1100"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada4aa9282e6cb593cd85d62e68d56e33"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00819.html#ada4aa9282e6cb593cd85d62e68d56e33">__eq__</a> (self, *args)</td></tr>
<tr class="memdesc:ada4aa9282e6cb593cd85d62e68d56e33"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the "==" operator.  <a href="#ada4aa9282e6cb593cd85d62e68d56e33">More...</a><br /></td></tr>
<tr class="separator:ada4aa9282e6cb593cd85d62e68d56e33"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a48e0807e720e9e3b648af61c6734e4c8"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00819.html#a48e0807e720e9e3b648af61c6734e4c8">__ne__</a> (self, *args)</td></tr>
<tr class="memdesc:a48e0807e720e9e3b648af61c6734e4c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the "=!" operator.  <a href="#a48e0807e720e9e3b648af61c6734e4c8">More...</a><br /></td></tr>
<tr class="separator:a48e0807e720e9e3b648af61c6734e4c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a974e07e9950e6760ed88fb5da8b3ae22"><td class="memItemLeft" align="right" valign="top">&quot;Feature&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00819.html#a974e07e9950e6760ed88fb5da8b3ae22">__ref__</a> (self)</td></tr>
<tr class="memdesc:a974e07e9950e6760ed88fb5da8b3ae22"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the reference.  <a href="#a974e07e9950e6760ed88fb5da8b3ae22">More...</a><br /></td></tr>
<tr class="separator:a974e07e9950e6760ed88fb5da8b3ae22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0624f6a96f4ab8a5446dfef0e7aef325"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00819.html#a0624f6a96f4ab8a5446dfef0e7aef325">GetName</a> (self)</td></tr>
<tr class="memdesc:a0624f6a96f4ab8a5446dfef0e7aef325"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the name of the current <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a>.  <a href="#a0624f6a96f4ab8a5446dfef0e7aef325">More...</a><br /></td></tr>
<tr class="separator:a0624f6a96f4ab8a5446dfef0e7aef325"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:ab69f38e9151262e4e5c502c71c1f0785"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00819.html#ab69f38e9151262e4e5c502c71c1f0785">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:ab69f38e9151262e4e5c502c71c1f0785"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#ab69f38e9151262e4e5c502c71c1f0785">More...</a><br /></td></tr>
<tr class="separator:ab69f38e9151262e4e5c502c71c1f0785"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Provides iterator functionality for the <a class="el" href="a00815.html" title="Provides list functionality for camera features.">FeatureList</a>. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a93a9275cdf674ece85043f758d9c1100"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a93a9275cdf674ece85043f758d9c1100">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.FeatureListIterator.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">   \brief    Constructor
</pre> <dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>object</b> (optional) A <a class="el" href="a00819.html" title="Provides iterator functionality for the FeatureList.">FeatureListIterator</a> object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ada4aa9282e6cb593cd85d62e68d56e33"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada4aa9282e6cb593cd85d62e68d56e33">&#9670;&nbsp;</a></span>__eq__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.FeatureListIterator.__eq__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Provides the "==" operator. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">args</td><td>object A <a class="el" href="a00819.html" title="Provides iterator functionality for the FeatureList.">FeatureListIterator</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="a48e0807e720e9e3b648af61c6734e4c8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a48e0807e720e9e3b648af61c6734e4c8">&#9670;&nbsp;</a></span>__ne__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.FeatureListIterator.__ne__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Provides the "=!" operator. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">args</td><td>object A <a class="el" href="a00819.html" title="Provides iterator functionality for the FeatureList.">FeatureListIterator</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>bool </dd></dl>

</div>
</div>
<a id="a974e07e9950e6760ed88fb5da8b3ae22"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a974e07e9950e6760ed88fb5da8b3ae22">&#9670;&nbsp;</a></span>__ref__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;Feature&quot; neoapi.FeatureListIterator.__ref__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Provides the reference. </p>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>

</div>
</div>
<a id="a0624f6a96f4ab8a5446dfef0e7aef325"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0624f6a96f4ab8a5446dfef0e7aef325">&#9670;&nbsp;</a></span>GetName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.FeatureListIterator.GetName </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the name of the current <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a>. </p>
<dl class="section return"><dt>Returns</dt><dd>The name as a NeoString </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="ab69f38e9151262e4e5c502c71c1f0785"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab69f38e9151262e4e5c502c71c1f0785">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.FeatureListIterator.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
