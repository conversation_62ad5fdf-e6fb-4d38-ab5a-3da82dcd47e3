<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented namespace members with links to the namespaces they belong to:</div>

<h3><a id="index_b"></a>- b -</h3><ul>
<li>BalanceWhiteAuto_Continuous
: <a class="el" href="a00091.html#ab71b44433b2af6862f033500ae066a9b">neoapi</a>
</li>
<li>BalanceWhiteAuto_Off
: <a class="el" href="a00091.html#a80bc520f2a0de9178168f1c22a3a821e">neoapi</a>
</li>
<li>BalanceWhiteAuto_Once
: <a class="el" href="a00091.html#a9586964e7a42df50d067dac56cc75ba9">neoapi</a>
</li>
<li>BalanceWhiteAutoStatus_ColorGainsTooHigh
: <a class="el" href="a00091.html#a5da7585b82a1e268c86f517956bf32d0">neoapi</a>
</li>
<li>BalanceWhiteAutoStatus_Initial
: <a class="el" href="a00091.html#a9aec30ac30407bb794f7a892c6b6e8b3">neoapi</a>
</li>
<li>BalanceWhiteAutoStatus_Start
: <a class="el" href="a00091.html#a8d139d857584629a3c4a9005b7d0b49b">neoapi</a>
</li>
<li>BalanceWhiteAutoStatus_Success
: <a class="el" href="a00091.html#ac79bd51c771e9266f3973b174109f929">neoapi</a>
</li>
<li>BalanceWhiteAutoStatus_Underrun
: <a class="el" href="a00091.html#ae208e4807d61f1447b28a20d4331a5f5">neoapi</a>
</li>
<li>Baudrate_Baud115200
: <a class="el" href="a00091.html#ae8e8315356785cccd08af004a72624c2">neoapi</a>
</li>
<li>Baudrate_Baud19200
: <a class="el" href="a00091.html#affec69b9c9be4140a1e074e0df48e524">neoapi</a>
</li>
<li>Baudrate_Baud230400
: <a class="el" href="a00091.html#a75811bd70b7c5093fa34a5802aaff747">neoapi</a>
</li>
<li>Baudrate_Baud38400
: <a class="el" href="a00091.html#a70fc71dff13d9121d8edde2d849d904e">neoapi</a>
</li>
<li>Baudrate_Baud460800
: <a class="el" href="a00091.html#af22431628944f2393eeda74cfd896b1a">neoapi</a>
</li>
<li>Baudrate_Baud57600
: <a class="el" href="a00091.html#af576063c85b86fd02b41ffd3dbe592d1">neoapi</a>
</li>
<li>Baudrate_Baud9600
: <a class="el" href="a00091.html#af1361d8260d731c12bd69c013a562417">neoapi</a>
</li>
<li>Baudrate_RS232Off
: <a class="el" href="a00091.html#aea05c0ea60f49e3c9fed8d64b7d4f1ba">neoapi</a>
</li>
<li>BinningHorizontalMode_Average
: <a class="el" href="a00091.html#af42f5017c4d54c9fb84d3fcfe3895bc7">neoapi</a>
</li>
<li>BinningHorizontalMode_Sum
: <a class="el" href="a00091.html#ad9d8dae55386b0f6feaced975dbd501d">neoapi</a>
</li>
<li>BinningSelector_Region0
: <a class="el" href="a00091.html#a1c23bc743c9cf67b439ab76d71c1f574">neoapi</a>
</li>
<li>BinningSelector_Region1
: <a class="el" href="a00091.html#a2296861418c21b6cc0dac7b24747cf2e">neoapi</a>
</li>
<li>BinningSelector_Region2
: <a class="el" href="a00091.html#a8ace90f589c2f46fc0209c59909c8a3e">neoapi</a>
</li>
<li>BinningSelector_Sensor
: <a class="el" href="a00091.html#acf05250883e901bb1010de128378dfd8">neoapi</a>
</li>
<li>BinningVerticalMode_Average
: <a class="el" href="a00091.html#a9e126c6d91eb5eb00414840a786ac243">neoapi</a>
</li>
<li>BinningVerticalMode_Sum
: <a class="el" href="a00091.html#ad93ef2b73cec25d400cfc3d7f23de2a3">neoapi</a>
</li>
<li>BlackLevelSelector_All
: <a class="el" href="a00091.html#a1333cd961b04455403125824a3936626">neoapi</a>
</li>
<li>BlackLevelSelector_Blue
: <a class="el" href="a00091.html#a4c6ebbed0bedf2bc592b8eb8549a2c01">neoapi</a>
</li>
<li>BlackLevelSelector_Green
: <a class="el" href="a00091.html#a45bbd4cd9c85dc2f8d42585100d3f481">neoapi</a>
</li>
<li>BlackLevelSelector_Red
: <a class="el" href="a00091.html#ad2cc6194429ef69c51c3700072146db8">neoapi</a>
</li>
<li>BlackLevelSelector_Tap1
: <a class="el" href="a00091.html#abb17fbe09fc9aa17461771158c58ff04">neoapi</a>
</li>
<li>BlackLevelSelector_Tap2
: <a class="el" href="a00091.html#a3941a0b048f5078f3f482a268bf87a29">neoapi</a>
</li>
<li>BlackLevelSelector_U
: <a class="el" href="a00091.html#a0bd59bc57b5d1300f0355141ca38d451">neoapi</a>
</li>
<li>BlackLevelSelector_V
: <a class="el" href="a00091.html#a67f871a0aac2659cfc589358acac3718">neoapi</a>
</li>
<li>BlackLevelSelector_Y
: <a class="el" href="a00091.html#a3f5e2edcdd61f369d9a8148efe2792e1">neoapi</a>
</li>
<li>BlackSunSuppression_Default
: <a class="el" href="a00091.html#acf28985878197bdfe52c8034b4172839">neoapi</a>
</li>
<li>BlackSunSuppression_High
: <a class="el" href="a00091.html#a376ff5c8802ae86646a7d004449bdb54">neoapi</a>
</li>
<li>BlackSunSuppression_Low
: <a class="el" href="a00091.html#a81d3f8e46ff333145ea5bbf7fa901b9b">neoapi</a>
</li>
<li>BlackSunSuppression_Max
: <a class="el" href="a00091.html#aa6b29fdab560be7f81fb6851fa9e506e">neoapi</a>
</li>
<li>BlackSunSuppression_Off
: <a class="el" href="a00091.html#ad27b645e436a0dd7d2a5cb50b32e76b0">neoapi</a>
</li>
<li>boCalibrationDataConfigurationMode_Off
: <a class="el" href="a00091.html#aac29809d054082c9aa463c4d1d7d67b9">neoapi</a>
</li>
<li>boCalibrationDataConfigurationMode_On
: <a class="el" href="a00091.html#a331a4742cd3c023f7eaea47076b47676">neoapi</a>
</li>
<li>boCalibrationMatrixSelector_CameraMatrix
: <a class="el" href="a00091.html#aefc44ffd92256608b0014d0a9e00d4a9">neoapi</a>
</li>
<li>boCalibrationMatrixSelector_NewCameraMatrix
: <a class="el" href="a00091.html#a1f566ca6847b18be67db2058b3109d50">neoapi</a>
</li>
<li>boCalibrationMatrixValueSelector_Value11
: <a class="el" href="a00091.html#a3577b5fefbcf8490f908a8f988d129de">neoapi</a>
</li>
<li>boCalibrationMatrixValueSelector_Value12
: <a class="el" href="a00091.html#a9c3b32de9dd370fe87989902bbb5fc5c">neoapi</a>
</li>
<li>boCalibrationMatrixValueSelector_Value13
: <a class="el" href="a00091.html#af31956ffdf73eb69d199c82538bca4cb">neoapi</a>
</li>
<li>boCalibrationMatrixValueSelector_Value21
: <a class="el" href="a00091.html#a10984ff170e4b02c64e6ef1f68a508c9">neoapi</a>
</li>
<li>boCalibrationMatrixValueSelector_Value22
: <a class="el" href="a00091.html#a6328b2fa753dc19af7e64c5a7d943bdb">neoapi</a>
</li>
<li>boCalibrationMatrixValueSelector_Value23
: <a class="el" href="a00091.html#aff19e5d9d0025335ac86864900157cfb">neoapi</a>
</li>
<li>boCalibrationMatrixValueSelector_Value31
: <a class="el" href="a00091.html#a7d0d22fc79e5336aefb0547521e1ca02">neoapi</a>
</li>
<li>boCalibrationMatrixValueSelector_Value32
: <a class="el" href="a00091.html#a685cce05e1baa759b6318f26c4f74d87">neoapi</a>
</li>
<li>boCalibrationMatrixValueSelector_Value33
: <a class="el" href="a00091.html#ad425f294bd3ceb762cebd61addbd2fc8">neoapi</a>
</li>
<li>boCalibrationVectorSelector_rvec
: <a class="el" href="a00091.html#ab84effcb40f9674b21f4af00d0d51471">neoapi</a>
</li>
<li>boCalibrationVectorSelector_tvec
: <a class="el" href="a00091.html#a31dd07f6cc1eb2c30ebf607fbfcbd3a4">neoapi</a>
</li>
<li>boCalibrationVectorValueSelector_Value1
: <a class="el" href="a00091.html#a63c766647cacdd831d8cd1a97e27ca45">neoapi</a>
</li>
<li>boCalibrationVectorValueSelector_Value2
: <a class="el" href="a00091.html#aad8522dcb43664d7f41868949cc9b5cf">neoapi</a>
</li>
<li>boCalibrationVectorValueSelector_Value3
: <a class="el" href="a00091.html#a9443f911f9e2c16d1ca979f0b884b5db">neoapi</a>
</li>
<li>boGeometryDistortionValueSelector_k1
: <a class="el" href="a00091.html#a9695f9d1b8bb875b40cd5ab35da225c2">neoapi</a>
</li>
<li>boGeometryDistortionValueSelector_k2
: <a class="el" href="a00091.html#af8fc8b6ce1c2d84fce4bb6e10a4be346">neoapi</a>
</li>
<li>boGeometryDistortionValueSelector_k3
: <a class="el" href="a00091.html#a1e312bc5c1c1c685415da91ef4aa598b">neoapi</a>
</li>
<li>boGeometryDistortionValueSelector_p1
: <a class="el" href="a00091.html#a1add3f565f7729c5bc029279e7518d04">neoapi</a>
</li>
<li>boGeometryDistortionValueSelector_p2
: <a class="el" href="a00091.html#ae246baad9a02e4d5ffcd8c4e8a53d51a">neoapi</a>
</li>
<li>BOPFShift_Bits0To7
: <a class="el" href="a00091.html#ae4cd06971acdb72af9e3633b53c7c8d5">neoapi</a>
</li>
<li>BOPFShift_Bits1To8
: <a class="el" href="a00091.html#a0e4139226d083582d9955469356f16f7">neoapi</a>
</li>
<li>BOPFShift_Bits2To9
: <a class="el" href="a00091.html#a7998836301d024710fbd75a0fc4b6cd9">neoapi</a>
</li>
<li>BOPFShift_Bits3To10
: <a class="el" href="a00091.html#a5a43d81ea6c643b5c9945e805ab71230">neoapi</a>
</li>
<li>BOPFShift_Bits4To11
: <a class="el" href="a00091.html#a6e66ca3a0d5aeebf7bdb35b6afc1485f">neoapi</a>
</li>
<li>BoSequencerEnable_Off
: <a class="el" href="a00091.html#a7da92c6d9b72a6eab99ef832d94e6d37">neoapi</a>
</li>
<li>BoSequencerEnable_On
: <a class="el" href="a00091.html#a3b85ad5b75100559aff0f10fae2db2bf">neoapi</a>
</li>
<li>BoSequencerIOSelector_SequencerOutput0
: <a class="el" href="a00091.html#a75d3f944ab2a23c813dda7ad3fd9b8f8">neoapi</a>
</li>
<li>BoSequencerIOSelector_SequencerOutput1
: <a class="el" href="a00091.html#afaf6a450ebe0abf0d424f3d55d1ea4b6">neoapi</a>
</li>
<li>BoSequencerIOSelector_SequencerOutput2
: <a class="el" href="a00091.html#acd72af336a3866dc70f8af9d785a677b">neoapi</a>
</li>
<li>BoSequencerMode_FreeRunning
: <a class="el" href="a00091.html#a22bbfb53ca1d82d02121bc362138a8f6">neoapi</a>
</li>
<li>BoSequencerMode_FreeRunningInitTrigger
: <a class="el" href="a00091.html#a5ea2b8f7bd3ff023d9aaa93643d1ec20">neoapi</a>
</li>
<li>BoSequencerMode_FreeRunningInitTriggerOnce
: <a class="el" href="a00091.html#ab3e7d2d2bb7a4bccb3ed4c2b2280f395">neoapi</a>
</li>
<li>BoSequencerMode_FreeRunningOnce
: <a class="el" href="a00091.html#ad3f02c169b5c81b9c4c230b684d1318c">neoapi</a>
</li>
<li>BoSequencerMode_SingleStepTrigger
: <a class="el" href="a00091.html#a482d9a3dde14568a028d26ff25e80e1d">neoapi</a>
</li>
<li>BoSequencerMode_SingleStepTriggerOnce
: <a class="el" href="a00091.html#a0a11100c5e48d7eb89316df78d4388b2">neoapi</a>
</li>
<li>BoSequencerSensorDigitizationTaps_Four
: <a class="el" href="a00091.html#a4bc6a355c2a9764e9816b7c449589598">neoapi</a>
</li>
<li>BoSequencerSensorDigitizationTaps_One
: <a class="el" href="a00091.html#ab5caf99f080fb4ac5279d9b627359fb3">neoapi</a>
</li>
<li>BoSequencerSensorDigitizationTaps_OneAndThree
: <a class="el" href="a00091.html#a676b76b58b49df855530c08e0426ac04">neoapi</a>
</li>
<li>BoSequencerSensorDigitizationTaps_OneAndTwo
: <a class="el" href="a00091.html#a54614a3a0c98d6b6fd130b395d3fbfab">neoapi</a>
</li>
<li>BoSequencerStart_Off
: <a class="el" href="a00091.html#a28d6176b9cd466772472c56ab181c621">neoapi</a>
</li>
<li>BoSequencerStart_On
: <a class="el" href="a00091.html#a4cbe04f2acc95ef6a147cd7ad2763d09">neoapi</a>
</li>
<li>boSerialConfigBaudRate_Baudrate115200Hz
: <a class="el" href="a00091.html#a77d06896f56179f6de0679006446e28f">neoapi</a>
</li>
<li>boSerialConfigBaudRate_Baudrate1843200Hz
: <a class="el" href="a00091.html#afb81bbb7d84f0927579602e677ab6791">neoapi</a>
</li>
<li>boSerialConfigBaudRate_Baudrate19200Hz
: <a class="el" href="a00091.html#a1e650271093d9837bc4f8f74411c7c68">neoapi</a>
</li>
<li>boSerialConfigBaudRate_Baudrate230400Hz
: <a class="el" href="a00091.html#a573a101efe076ae8d808aabc2e1518bc">neoapi</a>
</li>
<li>boSerialConfigBaudRate_Baudrate38400Hz
: <a class="el" href="a00091.html#a07e8baf18b1e1c8d11e0bd1396457b29">neoapi</a>
</li>
<li>boSerialConfigBaudRate_Baudrate460800Hz
: <a class="el" href="a00091.html#adaa091c2f09e83325d630728c8652809">neoapi</a>
</li>
<li>boSerialConfigBaudRate_Baudrate57600Hz
: <a class="el" href="a00091.html#a4c0c471b87cb33872ec47e1c3f28b92b">neoapi</a>
</li>
<li>boSerialConfigBaudRate_Baudrate921600Hz
: <a class="el" href="a00091.html#ad9b1652f5ced378075e634bac52e12ec">neoapi</a>
</li>
<li>boSerialConfigBaudRate_Baudrate9600Hz
: <a class="el" href="a00091.html#a5441e82a54de9e818b2b4e4c779bd266">neoapi</a>
</li>
<li>boSerialConfigDataBits_Eight
: <a class="el" href="a00091.html#afd95ab72bc0b9b2174efd249e80fb23d">neoapi</a>
</li>
<li>boSerialConfigDataBits_Five
: <a class="el" href="a00091.html#a008bd5314d0f53a09858f15d026dbefd">neoapi</a>
</li>
<li>boSerialConfigDataBits_Seven
: <a class="el" href="a00091.html#a966f834fe36487328f98126c223ab66e">neoapi</a>
</li>
<li>boSerialConfigDataBits_Six
: <a class="el" href="a00091.html#a2d1d8d69cba6ddbcf4d5d983531906da">neoapi</a>
</li>
<li>boSerialConfigParity_Even
: <a class="el" href="a00091.html#aff8037b32213ea5051c963888046e50b">neoapi</a>
</li>
<li>boSerialConfigParity_Mark
: <a class="el" href="a00091.html#a00ce5b703c3f45c57fe54a9df7729094">neoapi</a>
</li>
<li>boSerialConfigParity_None
: <a class="el" href="a00091.html#a422abebd4e455c4810d0e151d7f8748d">neoapi</a>
</li>
<li>boSerialConfigParity_Odd
: <a class="el" href="a00091.html#ab0480ea941996f40c5567cc534290adb">neoapi</a>
</li>
<li>boSerialConfigParity_Space
: <a class="el" href="a00091.html#adccf537a651aacbedaff1a85d6c1496a">neoapi</a>
</li>
<li>boSerialConfigStopBits_One
: <a class="el" href="a00091.html#a0cf87ae70a41d34fa09080f340c57b3f">neoapi</a>
</li>
<li>boSerialConfigStopBits_OnePtFive
: <a class="el" href="a00091.html#a6ae68f7b6772fa8c21d6055903e5322f">neoapi</a>
</li>
<li>boSerialConfigStopBits_Two
: <a class="el" href="a00091.html#afb4e12c688f4c2c6d494b4d31532e8b6">neoapi</a>
</li>
<li>boSerialMode_boSerialControl
: <a class="el" href="a00091.html#a4e8fa196dc9402cd9a1fd9def33ac00e">neoapi</a>
</li>
<li>boSerialMode_Bypass
: <a class="el" href="a00091.html#afa4c68c30cd2d1d9094941eb334fbacc">neoapi</a>
</li>
<li>boSerialMode_Off
: <a class="el" href="a00091.html#a6c1de0d41d88f733ffd21946c902bc96">neoapi</a>
</li>
<li>boSerialMode_OpticControl
: <a class="el" href="a00091.html#ab48f43a6c18dc859f351b3ee57199d84">neoapi</a>
</li>
<li>boSerialSelector_UART0
: <a class="el" href="a00091.html#a29ce20cc73ccdcef7fc38fdf6ded9509">neoapi</a>
</li>
<li>boSerialSelector_UART1
: <a class="el" href="a00091.html#a40e9ada9f72a23a8c7eff646c900fbea">neoapi</a>
</li>
<li>BrightnessAutoPriority_ExposureAuto
: <a class="el" href="a00091.html#ab0ab01a40f0adf26f8776256a4de4617">neoapi</a>
</li>
<li>BrightnessAutoPriority_GainAuto
: <a class="el" href="a00091.html#a8eae1c2dad89b898260dc76203c15007">neoapi</a>
</li>
<li>BrightnessCorrection_Off
: <a class="el" href="a00091.html#a04e9df10a54a1668d161d1250fb5e983">neoapi</a>
</li>
<li>BrightnessCorrection_On
: <a class="el" href="a00091.html#a314f283d457096a9404e0ec3674b7217">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
