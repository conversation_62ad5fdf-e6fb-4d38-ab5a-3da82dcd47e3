<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: NeoAPI Changelog</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">NeoAPI Changelog </div>  </div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#autotoc_md0">NeoAPI - ChangeLog</a><ul><li class="level2"><a href="#autotoc_md1">Version 1.5</a><ul><li class="level3"><a href="#autotoc_md2">API changes - common</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md3">Version 1.4.2</a><ul><li class="level3"><a href="#autotoc_md4">Fixes - common</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md5">Version 1.4.1</a><ul><li class="level3"><a href="#autotoc_md6">API changes - common</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md7">Version 1.4</a><ul><li class="level3"><a href="#autotoc_md8">API changes - common</a></li>
<li class="level3"><a href="#autotoc_md9">API - changes python specific</a></li>
<li class="level3"><a href="#autotoc_md10">Fixes - common</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md11">Version 1.3</a><ul><li class="level3"><a href="#autotoc_md12">API changes - common</a></li>
<li class="level3"><a href="#autotoc_md13">API - changes python specific</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md14">Version 1.2.1</a><ul><li class="level3"><a href="#autotoc_md15">API changes - common</a></li>
<li class="level3"><a href="#autotoc_md16">API - changes python specific</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md17">Version 1.2</a><ul><li class="level3"><a href="#autotoc_md18">API changes - common</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md19">Version 1.1.1</a><ul><li class="level3"><a href="#autotoc_md20">API - changes python specific</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md21">Version 1.1</a><ul><li class="level3"><a href="#autotoc_md22">API changes - common</a></li>
<li class="level3"><a href="#autotoc_md23">API - changes C++ specific</a></li>
<li class="level3"><a href="#autotoc_md24">API - changes C# specific</a></li>
<li class="level3"><a href="#autotoc_md25">API - changes python specific</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><h1><a class="anchor" id="autotoc_md0"></a>
NeoAPI - ChangeLog</h1>
<h2><a class="anchor" id="autotoc_md1"></a>
Version 1.5</h2>
<h3><a class="anchor" id="autotoc_md2"></a>
API changes - common</h3>
<ul>
<li>added support for multiple images per buffer via GenDC segmented image buffer (e.g. multi ROI applications)</li>
<li>new transport layer to support RDMA cameras</li>
<li>moved image information of class Image in a the new base class ImageInfo</li>
<li>class FeatureAccess extended with new features</li>
</ul>
<h2><a class="anchor" id="autotoc_md3"></a>
Version 1.4.2</h2>
<h3><a class="anchor" id="autotoc_md4"></a>
Fixes - common</h3>
<ul>
<li>fix python interpreter requirement for python wheels</li>
</ul>
<h2><a class="anchor" id="autotoc_md5"></a>
Version 1.4.1</h2>
<h3><a class="anchor" id="autotoc_md6"></a>
API changes - common</h3>
<ul>
<li>update of BGAPI to 2.13.4 (includes several bug fixes)</li>
</ul>
<h2><a class="anchor" id="autotoc_md7"></a>
Version 1.4</h2>
<h3><a class="anchor" id="autotoc_md8"></a>
API changes - common</h3>
<ul>
<li>added support for Rivermax driver model (new BGAPI Version 2.13)</li>
<li>fixed an issuse where PnP Events where not working as expected, when using PnP callback / PnP event handler</li>
<li>python 3.11 and 3.12 support</li>
</ul>
<h3><a class="anchor" id="autotoc_md9"></a>
API - changes python specific</h3>
<ul>
<li>returned object of image.GetImageData() gets a reference to the image, this also affects image.GetNPArray()</li>
</ul>
<h3><a class="anchor" id="autotoc_md10"></a>
Fixes - common</h3>
<ul>
<li>GetImage fixed in connection of Pnp DeviceRemoved</li>
<li>Locks in IsOnline, GetOfflineCount fixed</li>
<li>dead lock in AddBuffersToStream fixed</li>
</ul>
<h2><a class="anchor" id="autotoc_md11"></a>
Version 1.3</h2>
<h3><a class="anchor" id="autotoc_md12"></a>
API changes - common</h3>
<ul>
<li>add support for user buffers</li>
<li>Cam.EnableChunk() and Cam.DisableChunk() do not throw an exception if camera did not support chunk mode</li>
</ul>
<h3><a class="anchor" id="autotoc_md13"></a>
API - changes python specific</h3>
<ul>
<li>Image.GetImageData() return now a writeable ctypes array object instead of a bytearray copy</li>
<li>removed Image.GetMemoryView()</li>
<li>fixed a crash when acessing a neoapi object created during a chained instruction</li>
</ul>
<h2><a class="anchor" id="autotoc_md14"></a>
Version 1.2.1</h2>
<h3><a class="anchor" id="autotoc_md15"></a>
API changes - common</h3>
<ul>
<li>update of BGAPI to 2.12.3 (includes several bug fixes)</li>
<li>fixed a memory leak in Image::Save</li>
</ul>
<h3><a class="anchor" id="autotoc_md16"></a>
API - changes python specific</h3>
<ul>
<li>fixed trace for Linux (was not working in BGAPI 2.12.0)</li>
</ul>
<h2><a class="anchor" id="autotoc_md17"></a>
Version 1.2</h2>
<h3><a class="anchor" id="autotoc_md18"></a>
API changes - common</h3>
<ul>
<li>interface for dynamic addition and removal of cameras (Plug'n Play) extended to Camera Info List</li>
<li>added IsConnectable to Camera Infos to get a hint if a connect is possible</li>
<li>added Disconnect to release the connection to a camera</li>
<li>added StopStreaming, StartStreaming and IsStreaming to control the image acquisiton of the camera</li>
<li>removed GetExtension() because of its bad performance and litte use</li>
<li>added support for jpeg compressed camera images</li>
<li>added ConverterSettings to use Sharpening and ColorTransformation for images</li>
</ul>
<h2><a class="anchor" id="autotoc_md19"></a>
Version 1.1.1</h2>
<h3><a class="anchor" id="autotoc_md20"></a>
API - changes python specific</h3>
<ul>
<li>fixed a crash with GetNPArray in combination with threads</li>
</ul>
<h2><a class="anchor" id="autotoc_md21"></a>
Version 1.1</h2>
<h3><a class="anchor" id="autotoc_md22"></a>
API changes - common</h3>
<ul>
<li>interface for camera information introduced</li>
<li>interface for dynamic addition and removal of cameras (Plug'n Play) introduced</li>
<li>interface for camera events introduced, e.g. ExposureStart event</li>
<li>trace interface introduced</li>
<li>callback mechanism introduced for asynchronously signaled images, camera events, Plug'n Play events and trace logs</li>
<li>more possibilities of image handling (ClearImages, GetImageBufferCount)</li>
<li>chunk support extended</li>
<li>better possibility to monitor the camera connection state (IsOnline, GetOfflineCount)</li>
<li>possibilities of feature access extended (SFNC version 2.6)</li>
</ul>
<h3><a class="anchor" id="autotoc_md23"></a>
API - changes C++ specific</h3>
<ul>
<li>Cam::f --&gt; Cam::f(), reducing stack size</li>
<li>Cam::ChunkEnable() --&gt; Cam::EnableChunk()</li>
<li>Cam::GetChunkNames() --&gt; Cam::GetAvailableChunks()</li>
<li>Image::PixelFormatIsAvailable() --&gt; Image::IsPixelFormatAvailable()</li>
<li>Image::GetPixelformat() --&gt; Image::GetPixelFormat()</li>
<li>Feature::GetLength() --&gt; Feature::GetRegisterLength()</li>
<li>Feature::GetAddress() --&gt; Feature::GetRegisterAddress()</li>
</ul>
<h3><a class="anchor" id="autotoc_md24"></a>
API - changes C# specific</h3>
<ul>
<li>properties used instead of functions</li>
<li>Cam.ChunkEnable() --&gt; Cam.EnableChunk</li>
<li>Cam.GetChunkNames() --&gt; Cam.AvailableChunks</li>
<li>Image.PixelFormatIsAvailable() --&gt; Image.IsPixelFormatAvailable()</li>
<li>Image.GetPixelformat() --&gt; Image.PixelFormat</li>
<li>Feature.GetLength() --&gt; Feature.RegisterLength</li>
<li>Feature.GetAddress() --&gt; Feature.RegisterAddress</li>
</ul>
<h3><a class="anchor" id="autotoc_md25"></a>
API - changes python specific</h3>
<ul>
<li>properties used instead of functions</li>
<li>Cam.ChunkEnable() --&gt; Cam.EnableChunk()</li>
<li>Cam::GetChunkNames() --&gt; Cam.GetAvailableChunks()</li>
<li>Image.PixelFormatIsAvailable() --&gt; Image.IsPixelFormatAvailable()</li>
<li>Image.GetPixelformat() --&gt; Image.GetPixelFormat()</li>
<li>Feature.GetLength() --&gt; Feature.GetRegisterLength()</li>
<li>Feature.GetAddress() --&gt; Feature.GetRegisterAddress() </li>
</ul>
</div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
