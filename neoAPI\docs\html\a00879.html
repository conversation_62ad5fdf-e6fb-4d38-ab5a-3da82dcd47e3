<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.BufferBase Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00879.html">BufferBase</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00876.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.BufferBase Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Base class to derive from for use as user buffer.  
 <a href="a00879.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.BufferBase:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00879.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a452d20e5d9f465a672f3ef50c90d59bc"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00879.html#a452d20e5d9f465a672f3ef50c90d59bc">__init__</a> (self, *args)</td></tr>
<tr class="memdesc:a452d20e5d9f465a672f3ef50c90d59bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#a452d20e5d9f465a672f3ef50c90d59bc">More...</a><br /></td></tr>
<tr class="separator:a452d20e5d9f465a672f3ef50c90d59bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:accdcffb1da9a2438b3c3df5d1b2bd377"><td class="memItemLeft" align="right" valign="top">&quot;None&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00879.html#accdcffb1da9a2438b3c3df5d1b2bd377">RegisterMemory</a> (self, &quot;bytearray&quot; memory, &quot;int&quot; size)</td></tr>
<tr class="memdesc:accdcffb1da9a2438b3c3df5d1b2bd377"><td class="mdescLeft">&#160;</td><td class="mdescRight">providing a continues memory block used for a whole buffer To obtain the required size Cam.f.PayloadSize.Get() should be used.  <a href="#accdcffb1da9a2438b3c3df5d1b2bd377">More...</a><br /></td></tr>
<tr class="separator:accdcffb1da9a2438b3c3df5d1b2bd377"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe1daf3e5b20fab0586f93bdc92c3db6"><td class="memItemLeft" align="right" valign="top">&quot;None&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00879.html#abe1daf3e5b20fab0586f93bdc92c3db6">RegisterSegment</a> (self, &quot;bytearray&quot; memory, &quot;int&quot; size, &quot;int&quot; segment_id)</td></tr>
<tr class="memdesc:abe1daf3e5b20fab0586f93bdc92c3db6"><td class="mdescLeft">&#160;</td><td class="mdescRight">provide memory used for a specific buffer segment To obtain the required size <a class="el" href="a00803.html#a00db0b522111679788d2624d5c4e32bb" title="Get the whole size of the memory segment in the buffer.">neoapi.ImageInfo.GetSegmentSize()</a> should be used.  <a href="#abe1daf3e5b20fab0586f93bdc92c3db6">More...</a><br /></td></tr>
<tr class="separator:abe1daf3e5b20fab0586f93bdc92c3db6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab7090bce8566f9b492abf7e1ba8a449"><td class="memItemLeft" align="right" valign="top">&quot;None&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00879.html#aab7090bce8566f9b492abf7e1ba8a449">RegisterSegment</a> (self, &quot;bytearray&quot; memory, &quot;int&quot; size)</td></tr>
<tr class="memdesc:aab7090bce8566f9b492abf7e1ba8a449"><td class="mdescLeft">&#160;</td><td class="mdescRight">provide memory used for the first buffer segment To obtain the required size <a class="el" href="a00803.html#a00db0b522111679788d2624d5c4e32bb" title="Get the whole size of the memory segment in the buffer.">neoapi.ImageInfo.GetSegmentSize()</a> should be used.  <a href="#aab7090bce8566f9b492abf7e1ba8a449">More...</a><br /></td></tr>
<tr class="separator:aab7090bce8566f9b492abf7e1ba8a449"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32274dfadf9ff612958c8bdda5ad4043"><td class="memItemLeft" align="right" valign="top">&quot;None&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00879.html#a32274dfadf9ff612958c8bdda5ad4043">UnregisterMemory</a> (self)</td></tr>
<tr class="memdesc:a32274dfadf9ff612958c8bdda5ad4043"><td class="mdescLeft">&#160;</td><td class="mdescRight">unregister all previous assigned memories If the buffer object is assigned to a camera the buffer will be revoked automatically.  <a href="#a32274dfadf9ff612958c8bdda5ad4043">More...</a><br /></td></tr>
<tr class="separator:a32274dfadf9ff612958c8bdda5ad4043"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:abe1d87748dfad8ccb23e6212d2c7a317"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00879.html#abe1d87748dfad8ccb23e6212d2c7a317">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:abe1d87748dfad8ccb23e6212d2c7a317"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#abe1d87748dfad8ccb23e6212d2c7a317">More...</a><br /></td></tr>
<tr class="separator:abe1d87748dfad8ccb23e6212d2c7a317"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Base class to derive from for use as user buffer. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a452d20e5d9f465a672f3ef50c90d59bc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a452d20e5d9f465a672f3ef50c90d59bc">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.BufferBase.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructor. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="accdcffb1da9a2438b3c3df5d1b2bd377"></a>
<h2 class="memtitle"><span class="permalink"><a href="#accdcffb1da9a2438b3c3df5d1b2bd377">&#9670;&nbsp;</a></span>RegisterMemory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;None&quot; neoapi.BufferBase.RegisterMemory </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;bytearray&quot;&#160;</td>
          <td class="paramname"><em>memory</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>size</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>providing a continues memory block used for a whole buffer To obtain the required size Cam.f.PayloadSize.Get() should be used. </p>
<dl class="section note"><dt>Note</dt><dd>when memory blocks where registered with RegisterSegment before these memories will be unregistered from this buffer. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">memory</td><td>address to the memory </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">size</td><td>size of the memory </td></tr>
  </table>
  </dd>
</dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00791.html" title="Invalid Arguments Exception.">InvalidArgumentException</a></td><td>when buffer is already assigned to a camera object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="abe1daf3e5b20fab0586f93bdc92c3db6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe1daf3e5b20fab0586f93bdc92c3db6">&#9670;&nbsp;</a></span>RegisterSegment() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;None&quot; neoapi.BufferBase.RegisterSegment </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;bytearray&quot;&#160;</td>
          <td class="paramname"><em>memory</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>size</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>segment_id</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>provide memory used for a specific buffer segment To obtain the required size <a class="el" href="a00803.html#a00db0b522111679788d2624d5c4e32bb" title="Get the whole size of the memory segment in the buffer.">neoapi.ImageInfo.GetSegmentSize()</a> should be used. </p>
<dl class="section note"><dt>Note</dt><dd>When a memory block was registered with RegisterMemory before this memory will be unregistered from this buffer. </dd>
<dd>
Only when all registered memories for their segments are big enough and when the underlying producer support composite buffers (segments) the buffer is used during acquisition. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">memory</td><td>address to the memory </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">size</td><td>size of the memory </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">segment_id</td><td>segment identifier </td></tr>
  </table>
  </dd>
</dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00791.html" title="Invalid Arguments Exception.">InvalidArgumentException</a></td><td>when buffer is already assigned to a camera object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aab7090bce8566f9b492abf7e1ba8a449"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aab7090bce8566f9b492abf7e1ba8a449">&#9670;&nbsp;</a></span>RegisterSegment() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;None&quot; neoapi.BufferBase.RegisterSegment </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;bytearray&quot;&#160;</td>
          <td class="paramname"><em>memory</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>size</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>provide memory used for the first buffer segment To obtain the required size <a class="el" href="a00803.html#a00db0b522111679788d2624d5c4e32bb" title="Get the whole size of the memory segment in the buffer.">neoapi.ImageInfo.GetSegmentSize()</a> should be used. </p>
<dl class="section note"><dt>Note</dt><dd>When a memory block was registered with RegisterMemory before this memory will be unregistered from this buffer. </dd>
<dd>
Only when all registered memories for their segments are big enough and when the underlying producer support composite buffers (segments) the buffer is used during acquisition. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">memory</td><td>address to the memory </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">size</td><td>size of the memory </td></tr>
  </table>
  </dd>
</dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00791.html" title="Invalid Arguments Exception.">InvalidArgumentException</a></td><td>when buffer is already assigned to a camera object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a32274dfadf9ff612958c8bdda5ad4043"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a32274dfadf9ff612958c8bdda5ad4043">&#9670;&nbsp;</a></span>UnregisterMemory()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;None&quot; neoapi.BufferBase.UnregisterMemory </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>unregister all previous assigned memories If the buffer object is assigned to a camera the buffer will be revoked automatically. </p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="abe1d87748dfad8ccb23e6212d2c7a317"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe1d87748dfad8ccb23e6212d2c7a317">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.BufferBase.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
