<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_i"></a>- i -</h3><ul>
<li>ImageCallback()
: <a class="el" href="a00871.html#a01e0dd09362f2d4db1e844a08f124f12">neoapi.NeoImageCallback</a>
</li>
<li>ImageCompressionBitrate()
: <a class="el" href="a00763.html#a66062b311fe6cde4f5c111e73a3f8d24">neoapi.FeatureAccess</a>
</li>
<li>ImageCompressionJPEGFormatOption()
: <a class="el" href="a00763.html#a6b5532782e7c86e024249dc445704f17">neoapi.FeatureAccess</a>
</li>
<li>ImageCompressionMode()
: <a class="el" href="a00763.html#af0b15c9f6816661022878ef70ec46758">neoapi.FeatureAccess</a>
</li>
<li>ImageCompressionQuality()
: <a class="el" href="a00763.html#a33f073f6acce0eff94e2f4a567b9ff73">neoapi.FeatureAccess</a>
</li>
<li>ImageCompressionRateOption()
: <a class="el" href="a00763.html#afc85222e25af88456600a8fe50044842">neoapi.FeatureAccess</a>
</li>
<li>ImageCompressionVersion()
: <a class="el" href="a00763.html#a6c43acad603961fe97d7ec2a74dc582c">neoapi.FeatureAccess</a>
</li>
<li>ImageData()
: <a class="el" href="a00763.html#a4321b7273cc97043c3cb12fddc18428b">neoapi.FeatureAccess</a>
</li>
<li>ImageDataEnable()
: <a class="el" href="a00763.html#a12f61ab6393a3956a89c7b2f847a5645">neoapi.FeatureAccess</a>
</li>
<li>Info()
: <a class="el" href="a00899.html#a7c052e6ff01de7ce082a7bbdd91ad333">neoapi.NeoTrace</a>
</li>
<li>InterfaceSpeedMode()
: <a class="el" href="a00763.html#ae45f3342b71cc2e9d7f9cfa1fa2444a7">neoapi.FeatureAccess</a>
</li>
<li>IsAvailable()
: <a class="el" href="a00823.html#ada3c4ad870b561c575714a16f70d19db">neoapi.BaseFeature</a>
, <a class="el" href="a00811.html#acd8890c4c4e5d90ff9aabd069c1e9af4">neoapi.Feature</a>
</li>
<li>IsConnectable()
: <a class="el" href="a00883.html#a27191e69df494ad411b19603074cece0">neoapi.CamInfo</a>
</li>
<li>IsConnected()
: <a class="el" href="a00859.html#ad5d532323d90fcb245a0c2e9b4079d26">neoapi.CamBase</a>
</li>
<li>IsDone()
: <a class="el" href="a00843.html#ae956efb17be5de0ea7e50ef52e8b7bc7">neoapi.CommandFeature</a>
, <a class="el" href="a00811.html#a5f725a3ccd37f41f5f154784075adea7">neoapi.Feature</a>
</li>
<li>IsEmpty()
: <a class="el" href="a00855.html#a1b97c1b3863997a4f2274ace6f8d68fa">neoapi.Image</a>
, <a class="el" href="a00807.html#a319a9dbb64925123137c58a8466196b4">neoapi.NeoEvent</a>
</li>
<li>IsLastImage()
: <a class="el" href="a00855.html#a2162ba8e37ec2f240183953c984a4d30">neoapi.Image</a>
</li>
<li>IsOnline()
: <a class="el" href="a00859.html#aaaf166323ef78a64f75e02be7a32d8fa">neoapi.CamBase</a>
</li>
<li>IsPixelFormatAvailable()
: <a class="el" href="a00855.html#a8f2d9ca68a7bdc6726483914453285e3">neoapi.Image</a>
</li>
<li>IsReadable()
: <a class="el" href="a00823.html#a04f73633d9a8b93b7865bf91d2d36b67">neoapi.BaseFeature</a>
, <a class="el" href="a00859.html#a7e02b28ffff318ae93a6c0a37b5de38a">neoapi.CamBase</a>
, <a class="el" href="a00811.html#a6012ab5cf826fbf4e47147e56f2c00cd">neoapi.Feature</a>
, <a class="el" href="a00815.html#a92efae5d78e74056ca4be8180ddb014d">neoapi.FeatureList</a>
</li>
<li>IsSegmentShared()
: <a class="el" href="a00803.html#ab0f6176d9aba067023fc07d32cd374d7">neoapi.ImageInfo</a>
</li>
<li>IsSelector()
: <a class="el" href="a00847.html#ab78a7830a2eeff9e0c82484d8f538962">neoapi.EnumerationFeature</a>
, <a class="el" href="a00811.html#a85c27bf170bda573befcdbe84de2cd07">neoapi.Feature</a>
, <a class="el" href="a00831.html#a9e88fa23dbd3678afd3726b1ff49786e">neoapi.IntegerFeature</a>
</li>
<li>IsStreaming()
: <a class="el" href="a00859.html#a16d699f52cbdfa7b8f2ff2ed08149839">neoapi.CamBase</a>
</li>
<li>IsWritable()
: <a class="el" href="a00823.html#aac1faf49fc350a92ddf2ce11d35ba85c">neoapi.BaseFeature</a>
, <a class="el" href="a00859.html#afc86d804d99b2d9b11ce5c685548b24b">neoapi.CamBase</a>
, <a class="el" href="a00811.html#a0c3ef92f53d96f95324e130c56ecb1bb">neoapi.Feature</a>
, <a class="el" href="a00815.html#a505f3f3066e52efb89344680f6bc070a">neoapi.FeatureList</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
