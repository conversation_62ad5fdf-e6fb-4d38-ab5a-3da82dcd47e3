<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Member List</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00855.html">Image</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">neoapi.Image Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="a00855.html">neoapi.Image</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="a00855.html#a9c163a4c5d6c5372acfb297c2c62ff08">__init__</a>(self, *args)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00855.html#af9fd5b5a5d619eb69e2c6c49e51c5e70">Convert</a>(self, *args)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00855.html#a0688ec4d1bb9e85d943512fdf0fa6c54">Copy</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00855.html#ae418b72ce66230f42362b7226b731b1d">GetAvailablePixelFormats</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00855.html#ac665b2b3f7443eaf890b3e9afa71fda7">GetBufferID</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00855.html#a44768b042b6a974c18bc90197a761084">GetChunkList</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00803.html#a501b21913ccb8e7ad6836707f96511e9">GetCompression</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00803.html#a4d5595e748e4efee67fb72bca5d12d51">GetGroupID</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00803.html#ae2d5b973f50c053e96ecc7edfe9da635">GetHeight</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00855.html#a4ad7ae4cf7596593572ddd5eb30fcf10">GetImageData</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00855.html#a88cc2674ac9d27c44c5ad4879ee60774">GetImageIndex</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00855.html#ab3d59ea6282dcbb17551891f80b3b667">GetNPArray</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00803.html#a66e5a1a4759118139478186645dcde8e">GetPixelFormat</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00803.html#a1dd62ce238c06939313a60842098db1b">GetRegionID</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00803.html#ab40fa50855bca84a534c90af65c32230">GetSegmentIndex</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00803.html#a0026a34eafc3b9412a7bc3cb58c4db6d">GetSegmentOffset</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00803.html#a00db0b522111679788d2624d5c4e32bb">GetSegmentSize</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00803.html#a2a4b22523f4c2591f18a92aa6672cb3b">GetSize</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00803.html#a4c62cef40e061aa4b5648e15f6d45739">GetSourceID</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00855.html#a7bdb17944ef845188c485610b474251e">GetTimestamp</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00855.html#aea3dfa8dcd210d1475ee8ed0d9b79e16">GetUserBuffer</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00803.html#ae70432b723d9c832f6abb5cae6030bd5">GetWidth</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00803.html#a27e12c31a0a6257a2f34c5549412341a">GetXOffset</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00803.html#ae264d4741c2e3768699e8305f4502bbe">GetXPadding</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00803.html#a74ea98a5251ad92bcfc39dffb7e46ced">GetYOffset</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00803.html#aa68a32ae42c15e79caace60e346a4ed6">GetYPadding</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00855.html#a1b97c1b3863997a4f2274ace6f8d68fa">IsEmpty</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00855.html#a2162ba8e37ec2f240183953c984a4d30">IsLastImage</a>(self)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00855.html#a8f2d9ca68a7bdc6726483914453285e3">IsPixelFormatAvailable</a>(self, &quot;str&quot; pixelformat)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00803.html#ab0f6176d9aba067023fc07d32cd374d7">IsSegmentShared</a>(self)</td><td class="entry"><a class="el" href="a00803.html">neoapi.ImageInfo</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00855.html#a7281f446abf2c472c043334bb25e6fdc">Save</a>(self, &quot;str&quot; filename)</td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00855.html#aea66aea381f903a8ae7cc7d4f6685822">thisown</a></td><td class="entry"><a class="el" href="a00855.html">neoapi.Image</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
