<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_e"></a>- e -</h3><ul>
<li>EventNotification_Off
: <a class="el" href="a00091.html#afaa2789623770ba19815c0b6bafb82c5">neoapi</a>
</li>
<li>EventNotification_On
: <a class="el" href="a00091.html#a1016f1096021ee25196cf67863c217f6">neoapi</a>
</li>
<li>EventNotification_Once
: <a class="el" href="a00091.html#a595576d949027873168f0aad4f9bd2cd">neoapi</a>
</li>
<li>EventSelector_AcquisitionEnd
: <a class="el" href="a00091.html#a2777778d52c18fd285d3325bb09d3835">neoapi</a>
</li>
<li>EventSelector_AcquisitionError
: <a class="el" href="a00091.html#ad4f5e00b0a7991adbd891dc9b80be90e">neoapi</a>
</li>
<li>EventSelector_AcquisitionStart
: <a class="el" href="a00091.html#a30f3004f708bd1c7b0ae671c979321d9">neoapi</a>
</li>
<li>EventSelector_AcquisitionTransferEnd
: <a class="el" href="a00091.html#a1b72ec83cdfdb1bc187ad703a9818b3f">neoapi</a>
</li>
<li>EventSelector_AcquisitionTransferStart
: <a class="el" href="a00091.html#a1f303b07755e55b5e847b0c7d6f23070">neoapi</a>
</li>
<li>EventSelector_AcquisitionTrigger
: <a class="el" href="a00091.html#a4099c634bf1baa8ddfe324b683ac17ef">neoapi</a>
</li>
<li>EventSelector_AcquisitionTriggerMissed
: <a class="el" href="a00091.html#abfa7d43aca041240cc37e2ae0d5980e0">neoapi</a>
</li>
<li>EventSelector_Action1
: <a class="el" href="a00091.html#a12ec86a4985d7a9fe16348c9ac63b9d3">neoapi</a>
</li>
<li>EventSelector_ActionLate
: <a class="el" href="a00091.html#a89fe798c0f0eee067d45d40e4fe44534">neoapi</a>
</li>
<li>EventSelector_Counter0End
: <a class="el" href="a00091.html#a717bc059d5f02d0e01d873a9585d2e0d">neoapi</a>
</li>
<li>EventSelector_Counter0Start
: <a class="el" href="a00091.html#ada2acbece861e459c8dafd421c82ff86">neoapi</a>
</li>
<li>EventSelector_Counter1End
: <a class="el" href="a00091.html#a38986fba7cfc38d2a5f765e96e73f0ba">neoapi</a>
</li>
<li>EventSelector_Counter1Start
: <a class="el" href="a00091.html#addf3f864d300a74b6d69bc4dbe645848">neoapi</a>
</li>
<li>EventSelector_DeviceTemperatureStatusChanged
: <a class="el" href="a00091.html#a5859a7ed3641257b72858109ef9ade38">neoapi</a>
</li>
<li>EventSelector_Encoder0Restarted
: <a class="el" href="a00091.html#a01a8e2fa4adebad464243bebe77892a6">neoapi</a>
</li>
<li>EventSelector_Encoder0Stopped
: <a class="el" href="a00091.html#aa87216c7dafc039cd018c90900dc9830">neoapi</a>
</li>
<li>EventSelector_Encoder1Restarted
: <a class="el" href="a00091.html#a38384be403aa597754b238d532ef4e67">neoapi</a>
</li>
<li>EventSelector_Encoder1Stopped
: <a class="el" href="a00091.html#ae1051f9e6c0930c99eabaa13a0612356">neoapi</a>
</li>
<li>EventSelector_Error
: <a class="el" href="a00091.html#ac3dc6f586f5dafe08b704a40ee6ff7ef">neoapi</a>
</li>
<li>EventSelector_EventDiscarded
: <a class="el" href="a00091.html#a2a05aa12c0505d7668d2a1547df3a4be">neoapi</a>
</li>
<li>EventSelector_EventLost
: <a class="el" href="a00091.html#a10f59bc97b64490c9497be1cabf528cd">neoapi</a>
</li>
<li>EventSelector_ExposureEnd
: <a class="el" href="a00091.html#ace7afefb5efd13d6e83b0fa4d378f136">neoapi</a>
</li>
<li>EventSelector_ExposureStart
: <a class="el" href="a00091.html#a2ff78534475b6c34c9f06738f43e8600">neoapi</a>
</li>
<li>EventSelector_FrameBurstEnd
: <a class="el" href="a00091.html#a923eebc347641fed018ee328f03bc71f">neoapi</a>
</li>
<li>EventSelector_FrameBurstStart
: <a class="el" href="a00091.html#ad9b813bc47377f8f7adb0e10e099124e">neoapi</a>
</li>
<li>EventSelector_FrameEnd
: <a class="el" href="a00091.html#a30cffaaeac82b746da973ba36977ff37">neoapi</a>
</li>
<li>EventSelector_FrameStart
: <a class="el" href="a00091.html#a88552531ffb3b009601cefac718e46bf">neoapi</a>
</li>
<li>EventSelector_FrameTransferEnd
: <a class="el" href="a00091.html#a1e443ead632e433c216b944f6ef7f63a">neoapi</a>
</li>
<li>EventSelector_FrameTransferSkipped
: <a class="el" href="a00091.html#a5a13f287c273691252e889f7117bd661">neoapi</a>
</li>
<li>EventSelector_FrameTransferStart
: <a class="el" href="a00091.html#a567e81521b0ec057d89f1b74c84e57f0">neoapi</a>
</li>
<li>EventSelector_FrameTrigger
: <a class="el" href="a00091.html#a6fd36b2f9887c5a510d44efd60d6a05c">neoapi</a>
</li>
<li>EventSelector_FrameTriggerMissed
: <a class="el" href="a00091.html#adea231a3c2fcab45b92a2422248a14b5">neoapi</a>
</li>
<li>EventSelector_GigEVisionError
: <a class="el" href="a00091.html#a1a73d83bd53a48595d2e66344c076a72">neoapi</a>
</li>
<li>EventSelector_GigEVisionHeartbeatTimeOut
: <a class="el" href="a00091.html#afc26570e5138d04716111213dc098751">neoapi</a>
</li>
<li>EventSelector_Line0AnyEdge
: <a class="el" href="a00091.html#a8c922f60dd8b86a059d32bef3c216372">neoapi</a>
</li>
<li>EventSelector_Line0FallingEdge
: <a class="el" href="a00091.html#a998c373cceaac1795b08885c89be2098">neoapi</a>
</li>
<li>EventSelector_Line0RisingEdge
: <a class="el" href="a00091.html#af5633f77b77e78a279a0b912c36dd001">neoapi</a>
</li>
<li>EventSelector_Line1AnyEdge
: <a class="el" href="a00091.html#a8bd79c887b95bc2651bb460011e2b1c5">neoapi</a>
</li>
<li>EventSelector_Line1FallingEdge
: <a class="el" href="a00091.html#aeefd1c096fbc0f6db9acbc06ab81299c">neoapi</a>
</li>
<li>EventSelector_Line1RisingEdge
: <a class="el" href="a00091.html#af0d1f5ca9fcfbb81b1e7e13b9013b5fa">neoapi</a>
</li>
<li>EventSelector_Line2FallingEdge
: <a class="el" href="a00091.html#addafe93208b3a38141a512a6e09c973b">neoapi</a>
</li>
<li>EventSelector_Line2RisingEdge
: <a class="el" href="a00091.html#af06f8e7da1376621ee1f1c256414dc3c">neoapi</a>
</li>
<li>EventSelector_Line3FallingEdge
: <a class="el" href="a00091.html#a1c88c8cc03c681c819d1dbeed2815184">neoapi</a>
</li>
<li>EventSelector_Line3RisingEdge
: <a class="el" href="a00091.html#a6f1cd6616df4e2654118e7fd4b3514a8">neoapi</a>
</li>
<li>EventSelector_Line4FallingEdge
: <a class="el" href="a00091.html#a7b7ec5f2e46f3023b61ae7eb7032367f">neoapi</a>
</li>
<li>EventSelector_Line4RisingEdge
: <a class="el" href="a00091.html#a943c18c85cd0ca3d92b7d1cdc1aa8155">neoapi</a>
</li>
<li>EventSelector_Line5FallingEdge
: <a class="el" href="a00091.html#adf4ac150332d6c57f74d3d99b0a0c0be">neoapi</a>
</li>
<li>EventSelector_Line5RisingEdge
: <a class="el" href="a00091.html#a3696a0c798fd28f104f4d7e057aeeed2">neoapi</a>
</li>
<li>EventSelector_Line6FallingEdge
: <a class="el" href="a00091.html#a45edefa14b7f010924c3b45fd7dce2c8">neoapi</a>
</li>
<li>EventSelector_Line6RisingEdge
: <a class="el" href="a00091.html#a0411162f48ca925fdd31d868e7a5708c">neoapi</a>
</li>
<li>EventSelector_Line7FallingEdge
: <a class="el" href="a00091.html#accb1d293d65a67cadb2c62c372fc1475">neoapi</a>
</li>
<li>EventSelector_Line7RisingEdge
: <a class="el" href="a00091.html#ad8b8e08b6910f172464dfafc2c3c03d0">neoapi</a>
</li>
<li>EventSelector_LineEnd
: <a class="el" href="a00091.html#a982291dc6a02338778db08a68f820c4f">neoapi</a>
</li>
<li>EventSelector_LineStart
: <a class="el" href="a00091.html#a6579fa25225e2deef4fa8c1b6a6eed5e">neoapi</a>
</li>
<li>EventSelector_LineTrigger
: <a class="el" href="a00091.html#a84c65f2e0bcdb558c60f2e32569a7f26">neoapi</a>
</li>
<li>EventSelector_LineTriggerMissed
: <a class="el" href="a00091.html#a826b3ac918f2b5ed49767a235bd30d8a">neoapi</a>
</li>
<li>EventSelector_LinkSpeedChange
: <a class="el" href="a00091.html#a93063c5113953a47280eab2166d49c55">neoapi</a>
</li>
<li>EventSelector_LinkTrigger0
: <a class="el" href="a00091.html#ad62b139341e02aac80916f47a87f2d3c">neoapi</a>
</li>
<li>EventSelector_LinkTrigger1
: <a class="el" href="a00091.html#a52a515db007dd1cc8e4f665cdb44c14f">neoapi</a>
</li>
<li>EventSelector_PhysicalConnection0Down
: <a class="el" href="a00091.html#abe77885b5e318a764728a7fab90aff3a">neoapi</a>
</li>
<li>EventSelector_PhysicalConnection0Up
: <a class="el" href="a00091.html#afd72cc2e9628c3b95d48feb1bb05682a">neoapi</a>
</li>
<li>EventSelector_PhysicalConnection1Down
: <a class="el" href="a00091.html#ad864fc05ffbb2165ce01b5a7cf8ddc8d">neoapi</a>
</li>
<li>EventSelector_PhysicalConnection1Up
: <a class="el" href="a00091.html#a704f431c214e5077e83455d764b2eb7c">neoapi</a>
</li>
<li>EventSelector_PrimaryApplicationSwitch
: <a class="el" href="a00091.html#adad9ccb8169ca27d886c0f0895d7d37b">neoapi</a>
</li>
<li>EventSelector_PtpServoStatusChanged
: <a class="el" href="a00091.html#a2931b98732f98c8794c6d84bb1fd62db">neoapi</a>
</li>
<li>EventSelector_PtpStatusChanged
: <a class="el" href="a00091.html#a234682e9c135feeca21ee90cda4e1e60">neoapi</a>
</li>
<li>EventSelector_SequencerSetChange
: <a class="el" href="a00091.html#ab8793849278542dafeb5b22fa5beb644">neoapi</a>
</li>
<li>EventSelector_Stream0TransferBlockEnd
: <a class="el" href="a00091.html#a4aca644d861515d068e52f4765627563">neoapi</a>
</li>
<li>EventSelector_Stream0TransferBlockStart
: <a class="el" href="a00091.html#a22ccfc60ecb2f70c820e97e96343b3e2">neoapi</a>
</li>
<li>EventSelector_Stream0TransferBlockTrigger
: <a class="el" href="a00091.html#a37afff46c6d42d71dc7ccfedf4c14dc2">neoapi</a>
</li>
<li>EventSelector_Stream0TransferBurstEnd
: <a class="el" href="a00091.html#a10972c712df63d97055b8cf40ba739cf">neoapi</a>
</li>
<li>EventSelector_Stream0TransferBurstStart
: <a class="el" href="a00091.html#a20b8890d64b004b0d85bece139081586">neoapi</a>
</li>
<li>EventSelector_Stream0TransferEnd
: <a class="el" href="a00091.html#a9d95f644158811a648bc31e2a024dd73">neoapi</a>
</li>
<li>EventSelector_Stream0TransferOverflow
: <a class="el" href="a00091.html#afa90f51bd39448b99fbc91ea3eb4183c">neoapi</a>
</li>
<li>EventSelector_Stream0TransferPause
: <a class="el" href="a00091.html#aba4cd07f6258a7b4741490f7e30c0a8b">neoapi</a>
</li>
<li>EventSelector_Stream0TransferResume
: <a class="el" href="a00091.html#a37359960b1d5b14ec5f887af412867bf">neoapi</a>
</li>
<li>EventSelector_Stream0TransferStart
: <a class="el" href="a00091.html#a2a28cba8b9088cea5c447148d0c03d2c">neoapi</a>
</li>
<li>EventSelector_Test
: <a class="el" href="a00091.html#a7c9957900e2297c4f126fe19e4167e42">neoapi</a>
</li>
<li>EventSelector_Timer0End
: <a class="el" href="a00091.html#a9e880ddcd454ac6a6477501b57673c67">neoapi</a>
</li>
<li>EventSelector_Timer0Start
: <a class="el" href="a00091.html#a03a0e94b86104ed6a0573d07081dc84c">neoapi</a>
</li>
<li>EventSelector_Timer1End
: <a class="el" href="a00091.html#a3fbccb1008bd78dc72544b025b9ff320">neoapi</a>
</li>
<li>EventSelector_Timer1Start
: <a class="el" href="a00091.html#abb2a11709d2c19d052ef3fc0acd48946">neoapi</a>
</li>
<li>EventSelector_Timer2End
: <a class="el" href="a00091.html#ab606b4292e2815f892a33657c98b0f0c">neoapi</a>
</li>
<li>EventSelector_Timer3End
: <a class="el" href="a00091.html#ab0a398bc145f7617340a8a3296ea45c6">neoapi</a>
</li>
<li>EventSelector_TransferBufferFull
: <a class="el" href="a00091.html#a22ddfa46d4fc055c4c54b4ce73b19f9e">neoapi</a>
</li>
<li>EventSelector_TransferBufferReady
: <a class="el" href="a00091.html#ac4c157129962115daafd579bbded9d43">neoapi</a>
</li>
<li>EventSelector_TriggerOverlapped
: <a class="el" href="a00091.html#ab0ebb0463d10ea807f79dfac7ef1b79e">neoapi</a>
</li>
<li>EventSelector_TriggerReady
: <a class="el" href="a00091.html#a8948fbf8b1c4e41b5392c965df0ca2da">neoapi</a>
</li>
<li>EventSelector_TriggerSkipped
: <a class="el" href="a00091.html#a375f45c3cd5022a0b928670582194d53">neoapi</a>
</li>
<li>ExposureAuto_Continuous
: <a class="el" href="a00091.html#a14cace7ca40001839ec56b7dfe703d21">neoapi</a>
</li>
<li>ExposureAuto_Off
: <a class="el" href="a00091.html#aa0b7ee047e44f9746554346d82339640">neoapi</a>
</li>
<li>ExposureAuto_Once
: <a class="el" href="a00091.html#a7ff272dbf37815f6a1e2dd2897b50f86">neoapi</a>
</li>
<li>ExposureMode_Off
: <a class="el" href="a00091.html#ac3c9f683d2d9fe10a216d979f8634b30">neoapi</a>
</li>
<li>ExposureMode_Timed
: <a class="el" href="a00091.html#a72a7040c08f13e5241c9b31c4d6b5e61">neoapi</a>
</li>
<li>ExposureMode_TriggerControlled
: <a class="el" href="a00091.html#ac46f744208234310fccb7d87b8389c11">neoapi</a>
</li>
<li>ExposureMode_TriggerWidth
: <a class="el" href="a00091.html#ac0b14311f1af29bdf6108c072f9fc1d2">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
