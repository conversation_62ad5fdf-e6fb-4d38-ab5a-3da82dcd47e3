<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Welcome to the Baumer neoAPI for Python</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Welcome to the Baumer neoAPI for Python </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p>Welcome to the release of the new Baumer neoAPI. neoAPI strives to be your goto API for really fast and efficient GenICam camera integration. neoAPI is available for C++, C# and Python and for your choice of architecture; Windows x64, Linux x64, Linux AArch64.</p>
<p>So weather you need to write a proof of concept quickly in Python or you want to squeeze the last bit of performance out of the system in C++ — neoAPI makes working with cameras both easy and efficient.</p>
<div class="image">
<img src="neoAPI_all_languages.png" alt="neoAPI_all_languages.png" width="90%"/>
<div class="caption">
neoAPI code can easily be reused for C++, C# and Python</div></div>
<p>You can download the neoAPI packages for your preferred language and architecture from the <a href="https://www.baumer.com/c/333">Baumer Website</a>.</p>
<h1><a class="anchor" id="autotoc_md98"></a>
Introduction to the neoAPI</h1>
<p>Since more than 15 years, Baumer provides the Baumer GAPI SDK to help you integrate GenICam camera devices. The Baumer GAPI is widely used by numerous camera integrators in many industries. As camera hardware and computing devices becomes more powerful, more and more image recognition tasks are enabled and the usage of cameras in industrial application has skyrocketed.</p>
<blockquote class="doxtable">
<p>We think it is time for a new, more intuitive and much simplified way to integrate a camera sensor. To open up the many uses of such a flexible sensor as a Baumer industrial camera to a wide range of potential users. </p>
</blockquote>
<p><br />
 While developing the neoAPI we were guided by a couple of basic principles:</p>
<ul>
<li>Reduce the lines of code an integrator has to write</li>
<li>Reduce the amount of necessary prior knowledge of the GenICam standard</li>
<li>Provide a clean and intuitive API interface</li>
<li>Be tolerant of errors or misconfigurations</li>
<li>Enable speedy and efficient handling of the image data</li>
</ul>
<p>What you get is an API which makes retrieving images from a camera as simple as possible.</p>
<p>Feel free to start browsing the API documentation now or read further to get more information about the underlying concepts or more help on how to get the API working on your system.</p>
<h1><a class="anchor" id="autotoc_md99"></a>
Machine vision basics — EMVA, GenICam, SFNC, what?</h1>
<p>If you are new to machine vision you likely have never heard of the standards and the way how to work with a camera. We provide you with the basics to get you started quickly <a class="el" href="a00925.html">here</a>.</p>
<h1><a class="anchor" id="autotoc_md100"></a>
Getting started with neoAPI</h1>
<p>If you want to start playing with your camera, follow <a class="el" href="a00928.html">this link</a> to learn more about prerequisites, installation and the first steps.</p>
<h1><a class="anchor" id="autotoc_md101"></a>
Programming concepts</h1>
<p>We provide documentation for the main programming concepts in much detail. Please browse the Topic section to find how things work in the neoAPI</p>
<h1><a class="anchor" id="autotoc_md102"></a>
Further help</h1>
<p>In case of questions, suggestions or to raise an issue, please contact the Baumer camera support at <a href="#" onclick="location.href='mai'+'lto:'+'sup'+'po'+'rt.'+'ca'+'mer'+'as'+'@ba'+'um'+'er.'+'co'+'m'; return false;">suppo<span style="display: none;">.nosp@m.</span>rt.c<span style="display: none;">.nosp@m.</span>amera<span style="display: none;">.nosp@m.</span>s@ba<span style="display: none;">.nosp@m.</span>umer.<span style="display: none;">.nosp@m.</span>com</a>.</p>
<p>Documentation about your cameras can be found on the <a href="https://www.baumer.com/c/331">Baumer website</a> and in the <a href="http://vt.baumer.com">Baumer Member Area</a> which requires you to login.</p>
<p>Baumer application notes are also available on the Baumer website <a href="https://www.baumer.com/a/application-notes-industrial-cameras">here</a>.</p>
<h1><a class="anchor" id="autotoc_md103"></a>
Licence</h1>
<p>Please see the /Licence file for licencing information. </p>
</div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
