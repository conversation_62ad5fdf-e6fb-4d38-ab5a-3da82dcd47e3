import logging
from datetime import datetime, timedelta
import pyqtgraph as pg
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QComboBox, 
                             QLabel, QPushButton, QDateTimeEdit, QGroupBox)
from PyQt5.QtCore import QTimer, QDateTime
import numpy as np

class TemperaturePlotWidget(QWidget):
    """Widget für Temperaturverlauf-Anzeige"""
    
    def __init__(self, database):
        super().__init__()
        self.database = database
        self.plot_data = {}  # IP -> {'x': [], 'y': []}
        self.plot_curves = {}  # IP -> PlotCurveItem
        self.colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF']
        self.color_index = 0
        
        self.init_ui()
        
        # Timer für Plot-Updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_plots)
        self.update_timer.start(5000)  # 5 Sekunden
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        layout = QVBoxLayout(self)
        
        # Steuerungsbereich
        control_group = QGroupBox("Anzeige-Optionen")
        control_layout = QHBoxLayout(control_group)
        
        # Zeitbereich-Auswahl
        control_layout.addWidget(QLabel("Zeitbereich:"))
        self.time_range_combo = QComboBox()
        self.time_range_combo.addItems([
            "Letzte Stunde",
            "Letzte 6 Stunden", 
            "Letzte 24 Stunden",
            "Letzte 7 Tage",
            "Letzte 30 Tage"
        ])
        self.time_range_combo.setCurrentText("Letzte 24 Stunden")
        self.time_range_combo.currentTextChanged.connect(self.on_time_range_changed)
        control_layout.addWidget(self.time_range_combo)
        
        # Kamera-Filter
        control_layout.addWidget(QLabel("Kamera:"))
        self.camera_combo = QComboBox()
        self.camera_combo.addItem("Alle Kameras")
        self.camera_combo.currentTextChanged.connect(self.refresh_plots)
        control_layout.addWidget(self.camera_combo)
        
        # Aktualisieren-Button
        self.refresh_button = QPushButton("Aktualisieren")
        self.refresh_button.clicked.connect(self.refresh_plots)
        control_layout.addWidget(self.refresh_button)
        
        control_layout.addStretch()
        layout.addWidget(control_group)
        
        # Plot-Widget
        self.plot_widget = pg.PlotWidget()
        self.plot_widget.setLabel('left', 'Temperatur', units='°C')
        self.plot_widget.setLabel('bottom', 'Zeit')
        self.plot_widget.showGrid(x=True, y=True)
        self.plot_widget.setBackground('white')
        
        # Temperatur-Grenzlinien
        self.add_threshold_lines()
        
        layout.addWidget(self.plot_widget)
        
        # Legende
        self.legend = self.plot_widget.addLegend()
        
        # Status-Label
        self.status_label = QLabel("Bereit für Temperaturanzeige")
        layout.addWidget(self.status_label)
        
    def add_threshold_lines(self):
        """Fügt Temperatur-Grenzlinien hinzu"""
        from core.config import TEMP_WARNING_THRESHOLD, TEMP_CRITICAL_THRESHOLD
        
        # Warnung-Linie
        warning_line = pg.InfiniteLine(
            pos=TEMP_WARNING_THRESHOLD, 
            angle=0, 
            pen=pg.mkPen(color='orange', width=2, style=pg.QtCore.Qt.DashLine)
        )
        warning_line.setToolTip(f"Warnschwelle: {TEMP_WARNING_THRESHOLD}°C")
        self.plot_widget.addItem(warning_line)
        
        # Kritisch-Linie
        critical_line = pg.InfiniteLine(
            pos=TEMP_CRITICAL_THRESHOLD, 
            angle=0, 
            pen=pg.mkPen(color='red', width=2, style=pg.QtCore.Qt.DashLine)
        )
        critical_line.setToolTip(f"Kritische Schwelle: {TEMP_CRITICAL_THRESHOLD}°C")
        self.plot_widget.addItem(critical_line)
        
    def get_time_range_hours(self):
        """Gibt Zeitbereich in Stunden zurück"""
        range_text = self.time_range_combo.currentText()
        
        if "Stunde" in range_text:
            if "6" in range_text:
                return 6
            else:
                return 1
        elif "24" in range_text:
            return 24
        elif "7 Tage" in range_text:
            return 24 * 7
        elif "30 Tage" in range_text:
            return 24 * 30
        else:
            return 24
            
    def on_time_range_changed(self):
        """Wird aufgerufen wenn Zeitbereich geändert wird"""
        self.refresh_plots()
        
    def refresh_plots(self):
        """Aktualisiert alle Plots"""
        try:
            # Lösche alte Plots
            for curve in self.plot_curves.values():
                self.plot_widget.removeItem(curve)
            self.plot_curves.clear()
            self.plot_data.clear()
            
            # Hole Kameras
            cameras = self.database.get_cameras()
            selected_camera = self.camera_combo.currentText()
            
            # Update Kamera-Combo
            current_cameras = [self.camera_combo.itemText(i) for i in range(1, self.camera_combo.count())]
            new_cameras = [f"{cam[1]} ({cam[4]})" for cam in cameras]  # IP (Name)
            
            if current_cameras != new_cameras:
                self.camera_combo.clear()
                self.camera_combo.addItem("Alle Kameras")
                for cam_text in new_cameras:
                    self.camera_combo.addItem(cam_text)
                    
            # Lade Temperatur-Daten
            hours = self.get_time_range_hours()
            self.color_index = 0
            
            for camera in cameras:
                camera_id, ip, model, serial, name = camera[:5]
                camera_display = f"{ip} ({name})"
                
                # Prüfe Filter
                if selected_camera != "Alle Kameras" and selected_camera not in camera_display:
                    continue
                    
                # Hole Temperatur-Historie
                temp_history = self.database.get_temperature_history(camera_id, hours)
                
                if temp_history:
                    # Konvertiere zu Plot-Daten
                    timestamps = []
                    temperatures = []
                    
                    for timestamp_str, temp in temp_history:
                        try:
                            # Parse Timestamp
                            if isinstance(timestamp_str, str):
                                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                            else:
                                timestamp = timestamp_str
                                
                            timestamps.append(timestamp.timestamp())
                            temperatures.append(temp)
                        except Exception as e:
                            logging.warning(f"Fehler beim Parsen des Timestamps: {e}")
                            continue
                            
                    if timestamps and temperatures:
                        # Erstelle Plot-Kurve
                        color = self.colors[self.color_index % len(self.colors)]
                        self.color_index += 1
                        
                        curve = self.plot_widget.plot(
                            timestamps, 
                            temperatures,
                            pen=pg.mkPen(color=color, width=2),
                            name=camera_display
                        )
                        
                        self.plot_curves[ip] = curve
                        self.plot_data[ip] = {
                            'x': timestamps,
                            'y': temperatures
                        }
                        
            # Update X-Achse für Zeitanzeige
            self.setup_time_axis()
            
            # Status aktualisieren
            plot_count = len(self.plot_curves)
            self.status_label.setText(f"{plot_count} Kamera(s) angezeigt")
            
            logging.info(f"Plots aktualisiert: {plot_count} Kameras")
            
        except Exception as e:
            logging.error(f"Fehler beim Aktualisieren der Plots: {e}")
            self.status_label.setText("Fehler beim Laden der Daten")
            
    def setup_time_axis(self):
        """Konfiguriert Zeitachse"""
        try:
            # Zeitachse formatieren
            axis = self.plot_widget.getAxis('bottom')
            axis.setTicks([self.get_time_ticks()])
            
        except Exception as e:
            logging.error(f"Fehler bei Zeitachsen-Setup: {e}")
            
    def get_time_ticks(self):
        """Generiert Zeitachsen-Ticks"""
        ticks = []
        
        if not self.plot_data:
            return ticks
            
        # Finde Zeitbereich
        all_timestamps = []
        for data in self.plot_data.values():
            all_timestamps.extend(data['x'])
            
        if not all_timestamps:
            return ticks
            
        min_time = min(all_timestamps)
        max_time = max(all_timestamps)
        
        # Generiere Ticks basierend auf Zeitbereich
        hours = self.get_time_range_hours()
        
        if hours <= 1:
            # Alle 10 Minuten
            interval = 600
        elif hours <= 6:
            # Alle 30 Minuten
            interval = 1800
        elif hours <= 24:
            # Alle 2 Stunden
            interval = 7200
        else:
            # Alle 6 Stunden
            interval = 21600
            
        current_time = min_time
        while current_time <= max_time:
            dt = datetime.fromtimestamp(current_time)
            time_str = dt.strftime("%H:%M" if hours <= 24 else "%d.%m %H:%M")
            ticks.append((current_time, time_str))
            current_time += interval
            
        return ticks
        
    def add_temperature_point(self, ip, temperature):
        """Fügt neuen Temperaturpunkt hinzu"""
        try:
            current_time = datetime.now().timestamp()
            
            # Füge zu Plot-Daten hinzu
            if ip not in self.plot_data:
                self.plot_data[ip] = {'x': [], 'y': []}
                
            self.plot_data[ip]['x'].append(current_time)
            self.plot_data[ip]['y'].append(temperature)
            
            # Begrenze Anzahl Punkte für Performance
            max_points = 1000
            if len(self.plot_data[ip]['x']) > max_points:
                self.plot_data[ip]['x'] = self.plot_data[ip]['x'][-max_points:]
                self.plot_data[ip]['y'] = self.plot_data[ip]['y'][-max_points:]
                
            # Update Plot falls vorhanden
            if ip in self.plot_curves:
                curve = self.plot_curves[ip]
                curve.setData(self.plot_data[ip]['x'], self.plot_data[ip]['y'])
                
        except Exception as e:
            logging.error(f"Fehler beim Hinzufügen des Temperaturpunkts: {e}")
            
    def update_plots(self):
        """Periodisches Update der Plots"""
        # Nur bei Live-Anzeige (letzte Stunde/6 Stunden)
        if self.get_time_range_hours() <= 6:
            self.refresh_plots()
