import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, Tuple, Optional
from .config import DATABASE_PATH, ensure_data_directory

class TemperatureDatabase:
    """Datenbank für Temperaturaufzeichnungen"""
    
    def __init__(self):
        ensure_data_directory()
        self.db_path = DATABASE_PATH
        self.init_database()
        
    def init_database(self):
        """Initialisiert die Datenbank-Tabellen"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Tabelle für Kamera-Informationen
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS cameras (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ip_address TEXT UNIQUE NOT NULL,
                        model TEXT,
                        serial_number TEXT,
                        name TEXT,
                        active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Tabelle für Temperaturmessungen
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS temperature_readings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        camera_id INTEGER,
                        temperature REAL NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (camera_id) REFERENCES cameras (id)
                    )
                ''')
                
                # Index für bessere Performance
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_temp_timestamp 
                    ON temperature_readings (timestamp)
                ''')
                
                conn.commit()
                logging.info("Datenbank erfolgreich initialisiert")
                
        except sqlite3.Error as e:
            logging.error(f"Datenbankfehler bei Initialisierung: {e}")
            
    def add_camera(self, ip_address: str, model: str = "", serial_number: str = "", name: str = "") -> Optional[int]:
        """Fügt neue Kamera hinzu"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO cameras (ip_address, model, serial_number, name)
                    VALUES (?, ?, ?, ?)
                ''', (ip_address, model, serial_number, name))
                conn.commit()
                return cursor.lastrowid
        except sqlite3.Error as e:
            logging.error(f"Fehler beim Hinzufügen der Kamera {ip_address}: {e}")
            return None
            
    def get_cameras(self, active_only: bool = True) -> List[Tuple]:
        """Gibt alle Kameras zurück"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                if active_only:
                    cursor.execute('SELECT * FROM cameras WHERE active = 1')
                else:
                    cursor.execute('SELECT * FROM cameras')
                return cursor.fetchall()
        except sqlite3.Error as e:
            logging.error(f"Fehler beim Abrufen der Kameras: {e}")
            return []
            
    def add_temperature_reading(self, camera_id: int, temperature: float):
        """Fügt Temperaturmessung hinzu"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO temperature_readings (camera_id, temperature)
                    VALUES (?, ?)
                ''', (camera_id, temperature))
                conn.commit()
                logging.debug(f"Temperatur {temperature}°C für Kamera {camera_id} gespeichert")
        except sqlite3.Error as e:
            logging.error(f"Fehler beim Speichern der Temperatur: {e}")
            
    def get_temperature_history(self, camera_id: int, hours: int = 24) -> List[Tuple]:
        """Gibt Temperaturhistorie zurück"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                start_time = datetime.now() - timedelta(hours=hours)
                start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute('''
                    SELECT timestamp, temperature FROM temperature_readings
                    WHERE camera_id = ? AND timestamp >= ?
                    ORDER BY timestamp
                ''', (camera_id, start_time_str))
                return cursor.fetchall()
        except sqlite3.Error as e:
            logging.error(f"Fehler beim Abrufen der Temperaturhistorie: {e}")
            return []
            
    def cleanup_old_data(self, days: int = 30):
        """Löscht alte Daten"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cutoff_date = datetime.now() - timedelta(days=days)
                cutoff_date_str = cutoff_date.strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute('''
                    DELETE FROM temperature_readings WHERE timestamp < ?
                ''', (cutoff_date_str,))
                deleted_count = cursor.rowcount
                conn.commit()
                logging.info(f"{deleted_count} alte Temperaturmessungen gelöscht")
        except sqlite3.Error as e:
            logging.error(f"Fehler beim Bereinigen alter Daten: {e}")
