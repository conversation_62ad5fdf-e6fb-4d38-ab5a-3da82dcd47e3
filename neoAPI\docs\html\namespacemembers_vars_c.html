<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_c"></a>- c -</h3><ul>
<li>CalibrationMatrixColorSelector_Blue
: <a class="el" href="a00091.html#a60c0725f3c69beda7aeffe274ab2b46c">neoapi</a>
</li>
<li>CalibrationMatrixColorSelector_Green
: <a class="el" href="a00091.html#a49f95bf8104d3daf6c680439352a94f8">neoapi</a>
</li>
<li>CalibrationMatrixColorSelector_Red
: <a class="el" href="a00091.html#a5b6adb898253e84d34779273bfc1a612">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain00
: <a class="el" href="a00091.html#ab61475d64b6e433a567e55411a5ea451">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain01
: <a class="el" href="a00091.html#ace5df3b96d0d9c62ba07927a9c9edcf8">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain02
: <a class="el" href="a00091.html#ad96c82bb998cd088d8c8763afd75da80">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain03
: <a class="el" href="a00091.html#a4019c57cf39cace3d8294de9b92737aa">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain10
: <a class="el" href="a00091.html#aee56ef48562c8ce9cfd2ec2c1874987e">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain11
: <a class="el" href="a00091.html#afb10562ba4c630fccfb02a70df61a884">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain12
: <a class="el" href="a00091.html#ab647cc41d6152cd5e310e1cc7f30d03d">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain13
: <a class="el" href="a00091.html#a1894c59b622bd9a4828fdb233d1a0a38">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain20
: <a class="el" href="a00091.html#a04951fd4eb55970aaaf345b947c7116f">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain21
: <a class="el" href="a00091.html#a4c1058b77944571bfc7834dbad3591c6">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain22
: <a class="el" href="a00091.html#a5c4ca348fe814d7cdda20bef2097f3e5">neoapi</a>
</li>
<li>CalibrationMatrixValueSelector_Gain23
: <a class="el" href="a00091.html#ab80234024e12fce6c1ec1f479c379a90">neoapi</a>
</li>
<li>ChunkSelector_Binning
: <a class="el" href="a00091.html#a08f9df10f965146c6724c7756a514f43">neoapi</a>
</li>
<li>ChunkSelector_BinningHorizontal
: <a class="el" href="a00091.html#a1329218ddf068a3a46992f52eb4f5ca7">neoapi</a>
</li>
<li>ChunkSelector_BinningVertical
: <a class="el" href="a00091.html#aba87fa600f285bd512d701e24ad2b204">neoapi</a>
</li>
<li>ChunkSelector_BlackLevel
: <a class="el" href="a00091.html#a33a60bfad9addc17263b19c3039b2327">neoapi</a>
</li>
<li>ChunkSelector_BoSequencerEnable
: <a class="el" href="a00091.html#aebd74f3a9ff1bc7886523dd78dbd8cb0">neoapi</a>
</li>
<li>ChunkSelector_ComponentID
: <a class="el" href="a00091.html#ad7f149f10589e0b3b8025c24b70682b0">neoapi</a>
</li>
<li>ChunkSelector_ComponentIDValue
: <a class="el" href="a00091.html#a3117ced055e4d5e9a560029d54893b9a">neoapi</a>
</li>
<li>ChunkSelector_CounterValue
: <a class="el" href="a00091.html#a8bd753c94135235461baa42b49571a47">neoapi</a>
</li>
<li>ChunkSelector_DecimationHorizontal
: <a class="el" href="a00091.html#a9e0892a36312f44a981b50e8873b0a01">neoapi</a>
</li>
<li>ChunkSelector_DecimationVertical
: <a class="el" href="a00091.html#a6b86f07b784753de0d08715fc64ec064">neoapi</a>
</li>
<li>ChunkSelector_DeviceTemperature
: <a class="el" href="a00091.html#ab13fc86906e355276dd0e04950e932c7">neoapi</a>
</li>
<li>ChunkSelector_EncoderStatusValue
: <a class="el" href="a00091.html#a6aa4a00a3aa4c71bd3fb652a84df41c2">neoapi</a>
</li>
<li>ChunkSelector_EncoderValue
: <a class="el" href="a00091.html#acc60790fd58b0e99a29f15fc7789c83e">neoapi</a>
</li>
<li>ChunkSelector_ExposureTime
: <a class="el" href="a00091.html#add10bf8dde9b95951db2d5446e4ac81d">neoapi</a>
</li>
<li>ChunkSelector_FrameID
: <a class="el" href="a00091.html#a257db518064c21779f2f809046b50d96">neoapi</a>
</li>
<li>ChunkSelector_Gain
: <a class="el" href="a00091.html#a127d1aae4517a6a7c16da5bbb52ae322">neoapi</a>
</li>
<li>ChunkSelector_GroupIDValue
: <a class="el" href="a00091.html#a8aa5abc13b729fd11ac8b2a9fa971c9c">neoapi</a>
</li>
<li>ChunkSelector_Height
: <a class="el" href="a00091.html#a30565e2ea80cb9a6eaeeca3537b1e49f">neoapi</a>
</li>
<li>ChunkSelector_Image
: <a class="el" href="a00091.html#ac115bc8aaace9db7926c23784f14b5a8">neoapi</a>
</li>
<li>ChunkSelector_ImageControl
: <a class="el" href="a00091.html#a74070a3149a5dff8e47116fbd56ca5b5">neoapi</a>
</li>
<li>ChunkSelector_LinePitch
: <a class="el" href="a00091.html#a4a2ef6ded26adae95b0218a62709cafe">neoapi</a>
</li>
<li>ChunkSelector_LineStatusAll
: <a class="el" href="a00091.html#aa965ff7d2ab4b63323def21f562dd45b">neoapi</a>
</li>
<li>ChunkSelector_OffsetX
: <a class="el" href="a00091.html#acd0b1100ab9c5aebe26df5d78ae15bdd">neoapi</a>
</li>
<li>ChunkSelector_OffsetY
: <a class="el" href="a00091.html#a25810613b6200e68d61e41b7d0bfdca0">neoapi</a>
</li>
<li>ChunkSelector_PixelDynamicRangeMax
: <a class="el" href="a00091.html#a51b80cd5042777cb90a620e51e95e7d0">neoapi</a>
</li>
<li>ChunkSelector_PixelDynamicRangeMin
: <a class="el" href="a00091.html#ac79b87e29e53d30d76a7513b0bb5ca37">neoapi</a>
</li>
<li>ChunkSelector_PixelFormat
: <a class="el" href="a00091.html#a74c77b75bbff0325f943ade3075e44ee">neoapi</a>
</li>
<li>ChunkSelector_RegionID
: <a class="el" href="a00091.html#ab1f55c5f870234979048bd2c193186a4">neoapi</a>
</li>
<li>ChunkSelector_RegionIDValue
: <a class="el" href="a00091.html#aac8cf94273038cc5ba8b33a38192d7c2">neoapi</a>
</li>
<li>ChunkSelector_ReverseX
: <a class="el" href="a00091.html#a34733c1b0bbd0f0a6118eb3f6757a1b7">neoapi</a>
</li>
<li>ChunkSelector_ReverseY
: <a class="el" href="a00091.html#a3167a9568e098ab3e632a6d0e05bedef">neoapi</a>
</li>
<li>ChunkSelector_Scan3dAxisMax
: <a class="el" href="a00091.html#a35ced872dd0ac3f60dc870587a87dfc7">neoapi</a>
</li>
<li>ChunkSelector_Scan3dAxisMin
: <a class="el" href="a00091.html#aeffe153907f0f1fc66af14ed3de5e2a0">neoapi</a>
</li>
<li>ChunkSelector_Scan3dCoordinateOffset
: <a class="el" href="a00091.html#ae04c4b3dab4d7f202ce44c394fe3f9f7">neoapi</a>
</li>
<li>ChunkSelector_Scan3dCoordinateReferenceValue
: <a class="el" href="a00091.html#a5aa423310daa78ebb2f36e4a1ece3ee2">neoapi</a>
</li>
<li>ChunkSelector_Scan3dCoordinateScale
: <a class="el" href="a00091.html#aa571b135cee478775e3ba6578fed9fb9">neoapi</a>
</li>
<li>ChunkSelector_Scan3dCoordinateSystem
: <a class="el" href="a00091.html#a44f1c1d642edd8039283ed9323643571">neoapi</a>
</li>
<li>ChunkSelector_Scan3dCoordinateSystemReference
: <a class="el" href="a00091.html#a1890a00d4582af4924b65abdc389e6bd">neoapi</a>
</li>
<li>ChunkSelector_Scan3dCoordinateTransformValue
: <a class="el" href="a00091.html#a21794a546921b97b7c100b0479d79ce8">neoapi</a>
</li>
<li>ChunkSelector_Scan3dDistanceUnit
: <a class="el" href="a00091.html#a9f49543d1fc943fe3529d95c2551cd59">neoapi</a>
</li>
<li>ChunkSelector_Scan3dInvalidDataFlag
: <a class="el" href="a00091.html#ac1532078c80311c087cf6a2007f5b738">neoapi</a>
</li>
<li>ChunkSelector_Scan3dInvalidDataValue
: <a class="el" href="a00091.html#ae63a6e6d6ebea922b795320b458a3b08">neoapi</a>
</li>
<li>ChunkSelector_Scan3dOutputMode
: <a class="el" href="a00091.html#a6bcad578cc00c60fd595a01bdb30dc35">neoapi</a>
</li>
<li>ChunkSelector_SequencerSetActive
: <a class="el" href="a00091.html#a70d3e573583bbf89341bf1e0d5999248">neoapi</a>
</li>
<li>ChunkSelector_SourceID
: <a class="el" href="a00091.html#a6a6a1f9ffea5f7620fe6662578257537">neoapi</a>
</li>
<li>ChunkSelector_SourceIDValue
: <a class="el" href="a00091.html#af78d2f0c5dc356fd5b72dbeaf4ea8a23">neoapi</a>
</li>
<li>ChunkSelector_StreamChannelID
: <a class="el" href="a00091.html#a8fa33f62f58d9c854d24c1e918c3fa10">neoapi</a>
</li>
<li>ChunkSelector_TimerValue
: <a class="el" href="a00091.html#a8b2cff2d51a4d624004d3d711d25c83d">neoapi</a>
</li>
<li>ChunkSelector_Timestamp
: <a class="el" href="a00091.html#a82413bf6f3ced84bbee62d6707716c1f">neoapi</a>
</li>
<li>ChunkSelector_TimestampLatchValue
: <a class="el" href="a00091.html#a55af93ce74174115ccd159dc2621d41b">neoapi</a>
</li>
<li>ChunkSelector_TransferBlockID
: <a class="el" href="a00091.html#a4272ec4ba90acf2c0f3af9167a84dda2">neoapi</a>
</li>
<li>ChunkSelector_TransferQueueCurrentBlockCount
: <a class="el" href="a00091.html#a0f31f7dce48b3bc0a39be75ac2afba79">neoapi</a>
</li>
<li>ChunkSelector_TransferStreamID
: <a class="el" href="a00091.html#ab4e4392f506907e7bda2b05c958bb88d">neoapi</a>
</li>
<li>ChunkSelector_TriggerControl
: <a class="el" href="a00091.html#ac74ab1367a972e50aec17a3846b130ad">neoapi</a>
</li>
<li>ChunkSelector_TriggerID
: <a class="el" href="a00091.html#a037e8205f2ea0b04d4f949469cb5f858">neoapi</a>
</li>
<li>ChunkSelector_Width
: <a class="el" href="a00091.html#a913ddf898bde7517e0c573309a23bd04">neoapi</a>
</li>
<li>ClConfiguration_Base
: <a class="el" href="a00091.html#a437d7803ec27f24ef9ba7f7030ea27d4">neoapi</a>
</li>
<li>ClConfiguration_DualBase
: <a class="el" href="a00091.html#a24c46c40b1b19ae2b587465fed2dd47e">neoapi</a>
</li>
<li>ClConfiguration_EightyBit
: <a class="el" href="a00091.html#a27cd5bce856d8fe6d651053bff21e10f">neoapi</a>
</li>
<li>ClConfiguration_Full
: <a class="el" href="a00091.html#aaa7b3488119c1acda1b41a2a57d2771b">neoapi</a>
</li>
<li>ClConfiguration_Medium
: <a class="el" href="a00091.html#a1cb7dc0a076f1baa240a23732d6262e6">neoapi</a>
</li>
<li>ClTimeSlotsCount_One
: <a class="el" href="a00091.html#a4afa8014402d7cbf8e98be4192b4318e">neoapi</a>
</li>
<li>ClTimeSlotsCount_Three
: <a class="el" href="a00091.html#a17394bc47ddb007f2ec334935afddc79">neoapi</a>
</li>
<li>ClTimeSlotsCount_Two
: <a class="el" href="a00091.html#ac70db2e7c20649833522d8b215b780e5">neoapi</a>
</li>
<li>ColorTransformationAuto_Continuous
: <a class="el" href="a00091.html#a971303df815901943c7184ba6f04e00b">neoapi</a>
</li>
<li>ColorTransformationAuto_Off
: <a class="el" href="a00091.html#a0b40345aaa14c197c70e6017dd1801c0">neoapi</a>
</li>
<li>ColorTransformationAuto_Once
: <a class="el" href="a00091.html#a022ce588f702ea22629d0e04cf258940">neoapi</a>
</li>
<li>ColorTransformationFactoryListSelector_OptimizedMatrixFor3000K
: <a class="el" href="a00091.html#a2e58bbc712aae13500ac89a5e7f9f4b2">neoapi</a>
</li>
<li>ColorTransformationFactoryListSelector_OptimizedMatrixFor3200K
: <a class="el" href="a00091.html#a1789fdd255d65abba73ae35a2930d261">neoapi</a>
</li>
<li>ColorTransformationFactoryListSelector_OptimizedMatrixFor5000K
: <a class="el" href="a00091.html#a6c1524a6abb885a942377fa653af81c6">neoapi</a>
</li>
<li>ColorTransformationFactoryListSelector_OptimizedMatrixFor5600K
: <a class="el" href="a00091.html#aec54a9a800b0f2ed0ec1d5bdb5c01c9a">neoapi</a>
</li>
<li>ColorTransformationFactoryListSelector_OptimizedMatrixFor6500K
: <a class="el" href="a00091.html#ae8a051220afcdcd2f2bc8efc150d6844">neoapi</a>
</li>
<li>ColorTransformationSelector_RGBtoRGB
: <a class="el" href="a00091.html#aad487713f8b2ff2b6e4f684f09622ee8">neoapi</a>
</li>
<li>ColorTransformationSelector_RGBtoYUV
: <a class="el" href="a00091.html#a2e9a8d4a39f0177e92b92567d4865204">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Gain00
: <a class="el" href="a00091.html#a4f23bab8103f5b83730fd83bc7c4a0c6">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Gain01
: <a class="el" href="a00091.html#af2566816247250d7549b1da223401454">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Gain02
: <a class="el" href="a00091.html#af1120999f343dc0df62e3c23d05134b8">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Gain10
: <a class="el" href="a00091.html#a742d5853f5d196b64675e4ed7069f0da">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Gain11
: <a class="el" href="a00091.html#aa89821b6f4e512c57b437eeb5982adac">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Gain12
: <a class="el" href="a00091.html#a2ce5332587ddcd390c6094c05d21eeb5">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Gain20
: <a class="el" href="a00091.html#ae466b8d4968faa475c14b4e86a1b6c30">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Gain21
: <a class="el" href="a00091.html#a611d5a6677b009df010c9a9c5de71c2d">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Gain22
: <a class="el" href="a00091.html#a05bc74c7bf655c0a7f8cbf85992e4b9c">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Offset0
: <a class="el" href="a00091.html#a36839d8ae1c7a47194428f3b7450f71b">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Offset1
: <a class="el" href="a00091.html#aa97948a32c94f654b630b0811049ef19">neoapi</a>
</li>
<li>ColorTransformationValueSelector_Offset2
: <a class="el" href="a00091.html#a453e936c42258d43b10d774a71b66f4b">neoapi</a>
</li>
<li>ComponentSelector_Confidence
: <a class="el" href="a00091.html#a9f89399847d3783e00cf5766179e4532">neoapi</a>
</li>
<li>ComponentSelector_Disparity
: <a class="el" href="a00091.html#aaee45ccfd40d88716e944962c0830e88">neoapi</a>
</li>
<li>ComponentSelector_Infrared
: <a class="el" href="a00091.html#a485bfb15403c93ae86bdceda6d54717f">neoapi</a>
</li>
<li>ComponentSelector_Intensity
: <a class="el" href="a00091.html#a56b215395633984ae61285242b97463e">neoapi</a>
</li>
<li>ComponentSelector_Multispectral
: <a class="el" href="a00091.html#afb705bbfd755e6a5fe142105efedbdfb">neoapi</a>
</li>
<li>ComponentSelector_PolarizedRaw
: <a class="el" href="a00091.html#a01b1ee1823fc8516b3cfff5cd031705d">neoapi</a>
</li>
<li>ComponentSelector_Range
: <a class="el" href="a00091.html#a16187cf34b99d30b1065e5bc1c0f2b02">neoapi</a>
</li>
<li>ComponentSelector_Reflectance
: <a class="el" href="a00091.html#a4213f4f473ac11de9e617eb5d0e814a6">neoapi</a>
</li>
<li>ComponentSelector_Scatter
: <a class="el" href="a00091.html#a9c749d487706e023e5dabd0a81b063df">neoapi</a>
</li>
<li>ComponentSelector_Ultraviolet
: <a class="el" href="a00091.html#a2ce25887820ea739dc2ff2fd8f80348a">neoapi</a>
</li>
<li>CounterEventActivation_AnyEdge
: <a class="el" href="a00091.html#a0033db5bda9e40e82c6ce9fe2de44f58">neoapi</a>
</li>
<li>CounterEventActivation_FallingEdge
: <a class="el" href="a00091.html#a675179f4e9dcad33b44ee9796042c656">neoapi</a>
</li>
<li>CounterEventActivation_RisingEdge
: <a class="el" href="a00091.html#ae92af6fa709a4a63a9c6c4cd7b2fb5fa">neoapi</a>
</li>
<li>CounterEventSource_AcquisitionEnd
: <a class="el" href="a00091.html#adb3592741df49e3053993dcb24fa6e17">neoapi</a>
</li>
<li>CounterEventSource_AcquisitionStart
: <a class="el" href="a00091.html#a05fba913caf44e1fcd41b2b4b3738e1f">neoapi</a>
</li>
<li>CounterEventSource_AcquisitionTrigger
: <a class="el" href="a00091.html#a9f4ead3566e0ba2e6ae33aba59641739">neoapi</a>
</li>
<li>CounterEventSource_AcquisitionTriggerMissed
: <a class="el" href="a00091.html#a90c1a0a61f1e8fe2be03869c708bdc11">neoapi</a>
</li>
<li>CounterEventSource_Action0
: <a class="el" href="a00091.html#a7d17a2e17aee937f660257eeae5ea369">neoapi</a>
</li>
<li>CounterEventSource_Action1
: <a class="el" href="a00091.html#a141ca8529fe62e6bc5763ad1787d12ec">neoapi</a>
</li>
<li>CounterEventSource_Action2
: <a class="el" href="a00091.html#a63058cd051d948d07ad160b0652b8aff">neoapi</a>
</li>
<li>CounterEventSource_Counter0End
: <a class="el" href="a00091.html#af89feabe284cb8b26bef4eb9a808361d">neoapi</a>
</li>
<li>CounterEventSource_Counter0Start
: <a class="el" href="a00091.html#a0d518afa7f4bb86d7d154ff682e81b1e">neoapi</a>
</li>
<li>CounterEventSource_Counter1End
: <a class="el" href="a00091.html#aed11da75b22cd5b889c2e24f5a4c3e3d">neoapi</a>
</li>
<li>CounterEventSource_Counter1Start
: <a class="el" href="a00091.html#a45ad6c37a5826d2c9c8dc1fad12e8e6f">neoapi</a>
</li>
<li>CounterEventSource_Counter2End
: <a class="el" href="a00091.html#ade081f69f2c9ce016acd2dbc988ea0eb">neoapi</a>
</li>
<li>CounterEventSource_Counter2Start
: <a class="el" href="a00091.html#a43012ba0989b4d3bb3100b57be2c1410">neoapi</a>
</li>
<li>CounterEventSource_Encoder0
: <a class="el" href="a00091.html#ae13f590b654210e722056fe2c170d728">neoapi</a>
</li>
<li>CounterEventSource_Encoder1
: <a class="el" href="a00091.html#ad9453f3b1a5bc21af33c08d361bf5139">neoapi</a>
</li>
<li>CounterEventSource_Encoder2
: <a class="el" href="a00091.html#a190bedf57c842d1bce97a7f14a8f75a2">neoapi</a>
</li>
<li>CounterEventSource_ExposureActive
: <a class="el" href="a00091.html#ab223936751e7c20355ad24ef25f9efd6">neoapi</a>
</li>
<li>CounterEventSource_ExposureEnd
: <a class="el" href="a00091.html#a8ea9d62c1cfd36e438dfca41ead5c72b">neoapi</a>
</li>
<li>CounterEventSource_ExposureStart
: <a class="el" href="a00091.html#aef07a1bd400caebd1189db637c4c1f65">neoapi</a>
</li>
<li>CounterEventSource_FrameBurstEnd
: <a class="el" href="a00091.html#a4bb1d37e148a5299b29ea4942f237681">neoapi</a>
</li>
<li>CounterEventSource_FrameBurstStart
: <a class="el" href="a00091.html#a44ccaab96c3a9bc86e81969f264d3316">neoapi</a>
</li>
<li>CounterEventSource_FrameEnd
: <a class="el" href="a00091.html#a4c9abfeb69fdc5db6697b68c003d12d6">neoapi</a>
</li>
<li>CounterEventSource_FrameStart
: <a class="el" href="a00091.html#a44e16dc75060b6a796eb6e24b0664eb0">neoapi</a>
</li>
<li>CounterEventSource_FrameTransferSkipped
: <a class="el" href="a00091.html#a8cfce3bdce5a46c8739247f6da670d4d">neoapi</a>
</li>
<li>CounterEventSource_FrameTrigger
: <a class="el" href="a00091.html#a1d55c7a12934c94ca2912f288b695671">neoapi</a>
</li>
<li>CounterEventSource_FrameTriggerMissed
: <a class="el" href="a00091.html#a572ea81b10f40154d99106f7226fdc93">neoapi</a>
</li>
<li>CounterEventSource_Line0
: <a class="el" href="a00091.html#a872e1711069f0517fe3a965edaefd9fc">neoapi</a>
</li>
<li>CounterEventSource_Line1
: <a class="el" href="a00091.html#a9eecc385ab87c21b98b0743cd46d4229">neoapi</a>
</li>
<li>CounterEventSource_Line2
: <a class="el" href="a00091.html#a39e070d9e1d83bfbe254359bc13b8e3e">neoapi</a>
</li>
<li>CounterEventSource_LineEnd
: <a class="el" href="a00091.html#aa05acaea28055badc6c699abdc198d91">neoapi</a>
</li>
<li>CounterEventSource_LineStart
: <a class="el" href="a00091.html#af687f4e4e7c90fef94f9bdccd729d552">neoapi</a>
</li>
<li>CounterEventSource_LineTrigger
: <a class="el" href="a00091.html#af3c941896d777469ea6284789c9e4e59">neoapi</a>
</li>
<li>CounterEventSource_LineTriggerMissed
: <a class="el" href="a00091.html#a28ff07140c11b1b1a33a5d56dc65ad9e">neoapi</a>
</li>
<li>CounterEventSource_LinkTrigger0
: <a class="el" href="a00091.html#a6287da4ecb4765108b9d4ed17503f999">neoapi</a>
</li>
<li>CounterEventSource_LinkTrigger1
: <a class="el" href="a00091.html#a8878c48f771409470b5c5cdb8e831910">neoapi</a>
</li>
<li>CounterEventSource_LinkTrigger2
: <a class="el" href="a00091.html#a1f9aea59bfb7b86940de210b9cd78856">neoapi</a>
</li>
<li>CounterEventSource_LinkTriggerMissed0
: <a class="el" href="a00091.html#a20d03d1e72dfdcef88ae8300f4486810">neoapi</a>
</li>
<li>CounterEventSource_LinkTriggerMissed1
: <a class="el" href="a00091.html#aa33cf68a7fdcaa27c14525b2b6ef983a">neoapi</a>
</li>
<li>CounterEventSource_LinkTriggerMissed2
: <a class="el" href="a00091.html#a1777a25ff25754e7bbdaf323d3db497d">neoapi</a>
</li>
<li>CounterEventSource_LogicBlock0
: <a class="el" href="a00091.html#a3dc30165c60b8ae850406ce93bfbea7f">neoapi</a>
</li>
<li>CounterEventSource_LogicBlock1
: <a class="el" href="a00091.html#a27b5cf33d1fe122de095e33e38db8581">neoapi</a>
</li>
<li>CounterEventSource_LogicBlock2
: <a class="el" href="a00091.html#aa2cbd329b7ba067668401dafcc09781e">neoapi</a>
</li>
<li>CounterEventSource_Off
: <a class="el" href="a00091.html#a3ff40aa56198ecacd48c7b7a4f4a39c5">neoapi</a>
</li>
<li>CounterEventSource_SoftwareSignal0
: <a class="el" href="a00091.html#accbf152c4f2fc5bd50865b3316fd9246">neoapi</a>
</li>
<li>CounterEventSource_SoftwareSignal1
: <a class="el" href="a00091.html#a0f6371e65b09333d223980fa3521eef4">neoapi</a>
</li>
<li>CounterEventSource_SoftwareSignal2
: <a class="el" href="a00091.html#a052c91bb8941e8d94bf6c1e8b8412ebd">neoapi</a>
</li>
<li>CounterEventSource_Timer0End
: <a class="el" href="a00091.html#ad2124d25a00c5353311a0e52a809bef5">neoapi</a>
</li>
<li>CounterEventSource_Timer0Start
: <a class="el" href="a00091.html#a546f296e04db0c36bc7be5d407f89330">neoapi</a>
</li>
<li>CounterEventSource_Timer1End
: <a class="el" href="a00091.html#a1876ccaad851619e754824b729670793">neoapi</a>
</li>
<li>CounterEventSource_Timer1Start
: <a class="el" href="a00091.html#a79dc99f105953fa6900451d1e3a04504">neoapi</a>
</li>
<li>CounterEventSource_Timer2End
: <a class="el" href="a00091.html#a101b5e08a7c9b785c71835b15a5a12f0">neoapi</a>
</li>
<li>CounterEventSource_Timer2Start
: <a class="el" href="a00091.html#a756a874e1ce9f62ee2d0fa03022f03c2">neoapi</a>
</li>
<li>CounterEventSource_TimestampTick
: <a class="el" href="a00091.html#a4c840c526ed02c866cf8ac6dd056cd01">neoapi</a>
</li>
<li>CounterEventSource_TriggerSkipped
: <a class="el" href="a00091.html#a4f6a3836ae158cfa38120af0a104edb9">neoapi</a>
</li>
<li>CounterResetActivation_AnyEdge
: <a class="el" href="a00091.html#aec49e37e34f091bf5b6a48febef34b49">neoapi</a>
</li>
<li>CounterResetActivation_FallingEdge
: <a class="el" href="a00091.html#a4f9d877b9c464f4fe2fad8c1fb0509cc">neoapi</a>
</li>
<li>CounterResetActivation_LevelHigh
: <a class="el" href="a00091.html#a7d23251913e3dd8d07743d7802491d43">neoapi</a>
</li>
<li>CounterResetActivation_LevelLow
: <a class="el" href="a00091.html#a6d46b7a79a07340a72caadc139a8da5a">neoapi</a>
</li>
<li>CounterResetActivation_RisingEdge
: <a class="el" href="a00091.html#a4b12e61c4bfd317fcfc19f68173024f6">neoapi</a>
</li>
<li>CounterResetSource_AcquisitionEnd
: <a class="el" href="a00091.html#a2423dcac752deaf124823863d6f462e1">neoapi</a>
</li>
<li>CounterResetSource_AcquisitionStart
: <a class="el" href="a00091.html#ac798a895ed697c3ab17d401733c04df0">neoapi</a>
</li>
<li>CounterResetSource_AcquisitionTrigger
: <a class="el" href="a00091.html#ac99782cf4a49629e0a14923f6be120e1">neoapi</a>
</li>
<li>CounterResetSource_AcquisitionTriggerMissed
: <a class="el" href="a00091.html#aefa0ce208c43ea8952d2c357a9bf7f06">neoapi</a>
</li>
<li>CounterResetSource_Action0
: <a class="el" href="a00091.html#a1ca015c22273e0c29f183c3fe5d83df3">neoapi</a>
</li>
<li>CounterResetSource_Action1
: <a class="el" href="a00091.html#a2756dcdc46338c14b4f03ff7159e044b">neoapi</a>
</li>
<li>CounterResetSource_Action2
: <a class="el" href="a00091.html#a1abec9685411a1b14cb568e58a78856c">neoapi</a>
</li>
<li>CounterResetSource_Counter0End
: <a class="el" href="a00091.html#a424a1c12cd549e10882ef517ea0d0535">neoapi</a>
</li>
<li>CounterResetSource_Counter0Start
: <a class="el" href="a00091.html#af7f6751c96423f65a64412fa97278371">neoapi</a>
</li>
<li>CounterResetSource_Counter1End
: <a class="el" href="a00091.html#a196af14026136d8a05a373d1c74d222d">neoapi</a>
</li>
<li>CounterResetSource_Counter1Start
: <a class="el" href="a00091.html#a6038a05086ed0112976fc03969863424">neoapi</a>
</li>
<li>CounterResetSource_Counter2End
: <a class="el" href="a00091.html#a76d8f03cf96583dd4fa70766b9d352e0">neoapi</a>
</li>
<li>CounterResetSource_Counter2Start
: <a class="el" href="a00091.html#ad67578210eddbb283155ef473dc0789c">neoapi</a>
</li>
<li>CounterResetSource_CounterTrigger
: <a class="el" href="a00091.html#ab5bbc92f6b94c37bf8194401415074d5">neoapi</a>
</li>
<li>CounterResetSource_Encoder0
: <a class="el" href="a00091.html#acc09a922c8429b2c7904cbeff67aafe0">neoapi</a>
</li>
<li>CounterResetSource_Encoder1
: <a class="el" href="a00091.html#ab6166c13a05b0146748bf4d450828560">neoapi</a>
</li>
<li>CounterResetSource_Encoder2
: <a class="el" href="a00091.html#a6bcfb0fd822511b600c847fef553045f">neoapi</a>
</li>
<li>CounterResetSource_ExposureEnd
: <a class="el" href="a00091.html#aa444db77a2f18157fd893a318dc4153c">neoapi</a>
</li>
<li>CounterResetSource_ExposureStart
: <a class="el" href="a00091.html#a4a68593e3f17ce43da88d9cd7718f96f">neoapi</a>
</li>
<li>CounterResetSource_FrameEnd
: <a class="el" href="a00091.html#a56f0ffdf5cc330f109bba3818c8179c9">neoapi</a>
</li>
<li>CounterResetSource_FrameStart
: <a class="el" href="a00091.html#a7bfcaff4793869fa666bf1bc47516a56">neoapi</a>
</li>
<li>CounterResetSource_FrameTrigger
: <a class="el" href="a00091.html#ae271b08a4847d7a20b36d61306276ef1">neoapi</a>
</li>
<li>CounterResetSource_FrameTriggerMissed
: <a class="el" href="a00091.html#aed31fc6c1f47c8f450377db4d84f66c6">neoapi</a>
</li>
<li>CounterResetSource_Line0
: <a class="el" href="a00091.html#a694a9fdc198cafb926854805a34ba785">neoapi</a>
</li>
<li>CounterResetSource_Line1
: <a class="el" href="a00091.html#a3986c640a61b97827393476f4cb9c325">neoapi</a>
</li>
<li>CounterResetSource_Line2
: <a class="el" href="a00091.html#ab58576477182c30ea5d772e240b1c3c8">neoapi</a>
</li>
<li>CounterResetSource_LineEnd
: <a class="el" href="a00091.html#a7d2c2c3f6eaefab99739971c1eec5107">neoapi</a>
</li>
<li>CounterResetSource_LineStart
: <a class="el" href="a00091.html#a30324481a1017f2016eff0fbac97c81d">neoapi</a>
</li>
<li>CounterResetSource_LineTrigger
: <a class="el" href="a00091.html#a710609769c299c747f712b876953c3c7">neoapi</a>
</li>
<li>CounterResetSource_LineTriggerMissed
: <a class="el" href="a00091.html#aa39c582f841ac8ff2398efd2753ac71c">neoapi</a>
</li>
<li>CounterResetSource_LinkTrigger0
: <a class="el" href="a00091.html#a739610d62b87579f213c56cdeaeda5bf">neoapi</a>
</li>
<li>CounterResetSource_LinkTrigger1
: <a class="el" href="a00091.html#a67d3feab50d835c7b27e34ac49b007a8">neoapi</a>
</li>
<li>CounterResetSource_LinkTrigger2
: <a class="el" href="a00091.html#ae238929cb48a5cebaebf807cf6e5a2a5">neoapi</a>
</li>
<li>CounterResetSource_LogicBlock0
: <a class="el" href="a00091.html#a531d1e1c20134558b3c8d4a4f4e5abc9">neoapi</a>
</li>
<li>CounterResetSource_LogicBlock1
: <a class="el" href="a00091.html#acd3954b0a05dbbf5125eb1a2b72a5344">neoapi</a>
</li>
<li>CounterResetSource_LogicBlock2
: <a class="el" href="a00091.html#a207af8250677029b0596cf056f847c06">neoapi</a>
</li>
<li>CounterResetSource_Off
: <a class="el" href="a00091.html#ae8e135f01d86f66e5e78ecf63925f914">neoapi</a>
</li>
<li>CounterResetSource_SoftwareSignal0
: <a class="el" href="a00091.html#a92452fb9b5792e9e1f4e55db699af7bf">neoapi</a>
</li>
<li>CounterResetSource_SoftwareSignal1
: <a class="el" href="a00091.html#a7e6f47fde9b9e296d371e0c1b536b688">neoapi</a>
</li>
<li>CounterResetSource_SoftwareSignal2
: <a class="el" href="a00091.html#ad35b5979a31ed3029771988e042a2e2e">neoapi</a>
</li>
<li>CounterResetSource_Timer0End
: <a class="el" href="a00091.html#a8f512ac6d569cc4761c5161b41b20a56">neoapi</a>
</li>
<li>CounterResetSource_Timer0Start
: <a class="el" href="a00091.html#ad2bcf96c650e7565bca32909be91a48f">neoapi</a>
</li>
<li>CounterResetSource_Timer1End
: <a class="el" href="a00091.html#a0f3bc77ff841588f2e9eec3056b32dd6">neoapi</a>
</li>
<li>CounterResetSource_Timer1Start
: <a class="el" href="a00091.html#a5b6e0741bfe27ecfab5fc7864df13b03">neoapi</a>
</li>
<li>CounterResetSource_Timer2End
: <a class="el" href="a00091.html#a4a90346b3919ca81b4d2c362be797ccc">neoapi</a>
</li>
<li>CounterResetSource_Timer2Start
: <a class="el" href="a00091.html#a6aa402e18c29479d078d685ed792dba3">neoapi</a>
</li>
<li>CounterResetSource_UserOutput0
: <a class="el" href="a00091.html#a6c18de4c674391b54c2c2c1e0e5d2cbc">neoapi</a>
</li>
<li>CounterResetSource_UserOutput1
: <a class="el" href="a00091.html#a637b31182789b474a315ab0f1469fa26">neoapi</a>
</li>
<li>CounterResetSource_UserOutput2
: <a class="el" href="a00091.html#afeaa052175f58e2a28c1089a928bfb8b">neoapi</a>
</li>
<li>CounterSelector_Counter0
: <a class="el" href="a00091.html#a7aacc9da1009961173012f3158163e9d">neoapi</a>
</li>
<li>CounterSelector_Counter1
: <a class="el" href="a00091.html#a830aa620f30c68c7aaa4cfa1f12c0583">neoapi</a>
</li>
<li>CounterSelector_Counter2
: <a class="el" href="a00091.html#a253b70e33897c0cb12ebbf4f6718c4c4">neoapi</a>
</li>
<li>CustomDataConfigurationMode_Off
: <a class="el" href="a00091.html#adf39ad9f680692125afd3eb2f7b036b3">neoapi</a>
</li>
<li>CustomDataConfigurationMode_On
: <a class="el" href="a00091.html#a1e9359a03843aa9d2572f906eefaee57">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
