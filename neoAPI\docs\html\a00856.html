<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Member List</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00859.html">CamBase</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">neoapi.CamBase Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="a00859.html">neoapi.CamBase</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a32528a3186cba45869859c745c61b524">__del__</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#ab01cb8f7a559f37743af7ab7a9365687">__init__</a>(self, *args)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#ad62bf7e7209b986b3f0140301273b202">AddUserBuffer</a>(self, &quot;BufferBase&quot; buffer)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a9e1d7d205673d544624dffca42d14fca">ClearEvents</a>(self, *args)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#afcc7b65f478ca08c79ab839b607efb6a">ClearImages</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#abacf893cc84a9a97b55a6c0be753c10d">ClearPnPEvents</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a723ec46c77f3b734328a9f40e7aa1c19">Connect</a>(self, *args)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a650bf056805ae3c08dc53a48e1625fba">DisableChunk</a>(self, *args)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a8f6803711004617ffda5590e5e7f95b6">DisableEvent</a>(self, &quot;str&quot; name)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#adb6e79938a65f05b9605b675956005ac">DisableEventCallback</a>(self, *args)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a8fa517e4a1d9fe8ee897e83de308e512">DisableImageCallback</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#ada7b8d016968405a436c425caa46ce41">DisablePnPEventCallback</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a1b81f0c2270ab38b0cff03219249b301">Disconnect</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#aef9d59bf29336a3550604553665cd420">EnableChunk</a>(self, *args)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a5e4bd8f41eb373eb8af1d4610fdd56e5">EnableEvent</a>(self, &quot;str&quot; name, &quot;int&quot; max_queuesize=1000)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a3b845c798b1c2c7da09234d8d2c42c0b">EnableEventCallback</a>(self, callback, name=&quot;&quot;)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a8b38e28876930ca8f58eac475b893d74">EnableImageCallback</a>(self, callback)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a7973a93df5f8f705a5eca0d8289edef5">EnablePnPEventCallback</a>(self, callback)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a45c8a4adb6d7ea82fb99cf9af011bd95">Execute</a>(self, &quot;str&quot; name)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#acd1be2e807ab576178a378e04353a691">GetAdjustFeatureValueMode</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a535fb59548f1b8613979a9d3cd261371">GetAvailableChunks</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a96e7621be71177bc2bd79bb266d03d3c">GetAvailableEvents</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a5b34c75756c475826f4eafa59de2b3e6">GetEnabledEvents</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a457e6738d020688c89c4f76f6283b538">GetEvent</a>(self, *args)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a583f268ca217c5980a73142274850449">GetFeature</a>(self, &quot;str&quot; name)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#ae93ad58886f8665f885b777d27e9532f">GetFeatureList</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51">GetImage</a>(self, &quot;int&quot; timeout=400)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#ad635657fae4824cbc8df3a1aee5ad7eb">GetImageBufferCount</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a2692cee5025c780d021b782e125c9a35">GetImageBufferCycleCount</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#ae92af3924d145c8a26c18558d279fa9b">GetImageInfo</a>(self, &quot;int&quot; index)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a53107564ffb57385f19127eea6435f64">GetImages</a>(self, 'int' timeout=400)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a1dabab0b7beb6aff03dcdb8f13ef370d">GetImagesPerBuffer</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a6dc8972d4d0bdc70a9022b18f8cd1b00">GetInfo</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#aca5a01fb66bf17b8f9cbe250d3f3dc14">GetLibraryVersion</a>()</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a4b3accbf3321c31a5fceb7fc0ab9de87">GetOfflineCount</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a5533479e8e16bc1a5aea0a5c7a2dde5c">GetPnPEvent</a>(self, &quot;int&quot; timeout=400)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#aa15c1f6e02b987b7a3fe7b1300f9e6f9">GetRuntimeInfoList</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a2f1286b198a986b461e3229c784b0981">GetSynchronFeatureMode</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a8158f4ce4112a5c886b0258557e1bb79">GetUserBufferMode</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a374c5e3200cab8d7464bc907811ba035">HasFeature</a>(self, &quot;str&quot; name)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#ad5d532323d90fcb245a0c2e9b4079d26">IsConnected</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#aaaf166323ef78a64f75e02be7a32d8fa">IsOnline</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a7e02b28ffff318ae93a6c0a37b5de38a">IsReadable</a>(self, &quot;str&quot; name)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a16d699f52cbdfa7b8f2ff2ed08149839">IsStreaming</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#afc86d804d99b2d9b11ce5c685548b24b">IsWritable</a>(self, &quot;str&quot; name)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a10265873a47b6e513c5ea997f17baa54">ReadMemory</a>(self, &quot;int&quot; address, &quot;bytearray&quot; buffer, &quot;int&quot; length)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#ae55b7e85dbf2e1553af0fa906d709b4d">RevokeUserBuffer</a>(self, &quot;BufferBase&quot; buffer)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#ace4716e7126adb8e85bcc5e928ce87a7">SetAdjustFeatureValueMode</a>(self, &quot;bool&quot; adjust=True)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a3e8944a4aa1f5c87a9b95b391d278945">SetFeature</a>(self, *args)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#ab5ad7ce815a8397b5073767ed344a896">SetImageBufferCount</a>(self, &quot;int&quot; buffercount=10)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#ad6dc663d08df99799ec4f89a70a8bf89">SetImageBufferCycleCount</a>(self, &quot;int&quot; cyclecount=1)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a068ff88af2a49cfade6f63f23339251e">SetSynchronFeatureMode</a>(self, &quot;bool&quot; synchronous=True)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a8c378141255469a625d0005afcce88c6">SetUserBufferMode</a>(self, &quot;bool&quot; user_buffers=True)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a79a59c8640be69d19e9e312694fbec65">StartStreaming</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#abd8483f382bcb2857267e184ef661af0">StopStreaming</a>(self)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a95029eaabd281118ff8ac42556f056aa">thisown</a></td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="a00859.html#a5c9b37d460925587c86abc5096d9b657">WriteFeatureStack</a>(self, &quot;FeatureStack&quot; featurestack)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="a00859.html#a6e0dbde51b16e2ceec4d9cf0da0f5700">WriteMemory</a>(self, &quot;int&quot; address, &quot;bytearray&quot; buffer, &quot;int&quot; length)</td><td class="entry"><a class="el" href="a00859.html">neoapi.CamBase</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
