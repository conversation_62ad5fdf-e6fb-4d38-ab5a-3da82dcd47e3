<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members - Properties</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_v"></a>- v -</h3><ul>
<li>value
: <a class="el" href="a00839.html#ae7e5a89f8c029e4d0604bbe48ad9105c">neoapi.BoolFeature</a>
, <a class="el" href="a00095.html#a6cf8a6de25f494872ea28e538a4b1f6d">neoapi.CAcquisitionMode</a>
, <a class="el" href="a00099.html#ae01f4cf1b873f9ef42084aa5a698450c">neoapi.CAcquisitionStatusSelector</a>
, <a class="el" href="a00103.html#a4678ccf1d463cdc85352b0266f69c995">neoapi.CApertureStatus</a>
, <a class="el" href="a00107.html#acd5934c77d44e1f976fef9c712eebc48">neoapi.CAutoFeatureRegionMode</a>
, <a class="el" href="a00111.html#a70501d12a419c2b79dfe75698317625c">neoapi.CAutoFeatureRegionReference</a>
, <a class="el" href="a00115.html#a4bc17c4d74645662ad2aa2d7f18545d4">neoapi.CAutoFeatureRegionSelector</a>
, <a class="el" href="a00123.html#a076e12637d8991b8dee1299fa3e90b99">neoapi.CBalanceWhiteAuto</a>
, <a class="el" href="a00127.html#aff6b90f2f558617ab45b42a277b19ec6">neoapi.CBalanceWhiteAutoStatus</a>
, <a class="el" href="a00131.html#ab862b1a26e552a649effbc16e974aba0">neoapi.CBaudrate</a>
, <a class="el" href="a00135.html#add34fb00c5bfd80ad06496d2e3155b57">neoapi.CBinningHorizontalMode</a>
, <a class="el" href="a00139.html#ae75c31ac82fe4bf34dba63a39d498903">neoapi.CBinningSelector</a>
, <a class="el" href="a00143.html#aaf767de1d04551ba38d0192129ca31b4">neoapi.CBinningVerticalMode</a>
, <a class="el" href="a00147.html#a67b7a55e260d6d69fc9f35692e69aefc">neoapi.CBlackLevelSelector</a>
, <a class="el" href="a00151.html#a5429dda3323aae996074979af5874424">neoapi.CBlackSunSuppression</a>
, <a class="el" href="a00715.html#a9a1ff3eb9fb907751e9743684a61fe37">neoapi.CboCalibrationDataConfigurationMode</a>
, <a class="el" href="a00719.html#a1b8e265058a6c0c4b68548e3e27afb93">neoapi.CboCalibrationMatrixSelector</a>
, <a class="el" href="a00723.html#ab46d31ee41ab7d2b51ca1b3576653326">neoapi.CboCalibrationMatrixValueSelector</a>
, <a class="el" href="a00727.html#a0e30ce604453db14d67bccd464db11d2">neoapi.CboCalibrationVectorSelector</a>
, <a class="el" href="a00731.html#a8fa264d602594de23b8e912759700f3e">neoapi.CboCalibrationVectorValueSelector</a>
, <a class="el" href="a00735.html#aefe265f581385a6ec341806eb7da99e5">neoapi.CboGeometryDistortionValueSelector</a>
, <a class="el" href="a00119.html#a178e5eaff57ecb747ab56e1ffe02ea4c">neoapi.CBOPFShift</a>
, <a class="el" href="a00155.html#a31d09863682b45a2ce3b215fa2356e54">neoapi.CBoSequencerEnable</a>
, <a class="el" href="a00159.html#a230ee295efd444dbfe40decb624a8d7a">neoapi.CBoSequencerIOSelector</a>
, <a class="el" href="a00163.html#a6804e70703f15ccba1f7ee92713adc3b">neoapi.CBoSequencerMode</a>
, <a class="el" href="a00167.html#a11212c2ef546e2b829646b8844b1ec60">neoapi.CBoSequencerSensorDigitizationTaps</a>
, <a class="el" href="a00171.html#af146823f732288a67ff5903db83ffd89">neoapi.CBoSequencerStart</a>
, <a class="el" href="a00739.html#a561a00f9335d832055b8d3ee30bcbc3b">neoapi.CboSerialConfigBaudRate</a>
, <a class="el" href="a00743.html#a6a9d40094404cbd09761a9dc2d9da41f">neoapi.CboSerialConfigDataBits</a>
, <a class="el" href="a00747.html#a183d703042e4cd8204823f9076b14b6c">neoapi.CboSerialConfigParity</a>
, <a class="el" href="a00751.html#aae5bfb7a443fbfa34e47124fb9271deb">neoapi.CboSerialConfigStopBits</a>
, <a class="el" href="a00755.html#adbbd1551e58faceea6849166ac765981">neoapi.CboSerialMode</a>
, <a class="el" href="a00759.html#ad17b65e2eafbb2a521bb50ef925a6f64">neoapi.CboSerialSelector</a>
, <a class="el" href="a00175.html#a78b276b50289b86dc170148aaad82b61">neoapi.CBrightnessAutoPriority</a>
, <a class="el" href="a00179.html#a844e493eb5a6010b5e4d7a6733901c28">neoapi.CBrightnessCorrection</a>
, <a class="el" href="a00183.html#a5612d3b828fa48f9555f23126bb18f7a">neoapi.CCalibrationMatrixColorSelector</a>
, <a class="el" href="a00187.html#aea2b00f2d9d3b545ec2944208d248b85">neoapi.CCalibrationMatrixValueSelector</a>
, <a class="el" href="a00191.html#a2a80302d725c4174821049af0760ea88">neoapi.CChunkSelector</a>
, <a class="el" href="a00195.html#af4d92fc809b175e72b14b4275e27d6d6">neoapi.CClConfiguration</a>
, <a class="el" href="a00199.html#ac9c3af318c902ca18855f8da0a31d2d0">neoapi.CClTimeSlotsCount</a>
, <a class="el" href="a00203.html#a47ee6f62d9a1388a9c7de1151dc525f3">neoapi.CColorTransformationAuto</a>
, <a class="el" href="a00207.html#a57dbcb3b4d38a34b7de0ecf423c2bf94">neoapi.CColorTransformationFactoryListSelector</a>
, <a class="el" href="a00211.html#adfd7dd113a537e22037a9fec45d5b4c0">neoapi.CColorTransformationSelector</a>
, <a class="el" href="a00215.html#a72e139fdc225321919b176703598c998">neoapi.CColorTransformationValueSelector</a>
, <a class="el" href="a00219.html#a23e51e0304f73e0f843e28c57a4ca569">neoapi.CComponentSelector</a>
, <a class="el" href="a00223.html#a72f2e4ba5295a5bcae5e2cb73edeae2e">neoapi.CCounterEventActivation</a>
, <a class="el" href="a00227.html#a2146bd536e207dad4536b58d5f5ae6a1">neoapi.CCounterEventSource</a>
, <a class="el" href="a00231.html#a4075629048dafcf6ba4efef8b3356bff">neoapi.CCounterResetActivation</a>
, <a class="el" href="a00235.html#a1a5d5e85eb06665c57897ed5bf1f67bb">neoapi.CCounterResetSource</a>
, <a class="el" href="a00239.html#a1c50904aeb4a86f97b59ee9621525c43">neoapi.CCounterSelector</a>
, <a class="el" href="a00243.html#a4e2b3661e43f7b6c64ac0062014c4c20">neoapi.CCustomDataConfigurationMode</a>
, <a class="el" href="a00247.html#aca9bc552af809604408de42e14bc6a9c">neoapi.CDecimationHorizontalMode</a>
, <a class="el" href="a00251.html#a03c6cb579f4737d05d967587835a500c">neoapi.CDecimationVerticalMode</a>
, <a class="el" href="a00255.html#a25bc19087b8ca1925240ab73e5ab1f31">neoapi.CDefectPixelListSelector</a>
, <a class="el" href="a00259.html#ac3ab7c8661ab9ae29355bef96d28f4f9">neoapi.CDeviceCharacterSet</a>
, <a class="el" href="a00263.html#a4d368155c60b52151cf3d4eaa6707851">neoapi.CDeviceClockSelector</a>
, <a class="el" href="a00267.html#a6af977754233b2f276420cc65b9311e6">neoapi.CDeviceFrontUARTSource</a>
, <a class="el" href="a00271.html#a5f1477956b278481eb894c6376830d27">neoapi.CDeviceLicense</a>
, <a class="el" href="a00275.html#a58e5a76cf1f7eb0d713986b57e74d8fe">neoapi.CDeviceLicenseTypeSelector</a>
, <a class="el" href="a00279.html#ab7110e5a472e408d48cdc66d2fa78a95">neoapi.CDeviceLinkHeartbeatMode</a>
, <a class="el" href="a00283.html#a19a9b401ea380f0e619f1f6e2749a4e6">neoapi.CDeviceLinkSelector</a>
, <a class="el" href="a00287.html#a7e8089a2f63131743492e377858f7722">neoapi.CDeviceLinkThroughputLimitMode</a>
, <a class="el" href="a00291.html#a8f3c5647e2bd2f2fc9749564c590fed3">neoapi.CDeviceRegistersEndianness</a>
, <a class="el" href="a00295.html#a74141f096246163e03745896b6fa51b7">neoapi.CDeviceScanType</a>
, <a class="el" href="a00299.html#a6ec977fe4ae0f72cbcd21870e3a00b16">neoapi.CDeviceSensorSelector</a>
, <a class="el" href="a00303.html#a17b3cad107234996e920f07c79e04bc3">neoapi.CDeviceSensorType</a>
, <a class="el" href="a00307.html#ab9780601468a8a65be6872cdb3fd1e8d">neoapi.CDeviceSensorVersion</a>
, <a class="el" href="a00311.html#a66a508a199395c799ab24608f93f48ad">neoapi.CDeviceSerialPortBaudRate</a>
, <a class="el" href="a00315.html#a5c12242f4ef33621d7a43be30b4451e6">neoapi.CDeviceSerialPortSelector</a>
, <a class="el" href="a00319.html#a8cf00695c1b5820f017b4615de869edc">neoapi.CDeviceStreamChannelEndianness</a>
, <a class="el" href="a00323.html#ab7afa3094325f2bd96d5201839661bb0">neoapi.CDeviceStreamChannelType</a>
, <a class="el" href="a00331.html#ae5cfc031593e58b5284b9a21be110889">neoapi.CDeviceTapGeometry</a>
, <a class="el" href="a00335.html#a1e4be010fc870bbc1b0334cef2e11075">neoapi.CDeviceTemperatureSelector</a>
, <a class="el" href="a00339.html#a44ec30456d4ac72a079df4492dcc1032">neoapi.CDeviceTemperatureStatus</a>
, <a class="el" href="a00343.html#afd4779cda00fdf63eb906e36d653a6dc">neoapi.CDeviceTemperatureStatusTransitionSelector</a>
, <a class="el" href="a00327.html#a3636bbdc4a9f1e6eed8f8a401bb99147">neoapi.CDeviceTLType</a>
, <a class="el" href="a00347.html#a00e50b374f8416e225f8000106dc2ecb">neoapi.CDeviceType</a>
, <a class="el" href="a00351.html#ae312218f7074e79117e7970666b468f3">neoapi.CEventNotification</a>
, <a class="el" href="a00355.html#a089ced2efb25cca49e38a0390e1a894c">neoapi.CEventSelector</a>
, <a class="el" href="a00359.html#a1c8ed410197d32c18da35a657415099a">neoapi.CExposureAuto</a>
, <a class="el" href="a00363.html#aa0ff095d1ebe8205c22d3d56a3f00a5b">neoapi.CExposureMode</a>
, <a class="el" href="a00367.html#a1c2b8c8913bd95f1ab505a0200ff12a6">neoapi.CFileOpenMode</a>
, <a class="el" href="a00371.html#a5f65fdc8a68c70c6d67383c7b77d87a3">neoapi.CFileOperationSelector</a>
, <a class="el" href="a00375.html#a09ae8afe7437be75dc8e118823cff3b0">neoapi.CFileOperationStatus</a>
, <a class="el" href="a00379.html#a35f6b6848f2a7b5ad7f156da3989e518">neoapi.CFileSelector</a>
, <a class="el" href="a00383.html#a7c53c001394ac2b01516e73e564ef8bd">neoapi.CFocalLengthStatus</a>
, <a class="el" href="a00387.html#ac1911b7801cb85ed4e14588d739954e3">neoapi.CFocusStatus</a>
, <a class="el" href="a00391.html#a77da07eab507962b9f214d44de1140d4">neoapi.CGainAuto</a>
, <a class="el" href="a00395.html#a5105bd71f10a26bef623b7c5599cafbe">neoapi.CGainSelector</a>
, <a class="el" href="a00399.html#a7cb8487747a6949779af796387e98d43">neoapi.CGenDCStreamingMode</a>
, <a class="el" href="a00403.html#a925b17f41a48bc99da01ff9bf121cbc9">neoapi.CGenDCStreamingStatus</a>
, <a class="el" href="a00407.html#a83a216ee974d1e9fa6a24d92cac760a3">neoapi.CGevCCP</a>
, <a class="el" href="a00411.html#ac38d15187786e2693146b671fd2ae5f9">neoapi.CGevGVCPExtendedStatusCodesSelector</a>
, <a class="el" href="a00415.html#a692f74c28f3d03142ad5c7328abd7da5">neoapi.CGevIPConfigurationStatus</a>
, <a class="el" href="a00419.html#acfecf334405b3cf4602e6d7630fcf68e">neoapi.CGevSupportedOptionSelector</a>
, <a class="el" href="a00423.html#a7e01890e690583cecbf7c6fef195c463">neoapi.CHDRGainRatioSelector</a>
, <a class="el" href="a00427.html#af6ff08ad8859b2bf9187496f662d76c2">neoapi.CHDRTonemappingCurvePresetSelector</a>
, <a class="el" href="a00431.html#af87ef40c52ac0cee678d6096059cda2d">neoapi.CImageCompressionJPEGFormatOption</a>
, <a class="el" href="a00435.html#adb36f4e8fe472d1e9ee88aec0e369f48">neoapi.CImageCompressionMode</a>
, <a class="el" href="a00439.html#a235cf4eb848366e7f7d3ddfc3a9eed3e">neoapi.CImageCompressionRateOption</a>
, <a class="el" href="a00443.html#a608f9c0c69765ea1f014a0bd21adca47">neoapi.CInterfaceSpeedMode</a>
, <a class="el" href="a00455.html#a70b2134207033e21bc43bf4404fea032">neoapi.CLineFormat</a>
, <a class="el" href="a00459.html#a51001f0c3ec590ce74a57c9e7d988b84">neoapi.CLineMode</a>
, <a class="el" href="a00463.html#ac788cfca8956dce6400f29f674bae668">neoapi.CLinePWMConfigurationMode</a>
, <a class="el" href="a00467.html#a2f39d148527348b47ff224e1559c9620">neoapi.CLinePWMMode</a>
, <a class="el" href="a00471.html#a5b79670fe3e1dd7bb5cf347f302877f3">neoapi.CLineSelector</a>
, <a class="el" href="a00475.html#a572df299cd02628640390f6aed78f707">neoapi.CLineSource</a>
, <a class="el" href="a00447.html#a99b716b600a00f666bccd2720179d58b">neoapi.CLUTContent</a>
, <a class="el" href="a00451.html#aa157babb296f83d715c8cbad60dce073">neoapi.CLUTSelector</a>
, <a class="el" href="a00479.html#a30573574e679342783092dc44e275b1c">neoapi.CMemoryActivePart</a>
, <a class="el" href="a00483.html#ac1acb35d593a426be6278576f233b9de">neoapi.CMemoryMode</a>
, <a class="el" href="a00487.html#a8b0dd1a09b17c586c51e9f62bbb33a91">neoapi.CMemoryPartIncrementSource</a>
, <a class="el" href="a00491.html#a4aaf0476115204580466fab4062576c3">neoapi.CMemoryPartMode</a>
, <a class="el" href="a00495.html#ad7db6065ba9478e24778276c1bdb764a">neoapi.CMemoryPartSelector</a>
, <a class="el" href="a00499.html#ac7b9ef6e2211cd3dda427e9f8e8aa970">neoapi.COpticControllerSelector</a>
, <a class="el" href="a00503.html#aac54b16197ee84983aaed0c75824cacc">neoapi.COpticControllerStatus</a>
, <a class="el" href="a00507.html#a36c9ec89993740d2d219aba289004ef6">neoapi.CPartialScanEnabled</a>
, <a class="el" href="a00511.html#a3d04ea60df8ab0f84b6409c67b2a33f2">neoapi.CPixelFormat</a>
, <a class="el" href="a00515.html#aefbd02efacb09ea4e2661f3744963cd8">neoapi.CPtpClockAccuracy</a>
, <a class="el" href="a00519.html#ab6aa1bc0d40cf49f3d81e5285a16a8d9">neoapi.CPtpClockOffsetMode</a>
, <a class="el" href="a00523.html#a58d5b689912f1d68b626d0106893a8db">neoapi.CPtpDriftOffsetMode</a>
, <a class="el" href="a00527.html#ab81d874fbd1b8eb565c17940afe45985">neoapi.CPtpMode</a>
, <a class="el" href="a00531.html#ace2e6e82df5c060d166a79269fe8897c">neoapi.CPtpServoStatus</a>
, <a class="el" href="a00535.html#ab1cf2a2057d00e49b12f41212ba33c09">neoapi.CPtpStatus</a>
, <a class="el" href="a00539.html#aa4004857f91395621c5b4e4eb6dcd90e">neoapi.CPtpSyncMessageIntervalStatus</a>
, <a class="el" href="a00543.html#af5db876a331bfcd19e57a222b02c2ce4">neoapi.CPtpTimestampOffsetMode</a>
, <a class="el" href="a00547.html#ab7900a0e4d326d8517edb4c709861036">neoapi.CReadOutBuffering</a>
, <a class="el" href="a00551.html#ae40806c8803ff37625b575e4a7617c85">neoapi.CReadoutMode</a>
, <a class="el" href="a00555.html#a3b728f39d8518bf122304cc334271fc3">neoapi.CRegionAcquisitionMode</a>
, <a class="el" href="a00559.html#af60eaa355610f069e90c327a8ce7e98d">neoapi.CRegionConfigurationMode</a>
, <a class="el" href="a00563.html#a2c446e4f9e3da34546b16e8f882b1cde">neoapi.CRegionMode</a>
, <a class="el" href="a00567.html#a652b1beb656ee6f6dd7d59752d703da9">neoapi.CRegionSelector</a>
, <a class="el" href="a00571.html#a8df70fa4e7902b8ccb7a864dc9bd9f87">neoapi.CRegionTransferMode</a>
, <a class="el" href="a00579.html#a1decd0d7719ab72f554fc8c8dfdcb9d3">neoapi.CSensorADDigitization</a>
, <a class="el" href="a00583.html#a7285425faac74a36b4f89e2fe8c5b4c8">neoapi.CSensorCutConfigurationMode</a>
, <a class="el" href="a00587.html#a979059a4394293f2a3ac20ec190b92b7">neoapi.CSensorDigitizationTaps</a>
, <a class="el" href="a00591.html#a2f014cad798307fb2b1db3b0cae6a6ad">neoapi.CSensorShutterMode</a>
, <a class="el" href="a00595.html#ae7c72f064c1a979142dd1bead15a2304">neoapi.CSensorTaps</a>
, <a class="el" href="a00599.html#ad08af4aea71477453a2cc775937c3dea">neoapi.CSequencerConfigurationMode</a>
, <a class="el" href="a00603.html#a970b2183a4120299a2172b10b3d489bb">neoapi.CSequencerFeatureSelector</a>
, <a class="el" href="a00607.html#aa368060d2847afdc5094dbbcb9a57cb1">neoapi.CSequencerMode</a>
, <a class="el" href="a00611.html#a25a550ad05c311d18fd35eb5e6c622b3">neoapi.CSequencerTriggerActivation</a>
, <a class="el" href="a00615.html#a34fbab8e86389219fd2c348746b2b4a5">neoapi.CSequencerTriggerSource</a>
, <a class="el" href="a00619.html#a3791be1888e273f5927c5b8aa1e1325b">neoapi.CShadingSelector</a>
, <a class="el" href="a00623.html#a7776b1d4ca989d56aef89763c431713a">neoapi.CSharpeningMode</a>
, <a class="el" href="a00575.html#a1ca9d23d30b527d8b3ebe6514687b504">neoapi.CSIControl</a>
, <a class="el" href="a00627.html#adc43b90cdf631163619d3a29e2049153">neoapi.CSourceID</a>
, <a class="el" href="a00631.html#a8e71fa1e3aa2994ecf983483fb0149ff">neoapi.CSourceSelector</a>
, <a class="el" href="a00635.html#ad2bd902d6097d3d3318ccf2f28b6c008">neoapi.CSwitchPortSelector</a>
, <a class="el" href="a00639.html#ace92ceb53571a3f147cdf342620b6b65">neoapi.CTestPattern</a>
, <a class="el" href="a00643.html#a5417365910e3722826e7a9e1ad5e0169">neoapi.CTestPatternGeneratorSelector</a>
, <a class="el" href="a00647.html#a15428e957bf48643125823e2aac98f56">neoapi.CTestPayloadFormatMode</a>
, <a class="el" href="a00651.html#a241b1bbf541ba88f243eef7b992b90f7">neoapi.CTimerSelector</a>
, <a class="el" href="a00655.html#a01a9107bbd05ca72a4d6f11dc1ed4f6c">neoapi.CTimerTriggerActivation</a>
, <a class="el" href="a00659.html#a424e8c1a0f50a8d46e78b33f160db08b">neoapi.CTimerTriggerSource</a>
, <a class="el" href="a00663.html#ab00d9ff89aa9870a85755b81c6086576">neoapi.CTransferControlMode</a>
, <a class="el" href="a00667.html#a7427b0c01bfcb499630327ee00ca4840">neoapi.CTransferOperationMode</a>
, <a class="el" href="a00671.html#adce98c170b9b364987c109c00c4f76fa">neoapi.CTransferSelector</a>
, <a class="el" href="a00675.html#abe2e2b7e8271c524977ce1a1b712f404">neoapi.CTransferStatusSelector</a>
, <a class="el" href="a00679.html#a969d0cf4f282ae0412e498e5b0cc7de3">neoapi.CTriggerActivation</a>
, <a class="el" href="a00683.html#ad4ec263f6ae822f3b1e2586c3a42bef7">neoapi.CTriggerMode</a>
, <a class="el" href="a00687.html#a51623f51bf67d806ee3162d8ecb47025">neoapi.CTriggerOverlap</a>
, <a class="el" href="a00691.html#ab30484499a81f3d20cd54b2ba94a06e2">neoapi.CTriggerSelector</a>
, <a class="el" href="a00695.html#a4cc38f6768b46993992b37382aaf5e53">neoapi.CTriggerSource</a>
, <a class="el" href="a00699.html#a6c1c1f25213b097b7f9ab71423126814">neoapi.CUserOutputSelector</a>
, <a class="el" href="a00703.html#a497b92757906fdb614a7a6bd7fdb2328">neoapi.CUserSetDefault</a>
, <a class="el" href="a00707.html#a826dff4320be3dbd075a18f2ff6f9273">neoapi.CUserSetFeatureSelector</a>
, <a class="el" href="a00711.html#ab2e45422b8c6e4e6a4b1a9728d48ddd8">neoapi.CUserSetSelector</a>
, <a class="el" href="a00827.html#ac8f658ba086eef67bd35c10a25db4f0a">neoapi.DoubleFeature</a>
, <a class="el" href="a00811.html#ad9353d112a25ec31e5fee21dd4b0e50b">neoapi.Feature</a>
, <a class="el" href="a00831.html#ae6b6dbcf4251babc9dcbdc6333cda1d0">neoapi.IntegerFeature</a>
, <a class="el" href="a00835.html#a8195b7b7fe4c9184132cedc144896a5a">neoapi.StringFeature</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
