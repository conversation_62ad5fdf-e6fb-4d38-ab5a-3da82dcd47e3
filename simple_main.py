VERSION = "1.0.0"

import sys
import os
import logging
from datetime import datetime

# Füge src-Verzeichnis zum Python-Pfad hinzu
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox)
from PyQt5.QtCore import QTimer

# Logging konfigurieren
def setup_logging():
    os.makedirs('logs', exist_ok=True)
    log_filename = os.path.join('logs', f"baumer_temp_{datetime.now().strftime('%Y%m%d')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler()
        ]
    )

class SimpleMainWindow(QMainWindow):
    """Vereinfachtes Hauptfenster"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Kamera Temperatur Monitor")
        self.setGeometry(100, 100, 800, 600)
        
        # Zentrales Widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hauptlayout
        main_layout = QVBoxLayout(central_widget)
        
        # Titel
        title_label = QLabel("Baumer Kamera Temperatur Monitor v1.0.0")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # Tab-Widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Kameras-Tab
        self.init_camera_tab()
        
        # Temperatur-Tab
        self.init_temperature_tab()
        
        # Log-Tab
        self.init_log_tab()
        
        # Status-Label
        self.status_label = QLabel("Bereit")
        main_layout.addWidget(self.status_label)
        
        # Timer für Updates
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_status)
        self.timer.start(5000)
        
        logging.info("Vereinfachtes GUI initialisiert")
        
    def init_camera_tab(self):
        """Initialisiert Kamera-Tab"""
        camera_widget = QWidget()
        layout = QVBoxLayout(camera_widget)
        
        # Kamera-Liste
        camera_group = QGroupBox("Verfügbare Kameras")
        camera_layout = QVBoxLayout(camera_group)
        
        self.camera_list = QListWidget()
        camera_layout.addWidget(self.camera_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.scan_button = QPushButton("Netzwerk scannen")
        self.scan_button.clicked.connect(self.scan_network)
        button_layout.addWidget(self.scan_button)
        
        self.refresh_button = QPushButton("Aktualisieren")
        self.refresh_button.clicked.connect(self.refresh_cameras)
        button_layout.addWidget(self.refresh_button)
        
        camera_layout.addLayout(button_layout)
        layout.addWidget(camera_group)
        
        # Verbundene Kameras
        connected_group = QGroupBox("Verbundene Kameras")
        connected_layout = QVBoxLayout(connected_group)
        
        self.connected_label = QLabel("Keine Kameras verbunden")
        connected_layout.addWidget(self.connected_label)
        
        layout.addWidget(connected_group)
        
        self.tab_widget.addTab(camera_widget, "Kameras")
        
    def init_temperature_tab(self):
        """Initialisiert Temperatur-Tab"""
        temp_widget = QWidget()
        layout = QVBoxLayout(temp_widget)
        
        temp_label = QLabel("Temperaturverlauf")
        temp_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(temp_label)
        
        # Platzhalter für Plot
        plot_placeholder = QLabel("Hier wird der Temperaturverlauf angezeigt\n(Implementierung folgt)")
        plot_placeholder.setStyleSheet("border: 1px solid gray; padding: 20px; text-align: center;")
        layout.addWidget(plot_placeholder)
        
        self.tab_widget.addTab(temp_widget, "Temperaturverlauf")
        
    def init_log_tab(self):
        """Initialisiert Log-Tab"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        log_label = QLabel("System-Log")
        log_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(300)
        layout.addWidget(self.log_text)
        
        # Clear-Button
        clear_button = QPushButton("Log leeren")
        clear_button.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_button)
        
        self.tab_widget.addTab(log_widget, "System-Log")
        
    def scan_network(self):
        """Scannt Netzwerk nach Kameras"""
        try:
            self.scan_button.setEnabled(False)
            self.scan_button.setText("Scanne...")
            self.status_label.setText("Netzwerk-Scan läuft...")
            
            self.log_message("Starte Netzwerk-Scan...")
            
            # Simuliere Scan (vereinfacht)
            import threading
            import time
            
            def scan_thread():
                try:
                    time.sleep(2)  # Simuliere Scan-Zeit
                    
                    # Simuliere gefundene Kameras
                    found_cameras = [
                        "192.168.1.100 - VCXG-13M (Simulation)",
                        "192.168.1.101 - VCXG-13M (Simulation)"
                    ]
                    
                    # Update GUI im Hauptthread
                    QTimer.singleShot(0, lambda: self.scan_completed(found_cameras))
                    
                except Exception as e:
                    QTimer.singleShot(0, lambda: self.scan_error(str(e)))
            
            thread = threading.Thread(target=scan_thread, daemon=True)
            thread.start()
            
        except Exception as e:
            self.scan_error(str(e))
            
    def scan_completed(self, cameras):
        """Scan erfolgreich abgeschlossen"""
        self.scan_button.setEnabled(True)
        self.scan_button.setText("Netzwerk scannen")
        
        self.camera_list.clear()
        for camera in cameras:
            self.camera_list.addItem(camera)
            
        self.status_label.setText(f"{len(cameras)} Kamera(s) gefunden")
        self.log_message(f"Scan abgeschlossen: {len(cameras)} Kameras gefunden")
        
    def scan_error(self, error):
        """Scan-Fehler"""
        self.scan_button.setEnabled(True)
        self.scan_button.setText("Netzwerk scannen")
        self.status_label.setText(f"Scan-Fehler: {error}")
        self.log_message(f"Scan-Fehler: {error}")
        
    def refresh_cameras(self):
        """Aktualisiert Kamera-Liste"""
        self.log_message("Kamera-Liste aktualisiert")
        self.status_label.setText("Liste aktualisiert")
        
    def log_message(self, message):
        """Fügt Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logging.info(message)
        
    def update_status(self):
        """Aktualisiert Status"""
        current_time = datetime.now().strftime("%H:%M:%S")
        if "Bereit" in self.status_label.text():
            self.status_label.setText(f"Bereit - {current_time}")

def main():
    """Hauptprogramm"""
    try:
        setup_logging()
        logging.info(f"Starte Baumer Temperatur Monitor v{VERSION}")
        
        app = QApplication(sys.argv)
        app.setApplicationName("Baumer Temperatur Monitor")
        app.setApplicationVersion(VERSION)
        
        window = SimpleMainWindow()
        window.show()
        
        logging.info("GUI gestartet")
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Kritischer Fehler: {e}")
        print(f"FEHLER: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
