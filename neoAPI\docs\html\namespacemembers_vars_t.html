<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_t"></a>- t -</h3><ul>
<li>TestPattern_Black
: <a class="el" href="a00091.html#aa6459f0407e829b1f6a6d7282b5c1c92">neoapi</a>
</li>
<li>TestPattern_ColorBar
: <a class="el" href="a00091.html#a4175ff63bc2c41c72000a40088f75dba">neoapi</a>
</li>
<li>TestPattern_FrameCounter
: <a class="el" href="a00091.html#a96f531bc3e15b653bf74fa3bc5daaeec">neoapi</a>
</li>
<li>TestPattern_GreyDiagonalRamp
: <a class="el" href="a00091.html#ac7f96610894c2a06dbb42421f01e7e60">neoapi</a>
</li>
<li>TestPattern_GreyDiagonalRampHorizontalAndVerticalLineMoving
: <a class="el" href="a00091.html#ae3881cd51727b69eea6a736ad71e1862">neoapi</a>
</li>
<li>TestPattern_GreyDiagonalRampHorizontalLineMoving
: <a class="el" href="a00091.html#a8be908169326e9030ee623d4544865bc">neoapi</a>
</li>
<li>TestPattern_GreyDiagonalRampVerticalLineMoving
: <a class="el" href="a00091.html#a5f2ed9f3b9a3e5f0b42fec4c1f4e8b4c">neoapi</a>
</li>
<li>TestPattern_GreyDiagonalRampWithLineMoving
: <a class="el" href="a00091.html#add300c5857672493d82bed693cee8475">neoapi</a>
</li>
<li>TestPattern_GreyHorizontalRamp
: <a class="el" href="a00091.html#a729ed5ded43183665dccbaf8b68b30da">neoapi</a>
</li>
<li>TestPattern_GreyHorizontalRampHorizontalAndVerticalLineMoving
: <a class="el" href="a00091.html#a2b6f9009561d206d9e3e160bb5363dca">neoapi</a>
</li>
<li>TestPattern_GreyHorizontalRampHorizontalLineMoving
: <a class="el" href="a00091.html#ae8b8f5535f4d5c0691ea401026469c71">neoapi</a>
</li>
<li>TestPattern_GreyHorizontalRampMoving
: <a class="el" href="a00091.html#a3eb3d00a46413ed4ca5e4477a365b32f">neoapi</a>
</li>
<li>TestPattern_GreyHorizontalRampVerticalLineMoving
: <a class="el" href="a00091.html#aa153488254206d57b4aec4392428c4a7">neoapi</a>
</li>
<li>TestPattern_GreyVerticalRamp
: <a class="el" href="a00091.html#a6fcc317d177291db12b222064a307ae1">neoapi</a>
</li>
<li>TestPattern_GreyVerticalRampHorizontalAndVerticalLineMoving
: <a class="el" href="a00091.html#a54552b43eec68e2fd80194d5d6460078">neoapi</a>
</li>
<li>TestPattern_GreyVerticalRampHorizontalLineMoving
: <a class="el" href="a00091.html#a2cfbe03f38c9c445290c41992de26b20">neoapi</a>
</li>
<li>TestPattern_GreyVerticalRampMoving
: <a class="el" href="a00091.html#ac793fb52680bfb3c7152f62cb4c182a0">neoapi</a>
</li>
<li>TestPattern_GreyVerticalRampVerticalLineMoving
: <a class="el" href="a00091.html#a455aa2594638c972483f0f4f7fd276d7">neoapi</a>
</li>
<li>TestPattern_HorizontalAndVerticalLineMoving
: <a class="el" href="a00091.html#a26c699702086fe097fd1d83752b41d33">neoapi</a>
</li>
<li>TestPattern_HorizontalLineMoving
: <a class="el" href="a00091.html#ac0689588d78a13973ff370489930cddb">neoapi</a>
</li>
<li>TestPattern_Off
: <a class="el" href="a00091.html#a70b629aea9ce56bfefb84ff57b7e3555">neoapi</a>
</li>
<li>TestPattern_VerticalLineMoving
: <a class="el" href="a00091.html#acd89ab2884f5d8b9a651e0854afc9b04">neoapi</a>
</li>
<li>TestPattern_White
: <a class="el" href="a00091.html#a496b1c50c6baf391952e0323121b8c96">neoapi</a>
</li>
<li>TestPatternGeneratorSelector_ImageProcessor
: <a class="el" href="a00091.html#a983d2da1f00882d6c9ff26e6f7b87db0">neoapi</a>
</li>
<li>TestPatternGeneratorSelector_Region0
: <a class="el" href="a00091.html#ab7a611dd1aecbc45c4117916b6d6378f">neoapi</a>
</li>
<li>TestPatternGeneratorSelector_Region1
: <a class="el" href="a00091.html#a36461472342e399c462b7c945ce9dda0">neoapi</a>
</li>
<li>TestPatternGeneratorSelector_Region2
: <a class="el" href="a00091.html#ae973ea8f5d012b8c44be148f23bdc394">neoapi</a>
</li>
<li>TestPatternGeneratorSelector_Sensor
: <a class="el" href="a00091.html#a80ee979d589368916b0eae59a5427b78">neoapi</a>
</li>
<li>TestPatternGeneratorSelector_SensorProcessor
: <a class="el" href="a00091.html#a1a9a41a35f28c994514766e3c08ac500">neoapi</a>
</li>
<li>TestPayloadFormatMode_GenDC
: <a class="el" href="a00091.html#a8cc11dacece9e455f59f5580cdf86de4">neoapi</a>
</li>
<li>TestPayloadFormatMode_MultiPart
: <a class="el" href="a00091.html#af1c8684cc87c48adb2b203aef3ee8851">neoapi</a>
</li>
<li>TestPayloadFormatMode_Off
: <a class="el" href="a00091.html#ae0dd75e6af98a025b01751e2e961a61e">neoapi</a>
</li>
<li>TimerSelector_Timer0
: <a class="el" href="a00091.html#ac743de4645f5327b36a84d8aeae3a22e">neoapi</a>
</li>
<li>TimerSelector_Timer1
: <a class="el" href="a00091.html#a2243399a048db412d09b69c408047cdc">neoapi</a>
</li>
<li>TimerSelector_Timer2
: <a class="el" href="a00091.html#a99a30ca8ebb1a9f2d3877cf611d0d152">neoapi</a>
</li>
<li>TimerSelector_Timer3
: <a class="el" href="a00091.html#aee9392c8cdc02878740c2faa4defc229">neoapi</a>
</li>
<li>TimerTriggerActivation_AnyEdge
: <a class="el" href="a00091.html#a444dd784e00a205333c58d2f4b937f08">neoapi</a>
</li>
<li>TimerTriggerActivation_FallingEdge
: <a class="el" href="a00091.html#a757052fcce7a9d2c2083145e83f023a7">neoapi</a>
</li>
<li>TimerTriggerActivation_LevelHigh
: <a class="el" href="a00091.html#aed747486907e332fd01365f5bc9f412d">neoapi</a>
</li>
<li>TimerTriggerActivation_LevelLow
: <a class="el" href="a00091.html#a557260b549ed4147e34b072dacbf0dfb">neoapi</a>
</li>
<li>TimerTriggerActivation_RisingEdge
: <a class="el" href="a00091.html#aa95cc1d778a2a34857e56a2d51d208bf">neoapi</a>
</li>
<li>TimerTriggerSource_AcquisitionEnd
: <a class="el" href="a00091.html#adf90259b149bd1d1f381bb839e856dee">neoapi</a>
</li>
<li>TimerTriggerSource_AcquisitionStart
: <a class="el" href="a00091.html#a8c709379dc5ae83a68ff3246b41e25f5">neoapi</a>
</li>
<li>TimerTriggerSource_AcquisitionTrigger
: <a class="el" href="a00091.html#af83579171d58f2abc095af810bf92c81">neoapi</a>
</li>
<li>TimerTriggerSource_AcquisitionTriggerMissed
: <a class="el" href="a00091.html#a79449f03701308f0a88f8d74042055a8">neoapi</a>
</li>
<li>TimerTriggerSource_Action0
: <a class="el" href="a00091.html#a3ac0bc280901c2a22d1e74050e24e8cb">neoapi</a>
</li>
<li>TimerTriggerSource_Action1
: <a class="el" href="a00091.html#ab299592099f3ca1c0c49ab57a65ba1cd">neoapi</a>
</li>
<li>TimerTriggerSource_Action2
: <a class="el" href="a00091.html#ae5ac3d00f41e6f24e9d58ab18f0f824f">neoapi</a>
</li>
<li>TimerTriggerSource_Counter0End
: <a class="el" href="a00091.html#ab3add44dea5e88fbe9f098e3b88b4944">neoapi</a>
</li>
<li>TimerTriggerSource_Counter0Start
: <a class="el" href="a00091.html#a4989b379a277fe5fe4510b16188ecedd">neoapi</a>
</li>
<li>TimerTriggerSource_Counter1End
: <a class="el" href="a00091.html#a7dffe958c95ce04263050849309b36ae">neoapi</a>
</li>
<li>TimerTriggerSource_Counter1Start
: <a class="el" href="a00091.html#ad335d916978c57d49d87273a1dde9276">neoapi</a>
</li>
<li>TimerTriggerSource_Counter2End
: <a class="el" href="a00091.html#a9544c16f5d9ebf5ec5040b852bb52be1">neoapi</a>
</li>
<li>TimerTriggerSource_Counter2Start
: <a class="el" href="a00091.html#a363e2d592c24209d9e1950a8b53edc0a">neoapi</a>
</li>
<li>TimerTriggerSource_Encoder0
: <a class="el" href="a00091.html#a6e68c4aa5027547f12ab4ad69d7107e9">neoapi</a>
</li>
<li>TimerTriggerSource_Encoder1
: <a class="el" href="a00091.html#af736917274323ee88c2730e54454893b">neoapi</a>
</li>
<li>TimerTriggerSource_Encoder2
: <a class="el" href="a00091.html#a2c81abb4f5326bd2a7a312b622594be7">neoapi</a>
</li>
<li>TimerTriggerSource_ExposureEnd
: <a class="el" href="a00091.html#a079cb9d8129d66bef9ff1fffde711b78">neoapi</a>
</li>
<li>TimerTriggerSource_ExposureStart
: <a class="el" href="a00091.html#ae66a7bdb0f37d49ab77292efc54240a1">neoapi</a>
</li>
<li>TimerTriggerSource_FrameBurstEnd
: <a class="el" href="a00091.html#ac51820b5462e57bc5fa658d21f25c82c">neoapi</a>
</li>
<li>TimerTriggerSource_FrameBurstStart
: <a class="el" href="a00091.html#a5bb976a6a5be1f203cf93f4b261cd63e">neoapi</a>
</li>
<li>TimerTriggerSource_FrameEnd
: <a class="el" href="a00091.html#a60b214fb30c4bbce5b93d0496e0b687b">neoapi</a>
</li>
<li>TimerTriggerSource_FrameStart
: <a class="el" href="a00091.html#a2cd7d9c99746188b6ea3e9b7bb0954fa">neoapi</a>
</li>
<li>TimerTriggerSource_FrameTransferSkipped
: <a class="el" href="a00091.html#a48fadc6e7df8b2a2d2c8a89e3a3259ea">neoapi</a>
</li>
<li>TimerTriggerSource_FrameTrigger
: <a class="el" href="a00091.html#a791411c93a0426fc81092d4037d6677c">neoapi</a>
</li>
<li>TimerTriggerSource_FrameTriggerMissed
: <a class="el" href="a00091.html#af9aeb277654465498c8788a8dadf38ce">neoapi</a>
</li>
<li>TimerTriggerSource_Line0
: <a class="el" href="a00091.html#acd783c57cc0ac4681e6b3ab9aa518ac4">neoapi</a>
</li>
<li>TimerTriggerSource_Line1
: <a class="el" href="a00091.html#a5260be9eef793a68378ba47dc6fac827">neoapi</a>
</li>
<li>TimerTriggerSource_Line2
: <a class="el" href="a00091.html#a3735b40ddf77ed7f2554806de5c92849">neoapi</a>
</li>
<li>TimerTriggerSource_LineEnd
: <a class="el" href="a00091.html#ac841007796692b8c43c5e97bea07f2b5">neoapi</a>
</li>
<li>TimerTriggerSource_LineStart
: <a class="el" href="a00091.html#a6c5bd5a6bd1e16f760962b6741c5512e">neoapi</a>
</li>
<li>TimerTriggerSource_LineTrigger
: <a class="el" href="a00091.html#a5bffc6fbe3e22fc628343a47ba123ceb">neoapi</a>
</li>
<li>TimerTriggerSource_LineTriggerMissed
: <a class="el" href="a00091.html#a10f0455695b27a4c0fca858929686187">neoapi</a>
</li>
<li>TimerTriggerSource_LinkTrigger0
: <a class="el" href="a00091.html#a07b733f353c444fcb67084612d8aff1d">neoapi</a>
</li>
<li>TimerTriggerSource_LinkTrigger1
: <a class="el" href="a00091.html#ac9cd26410a360af3d2f5035464ed26b5">neoapi</a>
</li>
<li>TimerTriggerSource_LinkTrigger2
: <a class="el" href="a00091.html#ac6ef60b1d0e214568d8d21e45df344fe">neoapi</a>
</li>
<li>TimerTriggerSource_LogicBlock0
: <a class="el" href="a00091.html#a66a4a4a1b8a67475cf8a900e89055b45">neoapi</a>
</li>
<li>TimerTriggerSource_LogicBlock1
: <a class="el" href="a00091.html#aaae26ab347d39b5a3ae09684fde72fe3">neoapi</a>
</li>
<li>TimerTriggerSource_LogicBlock2
: <a class="el" href="a00091.html#a76a06d3585ed46db1741c06a5184fc67">neoapi</a>
</li>
<li>TimerTriggerSource_Off
: <a class="el" href="a00091.html#a1776cb3e4e33cf25085d4ac7ef8f9e9a">neoapi</a>
</li>
<li>TimerTriggerSource_Software
: <a class="el" href="a00091.html#a69b7fa26cdf0532e136cb4c20ad1050a">neoapi</a>
</li>
<li>TimerTriggerSource_SoftwareSignal0
: <a class="el" href="a00091.html#a5528725f22cfebd8616ac1a4d4946f3a">neoapi</a>
</li>
<li>TimerTriggerSource_SoftwareSignal1
: <a class="el" href="a00091.html#ab8b1ec63f90559468b798e4d41804674">neoapi</a>
</li>
<li>TimerTriggerSource_SoftwareSignal2
: <a class="el" href="a00091.html#a41be2e7a7f5cadc2e9180b39f909e5e7">neoapi</a>
</li>
<li>TimerTriggerSource_Timer0End
: <a class="el" href="a00091.html#ac4e04370b0aec3007e083e797e0c4458">neoapi</a>
</li>
<li>TimerTriggerSource_Timer0Start
: <a class="el" href="a00091.html#a5b5d24442a606ded81a053532a9424bc">neoapi</a>
</li>
<li>TimerTriggerSource_Timer1End
: <a class="el" href="a00091.html#ae03773cc53dacd54f97b721db1d1958b">neoapi</a>
</li>
<li>TimerTriggerSource_Timer1Start
: <a class="el" href="a00091.html#a3a39c7a0adc2431603eb02c874264029">neoapi</a>
</li>
<li>TimerTriggerSource_Timer2End
: <a class="el" href="a00091.html#a76b19780e1cd79cccbf2be72149fc749">neoapi</a>
</li>
<li>TimerTriggerSource_Timer2Start
: <a class="el" href="a00091.html#a505b88786e1c06c575f92c12bb53d3bf">neoapi</a>
</li>
<li>TimerTriggerSource_TriggerSkipped
: <a class="el" href="a00091.html#a901dfb2f44c6623e6c33f0c8ce19f5d7">neoapi</a>
</li>
<li>TimerTriggerSource_UserOutput0
: <a class="el" href="a00091.html#aa5979f92e302a5dd581980a39bf8bac1">neoapi</a>
</li>
<li>TimerTriggerSource_UserOutput1
: <a class="el" href="a00091.html#ad6d7ff48e93429abecea536f9812c69a">neoapi</a>
</li>
<li>TimerTriggerSource_UserOutput2
: <a class="el" href="a00091.html#abc2c30d2c94401c3df87f3d6b6378182">neoapi</a>
</li>
<li>TransferControlMode_Automatic
: <a class="el" href="a00091.html#af378f73dde84b715c5be5a39af07e0f0">neoapi</a>
</li>
<li>TransferControlMode_Basic
: <a class="el" href="a00091.html#ae259b370c47be4494dd3d684f004355d">neoapi</a>
</li>
<li>TransferControlMode_UserControlled
: <a class="el" href="a00091.html#a49be8e86062a5fdc4bed9735a23bd613">neoapi</a>
</li>
<li>TransferOperationMode_Continuous
: <a class="el" href="a00091.html#a2ff976b78d15f38c0991c87dcf76ee4b">neoapi</a>
</li>
<li>TransferOperationMode_MultiBlock
: <a class="el" href="a00091.html#ac7541f5dd8c2cd0b8492ff6fcfa92a0d">neoapi</a>
</li>
<li>TransferSelector_All
: <a class="el" href="a00091.html#a04f68d689ded73d6cef5022b43889051">neoapi</a>
</li>
<li>TransferSelector_Stream0
: <a class="el" href="a00091.html#af05e172738f4f03eb8e68f788204f8d1">neoapi</a>
</li>
<li>TransferSelector_Stream1
: <a class="el" href="a00091.html#a3b3ccbd720c02d6b5507faf0473c63c9">neoapi</a>
</li>
<li>TransferSelector_Stream2
: <a class="el" href="a00091.html#ab9b8e94bc61a1b1c8d6d27c57fb42dbf">neoapi</a>
</li>
<li>TransferSelector_Stream3
: <a class="el" href="a00091.html#a60c0b0d0cc3445e4a25600db2ccfaa62">neoapi</a>
</li>
<li>TransferSelector_Stream4
: <a class="el" href="a00091.html#a0687f659156db0d498872b90d4dc7aae">neoapi</a>
</li>
<li>TransferStatusSelector_Paused
: <a class="el" href="a00091.html#a040ef32eb91aee4aa86312a25ee4c510">neoapi</a>
</li>
<li>TransferStatusSelector_QueueOverflow
: <a class="el" href="a00091.html#adb226056d8e65af690b136edf3df2eb0">neoapi</a>
</li>
<li>TransferStatusSelector_Stopped
: <a class="el" href="a00091.html#aa9335a7652b7e9de59182fe1b4596d73">neoapi</a>
</li>
<li>TransferStatusSelector_Stopping
: <a class="el" href="a00091.html#a6e17615c06e7bfa42877465fa6b4e76f">neoapi</a>
</li>
<li>TransferStatusSelector_Streaming
: <a class="el" href="a00091.html#a4b9df60bcf81e8f91af06ff9ad0ad591">neoapi</a>
</li>
<li>TriggerActivation_AnyEdge
: <a class="el" href="a00091.html#ad06a6cfa28562691aaf3fbb0c16f803c">neoapi</a>
</li>
<li>TriggerActivation_FallingEdge
: <a class="el" href="a00091.html#a1b3e3ea361cb13e4ca691e9faa48c774">neoapi</a>
</li>
<li>TriggerActivation_LevelHigh
: <a class="el" href="a00091.html#a97971deffb2b6d705ecd86ae93a94b4b">neoapi</a>
</li>
<li>TriggerActivation_LevelLow
: <a class="el" href="a00091.html#ae88b20fd8d4dd89f970eebaea99f1dd0">neoapi</a>
</li>
<li>TriggerActivation_RisingEdge
: <a class="el" href="a00091.html#a0115de1462994f56b50d36a48c39e83f">neoapi</a>
</li>
<li>TriggerMode_Off
: <a class="el" href="a00091.html#a39afd18dede91a6d4652e7667006ad38">neoapi</a>
</li>
<li>TriggerMode_On
: <a class="el" href="a00091.html#a1a57e09ce3774d3a4f3c47fdaeda5c4b">neoapi</a>
</li>
<li>TriggerOverlap_Off
: <a class="el" href="a00091.html#a068fa2e512a109bd3c91f948045173a9">neoapi</a>
</li>
<li>TriggerOverlap_PreviousFrame
: <a class="el" href="a00091.html#ad2d104411bf444fa2fa45c643f17aa45">neoapi</a>
</li>
<li>TriggerOverlap_PreviousLine
: <a class="el" href="a00091.html#a574bc9cae5c54e4c5749fafa7014eb4a">neoapi</a>
</li>
<li>TriggerOverlap_ReadOut
: <a class="el" href="a00091.html#a78375d9be2b87c6ef810b42cb5d7f6f2">neoapi</a>
</li>
<li>TriggerSelector_AcquisitionActive
: <a class="el" href="a00091.html#aa70139f3bb0c23778b9fe7d2205df8a5">neoapi</a>
</li>
<li>TriggerSelector_AcquisitionEnd
: <a class="el" href="a00091.html#abd457805b075a6eae61c3a101ef85f4b">neoapi</a>
</li>
<li>TriggerSelector_AcquisitionStart
: <a class="el" href="a00091.html#ab9dd65c122740b30806b1859570b1beb">neoapi</a>
</li>
<li>TriggerSelector_ExposureActive
: <a class="el" href="a00091.html#ad878f52633bb0466357ad05ada9fd1d5">neoapi</a>
</li>
<li>TriggerSelector_ExposureEnd
: <a class="el" href="a00091.html#acd677063e9cca1deb9c04401439f8128">neoapi</a>
</li>
<li>TriggerSelector_ExposureStart
: <a class="el" href="a00091.html#a57274cd1e2709c1277c3a88718dae52c">neoapi</a>
</li>
<li>TriggerSelector_FrameActive
: <a class="el" href="a00091.html#aa7bfbc7e4860e7a52889fa1f58b6b508">neoapi</a>
</li>
<li>TriggerSelector_FrameBurstActive
: <a class="el" href="a00091.html#ac89b2bac9c040bb7659e2edfe5745e63">neoapi</a>
</li>
<li>TriggerSelector_FrameBurstEnd
: <a class="el" href="a00091.html#abdd0f4e18fbd511a819d6cb43c1b9b28">neoapi</a>
</li>
<li>TriggerSelector_FrameBurstStart
: <a class="el" href="a00091.html#af87645053ae315b4aaa598fb42fb847c">neoapi</a>
</li>
<li>TriggerSelector_FrameEnd
: <a class="el" href="a00091.html#a28bbda4478b79e392c51f09794375d1d">neoapi</a>
</li>
<li>TriggerSelector_FrameStart
: <a class="el" href="a00091.html#a8fa12fca3fcc1bb101571a0865a32822">neoapi</a>
</li>
<li>TriggerSelector_LineStart
: <a class="el" href="a00091.html#ab3d68190a1fedd7f9dee440056a6c3e3">neoapi</a>
</li>
<li>TriggerSelector_MultiSlopeExposureLimit1
: <a class="el" href="a00091.html#a23867f4c84053f9931d5e58ca99dc07d">neoapi</a>
</li>
<li>TriggerSource_Action0
: <a class="el" href="a00091.html#a2bf5d98b36a6385e246ad36b4f1b94ae">neoapi</a>
</li>
<li>TriggerSource_Action1
: <a class="el" href="a00091.html#abad18d344c66fc429139933452b4cdb7">neoapi</a>
</li>
<li>TriggerSource_Action2
: <a class="el" href="a00091.html#ad50537ae2bb5b65ce5957a6d9f1fabfd">neoapi</a>
</li>
<li>TriggerSource_All
: <a class="el" href="a00091.html#aff1990fdc26aff0ac3dec53fd8fbbae9">neoapi</a>
</li>
<li>TriggerSource_CC1
: <a class="el" href="a00091.html#a588368ef694e94ee46f4762b0546220e">neoapi</a>
</li>
<li>TriggerSource_CC2
: <a class="el" href="a00091.html#a3bf4224c20950f78421c2c10e2070e47">neoapi</a>
</li>
<li>TriggerSource_CC3
: <a class="el" href="a00091.html#a89c86cdbdb6ec94582a314b0e1693cd1">neoapi</a>
</li>
<li>TriggerSource_CC4
: <a class="el" href="a00091.html#afd90f52a3f02e0a30a3b27642450b1ec">neoapi</a>
</li>
<li>TriggerSource_Counter0End
: <a class="el" href="a00091.html#ae464f7386c6304d50ed87e5e4fea657c">neoapi</a>
</li>
<li>TriggerSource_Counter0Start
: <a class="el" href="a00091.html#a157392db0b01b0021e3ca69533d2c21b">neoapi</a>
</li>
<li>TriggerSource_Counter1End
: <a class="el" href="a00091.html#a10302ffd2034097d277d3d2a737ead90">neoapi</a>
</li>
<li>TriggerSource_Counter1Start
: <a class="el" href="a00091.html#a07560bf198fc83c4b9bb2581193d4be0">neoapi</a>
</li>
<li>TriggerSource_Counter2End
: <a class="el" href="a00091.html#a7f4d1eee0c8b85e90721f2ce612fdf7b">neoapi</a>
</li>
<li>TriggerSource_Counter2Start
: <a class="el" href="a00091.html#a617a823c54d885547daa233e634fa7c5">neoapi</a>
</li>
<li>TriggerSource_Encoder0
: <a class="el" href="a00091.html#aac1f3c292480aff67d3b93f8f1d72e22">neoapi</a>
</li>
<li>TriggerSource_Encoder1
: <a class="el" href="a00091.html#acfeb93d4ff3ea3603a2d9a2a1e65e327">neoapi</a>
</li>
<li>TriggerSource_Encoder2
: <a class="el" href="a00091.html#ac2b951ff425a7070f6d089f3965355f0">neoapi</a>
</li>
<li>TriggerSource_Line0
: <a class="el" href="a00091.html#ae18bde8ffcea7a54c25c29e565148dff">neoapi</a>
</li>
<li>TriggerSource_Line1
: <a class="el" href="a00091.html#addf1c81c238db8afd3a890856f7bc1e7">neoapi</a>
</li>
<li>TriggerSource_Line2
: <a class="el" href="a00091.html#aaf79ec825581523366346feddb5dd1c8">neoapi</a>
</li>
<li>TriggerSource_Line3
: <a class="el" href="a00091.html#ac486927a7733a95b9256c2a8dfe334ce">neoapi</a>
</li>
<li>TriggerSource_LinkTrigger0
: <a class="el" href="a00091.html#a94266183406e559cd97b6ad05c988ffc">neoapi</a>
</li>
<li>TriggerSource_LinkTrigger1
: <a class="el" href="a00091.html#a889d9e5684b6203a043020db810c0721">neoapi</a>
</li>
<li>TriggerSource_LinkTrigger2
: <a class="el" href="a00091.html#a20f862a4fcc774e5d39a3341b0c5dd00">neoapi</a>
</li>
<li>TriggerSource_LogicBlock0
: <a class="el" href="a00091.html#a540633d0ac22c301784e415b99229edc">neoapi</a>
</li>
<li>TriggerSource_LogicBlock1
: <a class="el" href="a00091.html#a2d75dbcaa9ff6a0f07c3dd157905e802">neoapi</a>
</li>
<li>TriggerSource_LogicBlock2
: <a class="el" href="a00091.html#a5feff3c40e8d840d13483f0c3161b734">neoapi</a>
</li>
<li>TriggerSource_Off
: <a class="el" href="a00091.html#a62a968a44f64a6ee8251afc713fc1bf5">neoapi</a>
</li>
<li>TriggerSource_Software
: <a class="el" href="a00091.html#a652f720988580afbc8edab91df5acd89">neoapi</a>
</li>
<li>TriggerSource_SoftwareSignal0
: <a class="el" href="a00091.html#adf1f034d8a237d21b8d32995892107ae">neoapi</a>
</li>
<li>TriggerSource_SoftwareSignal1
: <a class="el" href="a00091.html#a241f496fb90c4f94332e0f76f857dc6d">neoapi</a>
</li>
<li>TriggerSource_SoftwareSignal2
: <a class="el" href="a00091.html#aa539a17bfead2a69c4b8e228509de17a">neoapi</a>
</li>
<li>TriggerSource_Timer0End
: <a class="el" href="a00091.html#ab3786f8a4ebe0488d67f13aa0822a71b">neoapi</a>
</li>
<li>TriggerSource_Timer0Start
: <a class="el" href="a00091.html#aa028ed74766a554aab357bd8816a8a36">neoapi</a>
</li>
<li>TriggerSource_Timer1End
: <a class="el" href="a00091.html#aa53348ea3c4661e5773fc76b3c1b032a">neoapi</a>
</li>
<li>TriggerSource_Timer1Start
: <a class="el" href="a00091.html#a683201fa33974b702e55f5c73b16e5a5">neoapi</a>
</li>
<li>TriggerSource_Timer2End
: <a class="el" href="a00091.html#a04958e23d625872ee99a379c71707708">neoapi</a>
</li>
<li>TriggerSource_Timer2Start
: <a class="el" href="a00091.html#afb69e1f7b11fb2beb734d1adba3e1163">neoapi</a>
</li>
<li>TriggerSource_UserOutput0
: <a class="el" href="a00091.html#ace711a0580630dfd49f385133da7b781">neoapi</a>
</li>
<li>TriggerSource_UserOutput1
: <a class="el" href="a00091.html#a6474d31924ba2a2862550b05638afef2">neoapi</a>
</li>
<li>TriggerSource_UserOutput2
: <a class="el" href="a00091.html#a136eea5172a1f4baf310ec230c0c8614">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
