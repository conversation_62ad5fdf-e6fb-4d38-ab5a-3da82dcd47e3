#!/usr/bin/env python3
"""
Test-Tool für Netzwerkkarten-Erkennung
"""

import sys
import os
import subprocess
import logging

# Füge src-Verzeichnis hinzu
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ipconfig():
    """Testet ipconfig Ausgabe"""
    print("=== IPCONFIG TEST ===")
    
    try:
        # Teste ipconfig /all
        print("\n--- ipconfig /all ---")
        result = subprocess.run(['ipconfig', '/all'], capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            adapter_count = 0
            ip_count = 0
            
            for i, line in enumerate(lines):
                if 'adapter' in line.lower() and ':' in line:
                    adapter_count += 1
                    print(f"Adapter {adapter_count}: {line.strip()}")
                elif 'IPv4' in line:
                    ip_count += 1
                    print(f"  IPv4: {line.strip()}")
                elif 'Subnet Mask' in line or 'Subnetzmaske' in line:
                    print(f"  Subnet: {line.strip()}")
                    
            print(f"\nGefunden: {adapter_count} Adapter, {ip_count} IPv4-Adressen")
        else:
            print(f"ipconfig /all fehlgeschlagen: {result.returncode}")
            
    except Exception as e:
        print(f"ipconfig Test Fehler: {e}")

def test_discovery():
    """Testet CameraDiscovery"""
    print("\n=== CAMERA DISCOVERY TEST ===")
    
    try:
        from camera.discovery import CameraDiscovery
        
        discovery = CameraDiscovery()
        interfaces = discovery.available_interfaces
        
        print(f"\nGefundene Interfaces: {len(interfaces)}")
        for i, interface in enumerate(interfaces):
            print(f"\nInterface {i+1}:")
            print(f"  Name: {interface.get('name', 'Unbekannt')}")
            print(f"  IP: {interface.get('ip', 'Unbekannt')}")
            print(f"  Netzmaske: {interface.get('netmask', 'Unbekannt')}")
            print(f"  Netzwerk-Bereich: {interface.get('network_base', 'Unbekannt')}")
            print(f"  Beschreibung: {interface.get('description', 'Unbekannt')}")
            
    except Exception as e:
        print(f"Discovery Test Fehler: {e}")
        import traceback
        traceback.print_exc()

def test_manual_parsing():
    """Manueller Test der ipconfig Ausgabe"""
    print("\n=== MANUELLER PARSING TEST ===")
    
    try:
        result = subprocess.run(['ipconfig', '/all'], capture_output=True, text=True, timeout=15)
        if result.returncode == 0:
            output = result.stdout
            
            # Suche nach allen Adaptern
            adapters = []
            lines = output.split('\n')
            
            current_adapter = None
            current_data = {}
            
            for line in lines:
                line_stripped = line.strip()
                
                if 'adapter' in line.lower() and ':' in line:
                    # Speichere vorherigen Adapter
                    if current_adapter and current_data.get('ip'):
                        adapters.append({
                            'name': current_adapter,
                            'data': current_data.copy()
                        })
                    
                    current_adapter = line.split(':')[0].strip()
                    current_data = {}
                    
                elif 'IPv4' in line and current_adapter:
                    try:
                        ip_part = line.split(':')[1].strip()
                        ip = ip_part.split('(')[0].strip()
                        current_data['ip'] = ip
                    except:
                        pass
                        
                elif ('Subnet Mask' in line or 'Subnetzmaske' in line) and current_adapter:
                    try:
                        subnet = line.split(':')[1].strip()
                        current_data['subnet'] = subnet
                    except:
                        pass
                        
                elif 'Physical Address' in line or 'Physikalische Adresse' in line:
                    try:
                        mac = line.split(':')[1].strip()
                        current_data['mac'] = mac
                    except:
                        pass
            
            # Letzten Adapter hinzufügen
            if current_adapter and current_data.get('ip'):
                adapters.append({
                    'name': current_adapter,
                    'data': current_data.copy()
                })
            
            print(f"\nManuell geparste Adapter: {len(adapters)}")
            for i, adapter in enumerate(adapters):
                print(f"\nAdapter {i+1}: {adapter['name']}")
                for key, value in adapter['data'].items():
                    print(f"  {key}: {value}")
                    
    except Exception as e:
        print(f"Manueller Parsing Test Fehler: {e}")

def main():
    """Hauptfunktion"""
    print("Netzwerkkarten-Erkennungs-Test")
    print("=" * 40)
    
    # Logging für Debug
    logging.basicConfig(level=logging.DEBUG)
    
    test_ipconfig()
    test_manual_parsing()
    test_discovery()
    
    print("\n" + "=" * 40)
    print("Test abgeschlossen")

if __name__ == "__main__":
    main()
