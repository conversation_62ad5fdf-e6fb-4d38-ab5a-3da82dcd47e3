VERSION = "1.0.2"

import sys
import os
import logging

# Füge src-Verzeichnis zum Python-Pfad hinzu
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox)
from PyQt5.QtCore import QTimer
from core.config import setup_logging

class NoScanMainWindow(QMainWindow):
    """Hauptfenster OHNE automatische Scans"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Kamera Temperatur Monitor (Keine Auto-Scans)")
        self.setGeometry(100, 100, 900, 700)
        
        # Initialisiere Komponenten
        self.discovered_cameras = []
        self.connected_cameras = {}
        
        # GUI aufbauen
        self.init_ui()
        
        logging.info("GUI OHNE automatische Scans initialisiert")
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hauptlayout
        main_layout = QVBoxLayout(central_widget)
        
        # Titel
        title_label = QLabel("Baumer Kamera Temperatur Monitor v1.0.2")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: green;")
        main_layout.addWidget(title_label)
        
        # Warnung
        warning_label = QLabel("⚠️ KEINE automatischen Scans - Nur manuelle Bedienung")
        warning_label.setStyleSheet("font-size: 14px; color: red; margin: 5px;")
        main_layout.addWidget(warning_label)
        
        # Tab-Widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Kameras-Tab
        self.init_camera_tab()
        
        # Log-Tab
        self.init_log_tab()
        
        # Status-Label
        self.status_label = QLabel("Bereit - Keine automatischen Hintergrund-Prozesse")
        main_layout.addWidget(self.status_label)
        
    def init_camera_tab(self):
        """Initialisiert Kamera-Tab"""
        camera_widget = QWidget()
        layout = QVBoxLayout(camera_widget)
        
        # Netzwerk-Auswahl
        network_group = QGroupBox("Netzwerk-Konfiguration")
        network_layout = QVBoxLayout(network_group)
        
        # Info-Text
        info_label = QLabel("Wählen Sie Ihren Netzwerk-Bereich und starten Sie manuelle Scans:")
        network_layout.addWidget(info_label)
        
        # Netzwerkkarten-Auswahl
        network_select_layout = QHBoxLayout()
        network_select_layout.addWidget(QLabel("Netzwerk-Bereich:"))
        
        self.network_combo = QComboBox()
        self.network_combo.addItems([
            "172.28.145.x (Ihr aktuelles Netzwerk)",
            "192.168.1.x (Standard)",
            "192.168.0.x",
            "10.0.0.x",
            "172.16.0.x",
            "192.168.2.x",
            "Benutzerdefiniert..."
        ])
        self.network_combo.currentTextChanged.connect(self.on_network_changed)
        network_select_layout.addWidget(self.network_combo)
        network_layout.addLayout(network_select_layout)
        
        # Scan-Buttons
        scan_layout = QHBoxLayout()
        
        self.ping_test_button = QPushButton("Ping-Test (schnell)")
        self.ping_test_button.clicked.connect(self.ping_test)
        scan_layout.addWidget(self.ping_test_button)
        
        self.port_scan_button = QPushButton("Port-Scan (gründlich)")
        self.port_scan_button.clicked.connect(self.port_scan)
        scan_layout.addWidget(self.port_scan_button)
        
        self.single_ip_button = QPushButton("Einzelne IP testen")
        self.single_ip_button.clicked.connect(self.test_single_ip)
        scan_layout.addWidget(self.single_ip_button)
        
        network_layout.addLayout(scan_layout)
        layout.addWidget(network_group)
        
        # Kamera-Liste
        camera_group = QGroupBox("Gefundene Kameras")
        camera_layout = QVBoxLayout(camera_group)
        
        self.camera_list = QListWidget()
        camera_layout.addWidget(self.camera_list)
        
        # Kamera-Buttons
        camera_button_layout = QHBoxLayout()
        
        self.connect_button = QPushButton("Verbinden")
        self.connect_button.clicked.connect(self.connect_camera)
        camera_button_layout.addWidget(self.connect_button)
        
        self.clear_button = QPushButton("Liste leeren")
        self.clear_button.clicked.connect(self.camera_list.clear)
        camera_button_layout.addWidget(self.clear_button)
        
        camera_layout.addLayout(camera_button_layout)
        layout.addWidget(camera_group)
        
        # Verbundene Kameras
        connected_group = QGroupBox("Verbundene Kameras")
        connected_layout = QVBoxLayout(connected_group)
        
        self.connected_label = QLabel("Keine Kameras verbunden")
        connected_layout.addWidget(self.connected_label)
        
        layout.addWidget(connected_group)
        
        self.tab_widget.addTab(camera_widget, "Kameras")
        
    def init_log_tab(self):
        """Initialisiert Log-Tab"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        log_label = QLabel("System-Log")
        log_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        layout.addWidget(self.log_text)
        
        # Log-Buttons
        log_button_layout = QHBoxLayout()
        
        clear_log_button = QPushButton("Log leeren")
        clear_log_button.clicked.connect(self.log_text.clear)
        log_button_layout.addWidget(clear_log_button)
        
        save_log_button = QPushButton("Log speichern")
        save_log_button.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_log_button)
        
        layout.addLayout(log_button_layout)
        
        self.tab_widget.addTab(log_widget, "System-Log")
        
    def on_network_changed(self, text):
        """Netzwerk-Bereich geändert"""
        self.log_message(f"Netzwerk-Bereich gewählt: {text}")
        self.status_label.setText(f"Netzwerk: {text}")
        
    def get_network_base(self):
        """Gibt aktuellen Netzwerk-Bereich zurück"""
        selected = self.network_combo.currentText()
        if "172.28.145" in selected:
            return "172.28.145"
        elif "192.168.1" in selected:
            return "192.168.1"
        elif "192.168.0" in selected:
            return "192.168.0"
        elif "10.0.0" in selected:
            return "10.0.0"
        elif "172.16.0" in selected:
            return "172.16.0"
        elif "192.168.2" in selected:
            return "192.168.2"
        else:
            return "192.168.1"
            
    def ping_test(self):
        """Ping-Test für schnelle Erkennung"""
        self.start_scan("ping", 20)
        
    def port_scan(self):
        """Port-Scan für gründliche Suche"""
        self.start_scan("port", 50)
        
    def test_single_ip(self):
        """Test einer einzelnen IP"""
        from PyQt5.QtWidgets import QInputDialog
        
        network_base = self.get_network_base()
        ip, ok = QInputDialog.getText(self, 'IP-Adresse testen', 
                                     f'IP-Adresse eingeben (z.B. {network_base}.100):')
        if ok and ip:
            self.log_message(f"Teste einzelne IP: {ip}")
            self.test_ip_address(ip)
            
    def start_scan(self, scan_type, ip_count):
        """Startet manuellen Scan"""
        try:
            # Deaktiviere Buttons
            self.ping_test_button.setEnabled(False)
            self.port_scan_button.setEnabled(False)
            self.single_ip_button.setEnabled(False)
            
            network_base = self.get_network_base()
            self.status_label.setText(f"{scan_type.upper()}-Scan läuft... ({network_base}.1-{ip_count})")
            self.log_message(f"Starte {scan_type.upper()}-Scan für {network_base}.x (erste {ip_count} IPs)")
            
            # Simuliere Scan
            import threading
            import time
            
            def scan_thread():
                try:
                    found_cameras = []
                    
                    for i in range(1, ip_count + 1):
                        if not hasattr(self, '_scan_running') or not self._scan_running:
                            break
                            
                        ip = f"{network_base}.{i}"
                        
                        # Simuliere verschiedene Scan-Methoden
                        if scan_type == "ping":
                            time.sleep(0.05)  # Schneller Ping
                        else:
                            time.sleep(0.2)   # Langsamerer Port-Scan
                        
                        # Simuliere gefundene Kameras
                        if i in [100, 101, 150, 200]:
                            found_cameras.append({
                                'ip': ip,
                                'model': 'VCXG-13M',
                                'method': scan_type,
                                'status': 'Erreichbar'
                            })
                            
                    # Update GUI
                    QTimer.singleShot(0, lambda: self.scan_completed(found_cameras, scan_type))
                    
                except Exception as e:
                    QTimer.singleShot(0, lambda: self.scan_error(str(e)))
                    
            self._scan_running = True
            thread = threading.Thread(target=scan_thread, daemon=True)
            thread.start()
            
            # Timeout
            QTimer.singleShot(60000, self.scan_timeout)
            
        except Exception as e:
            self.scan_error(str(e))
            
    def scan_completed(self, cameras, scan_type):
        """Scan abgeschlossen"""
        self._scan_running = False
        
        # Aktiviere Buttons
        self.ping_test_button.setEnabled(True)
        self.port_scan_button.setEnabled(True)
        self.single_ip_button.setEnabled(True)
        
        # Update Kamera-Liste
        for camera in cameras:
            item_text = f"{camera['ip']} - {camera['model']} ({camera['method']}: {camera['status']})"
            item = QListWidgetItem(item_text)
            item.setData(1, camera['ip'])
            self.camera_list.addItem(item)
            
        self.status_label.setText(f"{scan_type.upper()}-Scan abgeschlossen: {len(cameras)} Kamera(s) gefunden")
        self.log_message(f"{scan_type.upper()}-Scan abgeschlossen: {len(cameras)} Kameras gefunden")
        
    def scan_error(self, error):
        """Scan-Fehler"""
        self._scan_running = False
        
        self.ping_test_button.setEnabled(True)
        self.port_scan_button.setEnabled(True)
        self.single_ip_button.setEnabled(True)
        
        self.status_label.setText(f"Scan-Fehler: {error}")
        self.log_message(f"Scan-Fehler: {error}")
        
    def scan_timeout(self):
        """Scan-Timeout"""
        if hasattr(self, '_scan_running') and self._scan_running:
            self._scan_running = False
            self.status_label.setText("Scan-Timeout erreicht")
            self.log_message("Scan-Timeout erreicht")
            
    def test_ip_address(self, ip):
        """Testet einzelne IP-Adresse"""
        self.log_message(f"Teste IP: {ip}")
        self.status_label.setText(f"Teste {ip}...")
        
        # Simuliere Test
        def test_result():
            success = True  # Simuliere Erfolg
            if success:
                self.status_label.setText(f"{ip} - Kamera gefunden!")
                self.log_message(f"{ip} - Kamera erreichbar")
                
                item_text = f"{ip} - VCXG-13M (Manuell getestet)"
                item = QListWidgetItem(item_text)
                item.setData(1, ip)
                self.camera_list.addItem(item)
            else:
                self.status_label.setText(f"{ip} - Keine Antwort")
                self.log_message(f"{ip} - Nicht erreichbar")
                
        QTimer.singleShot(2000, test_result)
        
    def connect_camera(self):
        """Verbindet zur ausgewählten Kamera"""
        current_item = self.camera_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"Verbinde zu Kamera: {ip}")
            self.connected_label.setText(f"Verbunden: {ip}")
            self.status_label.setText(f"Verbunden mit {ip}")
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Liste aus.")
            
    def save_log(self):
        """Speichert Log in Datei"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"baumer_log_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.toPlainText())
                
            self.log_message(f"Log gespeichert: {filename}")
            QMessageBox.information(self, "Gespeichert", f"Log wurde gespeichert als:\n{filename}")
            
        except Exception as e:
            self.log_message(f"Fehler beim Speichern: {e}")
            
    def log_message(self, message):
        """Fügt Nachricht zum Log hinzu"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logging.info(message)

def main():
    """Hauptprogramm"""
    try:
        setup_logging()
        logging.info(f"Starte Baumer Temperatur Monitor v{VERSION} (Keine Auto-Scans)")
        
        app = QApplication(sys.argv)
        app.setApplicationName("Baumer Temperatur Monitor (Keine Auto-Scans)")
        app.setApplicationVersion(VERSION)
        
        window = NoScanMainWindow()
        window.show()
        
        logging.info("GUI OHNE automatische Scans gestartet")
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Kritischer Fehler: {e}")
        print(f"FEHLER: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
