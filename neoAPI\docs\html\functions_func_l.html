<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members - Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_l"></a>- l -</h3><ul>
<li>LensDistortionCorrectionEnable()
: <a class="el" href="a00763.html#a5d48c7a65ebebc3a6ef97342b31cf6d3">neoapi.FeatureAccess</a>
</li>
<li>LineDebouncerHighTime()
: <a class="el" href="a00763.html#a2787f1bb9fe75118b6350c5eb3f12a2d">neoapi.FeatureAccess</a>
</li>
<li>LineDebouncerHighTimeAbs()
: <a class="el" href="a00763.html#a4b157fd0f291efbfa8407147414d6c0f">neoapi.FeatureAccess</a>
</li>
<li>LineDebouncerLowTime()
: <a class="el" href="a00763.html#a323572c89d2a23eef5af741f6459a478">neoapi.FeatureAccess</a>
</li>
<li>LineDebouncerLowTimeAbs()
: <a class="el" href="a00763.html#a1ba62c7e5e53250186939752b7a3adde">neoapi.FeatureAccess</a>
</li>
<li>LineFormat()
: <a class="el" href="a00763.html#a5c7b6b579f3ef8305e505b23af35a799">neoapi.FeatureAccess</a>
</li>
<li>LineInverter()
: <a class="el" href="a00763.html#a1a4198f5909aafa749c7280cec23651c">neoapi.FeatureAccess</a>
</li>
<li>LineLengthMin()
: <a class="el" href="a00763.html#aa0db0d4863a47d85714f037a182b2f48">neoapi.FeatureAccess</a>
</li>
<li>LineMode()
: <a class="el" href="a00763.html#af2ed27eecbd1be7606a1f6e15e894179">neoapi.FeatureAccess</a>
</li>
<li>LinePWMConfigurationMode()
: <a class="el" href="a00763.html#a3e3e1c9085850050d448d8fc23043f9a">neoapi.FeatureAccess</a>
</li>
<li>LinePWMDuration()
: <a class="el" href="a00763.html#ac11bf95e136c7f33dc52e10bebcfc4dc">neoapi.FeatureAccess</a>
</li>
<li>LinePWMDutyCycle()
: <a class="el" href="a00763.html#ad9b7cd2cda8328d6ccb756a681a667cf">neoapi.FeatureAccess</a>
</li>
<li>LinePWMMaxDuration()
: <a class="el" href="a00763.html#ab03fba776dd98583f8adc80c816bfac2">neoapi.FeatureAccess</a>
</li>
<li>LinePWMMaxDutyCycle()
: <a class="el" href="a00763.html#a32c024efe2f904a1cd626c18be7ae94c">neoapi.FeatureAccess</a>
</li>
<li>LinePWMMode()
: <a class="el" href="a00763.html#a597ecd93db2da21138d22ebaf2e79de9">neoapi.FeatureAccess</a>
</li>
<li>LinePWMOffTime()
: <a class="el" href="a00763.html#a9ac2ef87ca730bf6c487a49b68fc9c32">neoapi.FeatureAccess</a>
</li>
<li>LinePWMPeriodTime()
: <a class="el" href="a00763.html#a1239e128f78b9a74d9f5447330eba2e0">neoapi.FeatureAccess</a>
</li>
<li>LinePWMVersion()
: <a class="el" href="a00763.html#a31d50ca5b73c56f46d4e465f5cda1dd2">neoapi.FeatureAccess</a>
</li>
<li>LineSelector()
: <a class="el" href="a00763.html#a8f7995e4d052b90a9df5f159e246ada9">neoapi.FeatureAccess</a>
</li>
<li>LineSource()
: <a class="el" href="a00763.html#af4682430836cb576102089629445a2ff">neoapi.FeatureAccess</a>
</li>
<li>LineStatus()
: <a class="el" href="a00763.html#a633c9e8bd36e61af22406e1e6dee3c8a">neoapi.FeatureAccess</a>
</li>
<li>LineStatusAll()
: <a class="el" href="a00763.html#ae3dfc53bdad54aea8cdd4980eec60bb4">neoapi.FeatureAccess</a>
</li>
<li>LogCallback()
: <a class="el" href="a00895.html#af3f037865c2b5490b1fe7d7f21dc793e">neoapi.NeoTraceCallback</a>
</li>
<li>LostEventCounter()
: <a class="el" href="a00763.html#ac9bfe349b7a20921cdcf602f4f4b6815">neoapi.FeatureAccess</a>
</li>
<li>LUTContent()
: <a class="el" href="a00763.html#a1306ffa531995372488d0b5b63a4c285">neoapi.FeatureAccess</a>
</li>
<li>LUTEnable()
: <a class="el" href="a00763.html#ad63788cdead81a9835f39bdae56b5781">neoapi.FeatureAccess</a>
</li>
<li>LUTIndex()
: <a class="el" href="a00763.html#aae044b3c9ee74bbdf69edbc64a73b7fb">neoapi.FeatureAccess</a>
</li>
<li>LUTSelector()
: <a class="el" href="a00763.html#abdb5ac2f9a80f9ab7dc500a0db9ac281">neoapi.FeatureAccess</a>
</li>
<li>LUTValue()
: <a class="el" href="a00763.html#ac9a954376d0cb603cd7f472bc4486425">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
