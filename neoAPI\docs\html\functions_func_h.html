<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members - Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_h"></a>- h -</h3><ul>
<li>HasFeature()
: <a class="el" href="a00859.html#a374c5e3200cab8d7464bc907811ba035">neoapi.CamBase</a>
, <a class="el" href="a00815.html#a8fb45ac6412d880875d9df5a5a99ef80">neoapi.FeatureList</a>
</li>
<li>HDREnable()
: <a class="el" href="a00763.html#a857e6b2001cc3a7d3cae2501e64488ea">neoapi.FeatureAccess</a>
</li>
<li>HDREnableTriggerAutoMode()
: <a class="el" href="a00763.html#a149f71bab6d899ae3cc66ff2509e10dc">neoapi.FeatureAccess</a>
</li>
<li>HDRExposureRatio()
: <a class="el" href="a00763.html#afc0ca1a45c966ae9e26e5f25d9e2c2ff">neoapi.FeatureAccess</a>
</li>
<li>HDRExposureRatioPercent()
: <a class="el" href="a00763.html#a381cfea6eb1f9e54cad22baa6bcf8ca2">neoapi.FeatureAccess</a>
</li>
<li>HDRExposureTimeBrightArea()
: <a class="el" href="a00763.html#a2ce4f206eef499cb4b449731ad2974a6">neoapi.FeatureAccess</a>
</li>
<li>HDRExposureTimeDarkArea()
: <a class="el" href="a00763.html#a1c813d09dfeb493f3343ea94d2a5aa19">neoapi.FeatureAccess</a>
</li>
<li>HDRExposureTimeRatio()
: <a class="el" href="a00763.html#aa43eb37baec058800981d6f370774fe4">neoapi.FeatureAccess</a>
</li>
<li>HDRGainBrightArea()
: <a class="el" href="a00763.html#a6e4f8aee1258648330bc6a7d7d354952">neoapi.FeatureAccess</a>
</li>
<li>HDRGainDarkArea()
: <a class="el" href="a00763.html#a7193e6d98d4e4d108912ec60c760c661">neoapi.FeatureAccess</a>
</li>
<li>HDRGainRatio()
: <a class="el" href="a00763.html#acefb3f7ecc410f804045c455392d459f">neoapi.FeatureAccess</a>
</li>
<li>HDRGainRatioSelector()
: <a class="el" href="a00763.html#a19cc200b1689861bbdb206c49b39e8c8">neoapi.FeatureAccess</a>
</li>
<li>HDRIndex()
: <a class="el" href="a00763.html#ae0c8034b3b5f40c5db3bf78bb40c0c06">neoapi.FeatureAccess</a>
</li>
<li>HDRPotentialAbs()
: <a class="el" href="a00763.html#aab0dd608c4cea5e825cb1377e690c991">neoapi.FeatureAccess</a>
</li>
<li>HDRProcessingEnable()
: <a class="el" href="a00763.html#a2aa0572d81b7b4b23aa14a94a2dc09dd">neoapi.FeatureAccess</a>
</li>
<li>HDRProcessingSmoothingEnable()
: <a class="el" href="a00763.html#acae9d6dc5456e7ae0a91d9398ae4baff">neoapi.FeatureAccess</a>
</li>
<li>HDRProcessingThresholdMax()
: <a class="el" href="a00763.html#a85c785b1d32352cefa164d6f84dd03b4">neoapi.FeatureAccess</a>
</li>
<li>HDRProcessingThresholdMin()
: <a class="el" href="a00763.html#acac6f519edca61d522d2aead5b4bdfc4">neoapi.FeatureAccess</a>
</li>
<li>HDRSplitviewEnable()
: <a class="el" href="a00763.html#a1d6e75800b54aebd9c3ae0a2d1693b2b">neoapi.FeatureAccess</a>
</li>
<li>HDRTonemappingCurveGradient()
: <a class="el" href="a00763.html#aeb2c70d7509e5970619f1a6d0e5ab378">neoapi.FeatureAccess</a>
</li>
<li>HDRTonemappingCurveGridpoint()
: <a class="el" href="a00763.html#a226ec26c033cd1161ad370c8f2393fab">neoapi.FeatureAccess</a>
</li>
<li>HDRTonemappingCurveGridpointIndex()
: <a class="el" href="a00763.html#a09f5469bf02b6575946d7de89a7c4b88">neoapi.FeatureAccess</a>
</li>
<li>HDRTonemappingCurveOffset()
: <a class="el" href="a00763.html#a337d82aebd67c5b51380275e8fde8741">neoapi.FeatureAccess</a>
</li>
<li>HDRTonemappingCurvePresetSelector()
: <a class="el" href="a00763.html#a38b701c9afb7f1307afde3234f993bf7">neoapi.FeatureAccess</a>
</li>
<li>HDRTonemappingCurveResetToPreset()
: <a class="el" href="a00763.html#a100fb1538c0b7189ae045e1e2e50783a">neoapi.FeatureAccess</a>
</li>
<li>HDRTonemappingEnable()
: <a class="el" href="a00763.html#a0889a7bb2b57c3fdb9c3a3f445b7be35">neoapi.FeatureAccess</a>
</li>
<li>HDRTonemappingMax()
: <a class="el" href="a00763.html#a7e5286f2a640cb09fd4d55f5c8d9ada4">neoapi.FeatureAccess</a>
</li>
<li>HDRTonemappingMean()
: <a class="el" href="a00763.html#a6adf5d1049943c766b32bdf36f650dff">neoapi.FeatureAccess</a>
</li>
<li>Height()
: <a class="el" href="a00763.html#a422553dc95095970a0c7ec19e320b522">neoapi.FeatureAccess</a>
</li>
<li>HeightMax()
: <a class="el" href="a00763.html#a4f970d927afbf97af680b21cd339a850">neoapi.FeatureAccess</a>
</li>
<li>HighConversionGain()
: <a class="el" href="a00763.html#aeba66ca6d39328855120bd7ca5c6d08a">neoapi.FeatureAccess</a>
</li>
<li>HighConversionGainEnable()
: <a class="el" href="a00763.html#a12bc8847c25ef3ce4b185e1b4928f195">neoapi.FeatureAccess</a>
</li>
<li>HQModeEnable()
: <a class="el" href="a00763.html#a8ebf930eac88379045826d5a99e47f7d">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
