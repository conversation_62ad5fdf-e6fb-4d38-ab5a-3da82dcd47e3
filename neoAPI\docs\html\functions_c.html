<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_c"></a>- c -</h3><ul>
<li>CalibrationAngleOfPolarizationOffset()
: <a class="el" href="a00763.html#aadc4f9ccac2be7f8e1d6ff2fe41b1e4a">neoapi.FeatureAccess</a>
</li>
<li>CalibrationEnable()
: <a class="el" href="a00763.html#aa8688d60723c7e7f5020c046e0d0f106">neoapi.FeatureAccess</a>
</li>
<li>CalibrationMatrixColorSelector()
: <a class="el" href="a00763.html#a0408f7d12259cdcf93dd878a6af1d841">neoapi.FeatureAccess</a>
</li>
<li>CalibrationMatrixValue()
: <a class="el" href="a00763.html#ab41c79998238d7139da3b538a405d404">neoapi.FeatureAccess</a>
</li>
<li>CalibrationMatrixValueSelector()
: <a class="el" href="a00763.html#a51c28171a83692f1c59a4d881f9cb8a2">neoapi.FeatureAccess</a>
</li>
<li>ChunkActionRequestID()
: <a class="el" href="a00763.html#aa198ed90b0f6fd2a405cf352ef817458">neoapi.FeatureAccess</a>
</li>
<li>ChunkActionSourceIP()
: <a class="el" href="a00763.html#a11634467d8a79cbb2d9183a02cc59951">neoapi.FeatureAccess</a>
</li>
<li>ChunkBinningRegion0()
: <a class="el" href="a00763.html#a9ec41388058a3add3de82cc1cb2a5851">neoapi.FeatureAccess</a>
</li>
<li>ChunkBinningSensor()
: <a class="el" href="a00763.html#ab0620e227bd49428f81bf587dc5fb792">neoapi.FeatureAccess</a>
</li>
<li>ChunkEnable()
: <a class="el" href="a00763.html#a4e6b5ad2a1a460a754e2ca66de4473f8">neoapi.FeatureAccess</a>
</li>
<li>ChunkModeActive()
: <a class="el" href="a00763.html#a4a7e15e6f65954c73df38b30fd24a022">neoapi.FeatureAccess</a>
</li>
<li>ChunkSelector()
: <a class="el" href="a00763.html#a2d3aaf3a00ec05ff2bbe0597ff55f8bc">neoapi.FeatureAccess</a>
</li>
<li>ChunkTriggerCounter()
: <a class="el" href="a00763.html#a93cad7c1c040c829941e88c6770ac3f5">neoapi.FeatureAccess</a>
</li>
<li>ClConfiguration()
: <a class="el" href="a00763.html#af526143087283db0755b565c57059bb3">neoapi.FeatureAccess</a>
</li>
<li>Clear()
: <a class="el" href="a00867.html#a736e337fa52dd92bb4143cfde205b762">neoapi.FeatureStack</a>
</li>
<li>ClearEvents()
: <a class="el" href="a00863.html#afa62e4ad2e20e3f8e77652168ada7ce1">neoapi.Cam</a>
, <a class="el" href="a00859.html#a9e1d7d205673d544624dffca42d14fca">neoapi.CamBase</a>
</li>
<li>ClearImages()
: <a class="el" href="a00863.html#a468a099f074de5e226b222d191536160">neoapi.Cam</a>
, <a class="el" href="a00859.html#afcc7b65f478ca08c79ab839b607efb6a">neoapi.CamBase</a>
</li>
<li>ClearPnPEvents()
: <a class="el" href="a00863.html#ab07f5cc12958e241b180b20e3e00cc47">neoapi.Cam</a>
, <a class="el" href="a00859.html#abacf893cc84a9a97b55a6c0be753c10d">neoapi.CamBase</a>
, <a class="el" href="a00891.html#a765baee4d9cb0a86c9ba7be81f75bec7">neoapi.CamInfoList</a>
</li>
<li>CLFVALLowTime()
: <a class="el" href="a00763.html#ad851f388be8d1716c9f3a26452873166">neoapi.FeatureAccess</a>
</li>
<li>CLLVALLowTime()
: <a class="el" href="a00763.html#ac0c2918e60afe9f9443a63829bade3c3">neoapi.FeatureAccess</a>
</li>
<li>CLMCRC()
: <a class="el" href="a00763.html#a94905693e3a1f83fcb3a23ed173b9689">neoapi.FeatureAccess</a>
</li>
<li>CLMCTT()
: <a class="el" href="a00763.html#a9d590fc1fba6fb6de57b350179c8b427">neoapi.FeatureAccess</a>
</li>
<li>ClTimeSlotsCount()
: <a class="el" href="a00763.html#aabd21cdcd175d0405fe836023e149183">neoapi.FeatureAccess</a>
</li>
<li>ColorTransformationAuto()
: <a class="el" href="a00763.html#a689eab5fcefdbfbed5d377fe6d5c82bf">neoapi.FeatureAccess</a>
</li>
<li>ColorTransformationEnable()
: <a class="el" href="a00763.html#add998fe98da3c49b79a8e3d9c1ae98aa">neoapi.FeatureAccess</a>
</li>
<li>ColorTransformationFactoryListSelector()
: <a class="el" href="a00763.html#aa94aa78dad6d1fd0f33cd38160712a45">neoapi.FeatureAccess</a>
</li>
<li>ColorTransformationOutputColorSpace()
: <a class="el" href="a00763.html#a566777c3429f80e418d109da37d906be">neoapi.FeatureAccess</a>
</li>
<li>ColorTransformationResetToFactoryList()
: <a class="el" href="a00763.html#a7b60b08cea636cd87f860cfbe447a035">neoapi.FeatureAccess</a>
</li>
<li>ColorTransformationSelector()
: <a class="el" href="a00763.html#ab5ef6ea5d0f7f6f5cfb73f7b2317b75d">neoapi.FeatureAccess</a>
</li>
<li>ColorTransformationValue()
: <a class="el" href="a00763.html#ac4f5fbef38c21be7ec8e731b8b00eb8c">neoapi.FeatureAccess</a>
</li>
<li>ColorTransformationValueSelector()
: <a class="el" href="a00763.html#acc0077fcb3a0bcc05b42e47ee5e476fa">neoapi.FeatureAccess</a>
</li>
<li>ComponentEnable()
: <a class="el" href="a00763.html#a989ea09b12296298acc2f19cf7cd3be8">neoapi.FeatureAccess</a>
</li>
<li>ComponentSelector()
: <a class="el" href="a00763.html#a99cee45aeeefe18e907f7a3bfa18f038">neoapi.FeatureAccess</a>
</li>
<li>ConcatenationEnable()
: <a class="el" href="a00763.html#a7b844a39db4511b13f0da09e31747ca7">neoapi.FeatureAccess</a>
</li>
<li>Connect()
: <a class="el" href="a00863.html#af9342490a56163f394c07abf61064880">neoapi.Cam</a>
, <a class="el" href="a00859.html#a723ec46c77f3b734328a9f40e7aa1c19">neoapi.CamBase</a>
</li>
<li>Convert()
: <a class="el" href="a00855.html#af9fd5b5a5d619eb69e2c6c49e51c5e70">neoapi.Image</a>
</li>
<li>Copy()
: <a class="el" href="a00855.html#a0688ec4d1bb9e85d943512fdf0fa6c54">neoapi.Image</a>
</li>
<li>CounterDuration()
: <a class="el" href="a00763.html#ae28838eba5ea3065459d27c89c99b42d">neoapi.FeatureAccess</a>
</li>
<li>CounterEventActivation()
: <a class="el" href="a00763.html#aef566684e74191bd3fe6560202790fa5">neoapi.FeatureAccess</a>
</li>
<li>CounterEventSource()
: <a class="el" href="a00763.html#a5d65f46be043b6c0c94576ecda43b1ac">neoapi.FeatureAccess</a>
</li>
<li>CounterReset()
: <a class="el" href="a00763.html#a02780c05eed6d90d1780c54ab57f2e92">neoapi.FeatureAccess</a>
</li>
<li>CounterResetActivation()
: <a class="el" href="a00763.html#a04c4f8b5a1214e86b318fd963846adce">neoapi.FeatureAccess</a>
</li>
<li>CounterResetSource()
: <a class="el" href="a00763.html#a16c4081c46a76f7d6420be7320c8b16f">neoapi.FeatureAccess</a>
</li>
<li>CounterSelector()
: <a class="el" href="a00763.html#a2eef40776dd24d84a0900fc577010376">neoapi.FeatureAccess</a>
</li>
<li>CounterValue()
: <a class="el" href="a00763.html#a333b6d8b0f67e3fe2ec8a65ff3af2bf8">neoapi.FeatureAccess</a>
</li>
<li>CounterValueAtReset()
: <a class="el" href="a00763.html#a066dc66bdb5318c99555458aef30bf27">neoapi.FeatureAccess</a>
</li>
<li>CustomData()
: <a class="el" href="a00763.html#a3936fa4b5b4651290d420e5d9364fd83">neoapi.FeatureAccess</a>
</li>
<li>CustomDataConfigurationMode()
: <a class="el" href="a00763.html#a3566966ad86255b6823cd15076f302b9">neoapi.FeatureAccess</a>
</li>
<li>CustomDataSelector()
: <a class="el" href="a00763.html#a4b11a1e46f7c25f1691e8a930ab276fd">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
