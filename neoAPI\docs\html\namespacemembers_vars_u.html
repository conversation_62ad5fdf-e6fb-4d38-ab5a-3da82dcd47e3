<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
&#160;

<h3><a id="index_u"></a>- u -</h3><ul>
<li>UserOutputSelector_UserOutput0
: <a class="el" href="a00091.html#ae91d49310138c09b8330d42b97c36bd7">neoapi</a>
</li>
<li>UserOutputSelector_UserOutput1
: <a class="el" href="a00091.html#a2a399bf28957693e56e4f60021849f93">neoapi</a>
</li>
<li>UserOutputSelector_UserOutput2
: <a class="el" href="a00091.html#ad5b5769acad5646157197b3b0778cb4d">neoapi</a>
</li>
<li>UserOutputSelector_UserOutput3
: <a class="el" href="a00091.html#a3b58581b0046de2afe10c5b9497cb12a">neoapi</a>
</li>
<li>UserOutputSelector_UserOutput4
: <a class="el" href="a00091.html#a0d518c05f70f41f2d27740e5e303dff5">neoapi</a>
</li>
<li>UserSetDefault_Default
: <a class="el" href="a00091.html#a00957d829b0dda1cd94aa00d1ad0f4f3">neoapi</a>
</li>
<li>UserSetDefault_UserSet0
: <a class="el" href="a00091.html#aeeaab3aa1684e1f4a89e8f0b4ce7637e">neoapi</a>
</li>
<li>UserSetDefault_UserSet1
: <a class="el" href="a00091.html#ac3357f47201cca842303236043a16e5b">neoapi</a>
</li>
<li>UserSetDefault_UserSet2
: <a class="el" href="a00091.html#aa22b22ca22f5e8a9b3f5326403301e74">neoapi</a>
</li>
<li>UserSetDefault_UserSet3
: <a class="el" href="a00091.html#a725ff3e60abe87b42f6646d59adf78cb">neoapi</a>
</li>
<li>UserSetFeatureSelector_AcquisitionFrameCount
: <a class="el" href="a00091.html#a9d5362530cc6f32a31540c27fd70caf7">neoapi</a>
</li>
<li>UserSetFeatureSelector_AcquisitionFrameRate
: <a class="el" href="a00091.html#a7db83bebdcff4fd666c09b86a1a39dc9">neoapi</a>
</li>
<li>UserSetFeatureSelector_AcquisitionFrameRateEnable
: <a class="el" href="a00091.html#a45ba7e04e041168f6cbdb3f0dd88176d">neoapi</a>
</li>
<li>UserSetFeatureSelector_AcquisitionMode
: <a class="el" href="a00091.html#a9d9869532f00ac0589eedf712cc63630">neoapi</a>
</li>
<li>UserSetFeatureSelector_ActionDeviceKey
: <a class="el" href="a00091.html#ae1aadfe2b71767c9fc1f9eb7d38c52fd">neoapi</a>
</li>
<li>UserSetFeatureSelector_ActionGroupKey
: <a class="el" href="a00091.html#a27a3db1585c13989fc27e9bc69b467f8">neoapi</a>
</li>
<li>UserSetFeatureSelector_ActionGroupMask
: <a class="el" href="a00091.html#ad0b7548ea511d989385c28324c5cefb5">neoapi</a>
</li>
<li>UserSetFeatureSelector_AutoFeatureHeight
: <a class="el" href="a00091.html#a3fed5fccdf355e1168b0352b93e10bd7">neoapi</a>
</li>
<li>UserSetFeatureSelector_AutoFeatureOffsetX
: <a class="el" href="a00091.html#a902a3c55cd68d3cb1713bcde5576a99a">neoapi</a>
</li>
<li>UserSetFeatureSelector_AutoFeatureOffsetY
: <a class="el" href="a00091.html#ae13f250c7988a111a61d25fddf436d6d">neoapi</a>
</li>
<li>UserSetFeatureSelector_AutoFeatureRegionMode
: <a class="el" href="a00091.html#a47095dfe46ed12f0a1413128113fb275">neoapi</a>
</li>
<li>UserSetFeatureSelector_AutoFeatureRegionReference
: <a class="el" href="a00091.html#acaef2b8d01dd4f9009c395e58f048aa7">neoapi</a>
</li>
<li>UserSetFeatureSelector_AutoFeatureWidth
: <a class="el" href="a00091.html#aceb925f5c72676a50184a5bb3a0d225a">neoapi</a>
</li>
<li>UserSetFeatureSelector_BalanceWhiteAuto
: <a class="el" href="a00091.html#adb740a11ef36df5943c807e82bad3d96">neoapi</a>
</li>
<li>UserSetFeatureSelector_BinningHorizontal
: <a class="el" href="a00091.html#ac0207ef45f2744815be5c9c18790198f">neoapi</a>
</li>
<li>UserSetFeatureSelector_BinningHorizontalMode
: <a class="el" href="a00091.html#ab09950b484b00819a8813d8fb0b113f5">neoapi</a>
</li>
<li>UserSetFeatureSelector_BinningVertical
: <a class="el" href="a00091.html#a87bfe3d3b08290e04c95db965a4450d2">neoapi</a>
</li>
<li>UserSetFeatureSelector_BinningVerticalMode
: <a class="el" href="a00091.html#a1f2f5008b6386beb510baab46702a4f5">neoapi</a>
</li>
<li>UserSetFeatureSelector_BlackLevel
: <a class="el" href="a00091.html#aa38a392820a94f7925cc93bf11eee01e">neoapi</a>
</li>
<li>UserSetFeatureSelector_BOPFShift
: <a class="el" href="a00091.html#a48501ed65019897624593b882ab54bfd">neoapi</a>
</li>
<li>UserSetFeatureSelector_BrightnessAutoNominalValue
: <a class="el" href="a00091.html#a4f9c15a45adb7572c53a6ad3622b95f1">neoapi</a>
</li>
<li>UserSetFeatureSelector_BrightnessAutoPriority
: <a class="el" href="a00091.html#ac935ec68ac893646da1625f190f6ec9c">neoapi</a>
</li>
<li>UserSetFeatureSelector_ChunkEnable
: <a class="el" href="a00091.html#a658d4f51195e99fd4e5a470631dc4da9">neoapi</a>
</li>
<li>UserSetFeatureSelector_ChunkModeActive
: <a class="el" href="a00091.html#a723ca556cb6bde67a66240cfc9b71481">neoapi</a>
</li>
<li>UserSetFeatureSelector_ColorTransformationAuto
: <a class="el" href="a00091.html#a1482fe4ab675b8129bfa9ca2c2905e68">neoapi</a>
</li>
<li>UserSetFeatureSelector_ColorTransformationValue
: <a class="el" href="a00091.html#aab2839094cb2cb4ffe1ceae072d57d9d">neoapi</a>
</li>
<li>UserSetFeatureSelector_CounterDuration
: <a class="el" href="a00091.html#ad5d8a12434089b737d59cc328a48af3a">neoapi</a>
</li>
<li>UserSetFeatureSelector_CounterEventActivation
: <a class="el" href="a00091.html#a55498634d546c49151dd9b9b19131748">neoapi</a>
</li>
<li>UserSetFeatureSelector_CounterEventSource
: <a class="el" href="a00091.html#a866637f3d9f2e07ded13dc9e53e62f3a">neoapi</a>
</li>
<li>UserSetFeatureSelector_CounterResetActivation
: <a class="el" href="a00091.html#afa95322bb57208307a34218a05cf268b">neoapi</a>
</li>
<li>UserSetFeatureSelector_CounterResetSource
: <a class="el" href="a00091.html#a3d1ab08c44b35343cccdf9378ebee7ee">neoapi</a>
</li>
<li>UserSetFeatureSelector_DefectPixelCorrection
: <a class="el" href="a00091.html#a56a67989bd53de10583c2d1b22c8e87f">neoapi</a>
</li>
<li>UserSetFeatureSelector_DeviceLinkThroughputLimit
: <a class="el" href="a00091.html#a7a377939e1ecfbbb36f93c3f57204927">neoapi</a>
</li>
<li>UserSetFeatureSelector_DeviceSpecificFeatureList
: <a class="el" href="a00091.html#ae49b9a6648c50343dc643d19eb754844">neoapi</a>
</li>
<li>UserSetFeatureSelector_DeviceTemperatureStatusTransition
: <a class="el" href="a00091.html#a399dedd0d024ec02f577b67298ea7178">neoapi</a>
</li>
<li>UserSetFeatureSelector_EventNotification
: <a class="el" href="a00091.html#a35ebe2684745933c1df0c7e175653b94">neoapi</a>
</li>
<li>UserSetFeatureSelector_ExposureAuto
: <a class="el" href="a00091.html#ab612cdf35801e547c93e051880a8dfa2">neoapi</a>
</li>
<li>UserSetFeatureSelector_ExposureAutoMaxValue
: <a class="el" href="a00091.html#a9a539f1dfb68a885d39a837db0ef60d7">neoapi</a>
</li>
<li>UserSetFeatureSelector_ExposureAutoMinValue
: <a class="el" href="a00091.html#a69045d5f5df8b8d9a1b9e0ad156cc8c3">neoapi</a>
</li>
<li>UserSetFeatureSelector_ExposureMode
: <a class="el" href="a00091.html#ac2c6902353af9f0c44a7554edf450a5d">neoapi</a>
</li>
<li>UserSetFeatureSelector_ExposureTime
: <a class="el" href="a00091.html#aa00d66f656cdbf3cf779a3caebc66c40">neoapi</a>
</li>
<li>UserSetFeatureSelector_FixedPatternNoiseCorrection
: <a class="el" href="a00091.html#a9a257b2195555b801e481b97f2036e03">neoapi</a>
</li>
<li>UserSetFeatureSelector_FrameCounter
: <a class="el" href="a00091.html#a5c8376f6d1a96905e6075251950ffae3">neoapi</a>
</li>
<li>UserSetFeatureSelector_Gain
: <a class="el" href="a00091.html#a44ac9b0ae9a2fff339d5914d6d0fb523">neoapi</a>
</li>
<li>UserSetFeatureSelector_GainAuto
: <a class="el" href="a00091.html#a3ba384b148ba88b503f4ea5fd8f8d754">neoapi</a>
</li>
<li>UserSetFeatureSelector_GainAutoMaxValue
: <a class="el" href="a00091.html#ab2aecdc798e3dfe1be050c86c8353460">neoapi</a>
</li>
<li>UserSetFeatureSelector_GainAutoMinValue
: <a class="el" href="a00091.html#a645e19b666e612ba3ea2db4a0094834b">neoapi</a>
</li>
<li>UserSetFeatureSelector_Gamma
: <a class="el" href="a00091.html#abf23d21b8c759fb7b5820701254733e0">neoapi</a>
</li>
<li>UserSetFeatureSelector_GevSCFTD
: <a class="el" href="a00091.html#a990d356cdda79b3e4417167a3e7e4a02">neoapi</a>
</li>
<li>UserSetFeatureSelector_GevSCPD
: <a class="el" href="a00091.html#a6c271459bec2430ff46a452eaaa286ef">neoapi</a>
</li>
<li>UserSetFeatureSelector_Height
: <a class="el" href="a00091.html#a7a30236354e2694e35c9307ee027dd2d">neoapi</a>
</li>
<li>UserSetFeatureSelector_LineDebouncerHighTimeAbs
: <a class="el" href="a00091.html#a6fe113af579301f94e727a4227be82fb">neoapi</a>
</li>
<li>UserSetFeatureSelector_LineDebouncerLowTimeAbs
: <a class="el" href="a00091.html#a7f66f94e2a92c00ba7ebafedd5597ec2">neoapi</a>
</li>
<li>UserSetFeatureSelector_LineInverter
: <a class="el" href="a00091.html#a2034cb9b2e9c3ddde9e23512f669a1bb">neoapi</a>
</li>
<li>UserSetFeatureSelector_LineMode
: <a class="el" href="a00091.html#a082a98836198395fddf6b7ca46576e27">neoapi</a>
</li>
<li>UserSetFeatureSelector_LinePWMDuration
: <a class="el" href="a00091.html#acbb82176e02788ac548a5fb95cbfea1d">neoapi</a>
</li>
<li>UserSetFeatureSelector_LinePWMDutyCycle
: <a class="el" href="a00091.html#a5f8be1a3c84098e35966643294687976">neoapi</a>
</li>
<li>UserSetFeatureSelector_LinePWMMaxDuration
: <a class="el" href="a00091.html#a51bacbecb164e832bffcc3b150781d1a">neoapi</a>
</li>
<li>UserSetFeatureSelector_LinePWMMaxDutyCycle
: <a class="el" href="a00091.html#a0b46fb6d5324c991d5acd78903f1f9c7">neoapi</a>
</li>
<li>UserSetFeatureSelector_LinePWMMode
: <a class="el" href="a00091.html#a65570f0843d42c60babc497b30bd76e1">neoapi</a>
</li>
<li>UserSetFeatureSelector_LineSource
: <a class="el" href="a00091.html#a458829b865eaa67d69b92f1b4872e8ad">neoapi</a>
</li>
<li>UserSetFeatureSelector_LUTContent
: <a class="el" href="a00091.html#aadf9749280d59381c90026e2505d7d53">neoapi</a>
</li>
<li>UserSetFeatureSelector_LUTEnable
: <a class="el" href="a00091.html#a221c4a8b473ab21b3f33e646dc11461e">neoapi</a>
</li>
<li>UserSetFeatureSelector_LUTValue
: <a class="el" href="a00091.html#a0349243cbbc309262af93e2a7befbe24">neoapi</a>
</li>
<li>UserSetFeatureSelector_OffsetX
: <a class="el" href="a00091.html#a12de97d2c44e7c045794b5c784d43599">neoapi</a>
</li>
<li>UserSetFeatureSelector_OffsetY
: <a class="el" href="a00091.html#a175ee492fdb766fddf97bbac067524e9">neoapi</a>
</li>
<li>UserSetFeatureSelector_PixelFormat
: <a class="el" href="a00091.html#a7df296b623d5809fc247c88db1807870">neoapi</a>
</li>
<li>UserSetFeatureSelector_PtpEnable
: <a class="el" href="a00091.html#a1d02721f236f57dc285251b959bbfc90">neoapi</a>
</li>
<li>UserSetFeatureSelector_PtpMode
: <a class="el" href="a00091.html#a74d020a1149630e63ac7197e671e330d">neoapi</a>
</li>
<li>UserSetFeatureSelector_ReadoutMode
: <a class="el" href="a00091.html#a9cb54177ecb770b788ba85f131e3a9ce">neoapi</a>
</li>
<li>UserSetFeatureSelector_ReverseX
: <a class="el" href="a00091.html#a1e13afaac4f5ae2cea8e50e80af8cb8d">neoapi</a>
</li>
<li>UserSetFeatureSelector_ReverseY
: <a class="el" href="a00091.html#aae4dacb28696938a306ed823278ef8b9">neoapi</a>
</li>
<li>UserSetFeatureSelector_SensorADDigitization
: <a class="el" href="a00091.html#a295d3aafbe6883ae15b762b826f8b630">neoapi</a>
</li>
<li>UserSetFeatureSelector_SensorShutterMode
: <a class="el" href="a00091.html#a80b31108ff9dd0057371235d4857da3e">neoapi</a>
</li>
<li>UserSetFeatureSelector_SequencerMode
: <a class="el" href="a00091.html#a85a5a6f31fe319133fbbd6fa128c71b5">neoapi</a>
</li>
<li>UserSetFeatureSelector_SequencerSetNext
: <a class="el" href="a00091.html#a6b8ce2d1b924ec95bf2827270eb137fa">neoapi</a>
</li>
<li>UserSetFeatureSelector_SequencerSetStart
: <a class="el" href="a00091.html#ab44e78a95419787338bcb208fc49754e">neoapi</a>
</li>
<li>UserSetFeatureSelector_SequencerTriggerActivation
: <a class="el" href="a00091.html#adc128b906ae3ef8bc4411e2e078f4b4b">neoapi</a>
</li>
<li>UserSetFeatureSelector_SequencerTriggerSource
: <a class="el" href="a00091.html#a205a1f7a05e13d6a61bbff57ad7387c0">neoapi</a>
</li>
<li>UserSetFeatureSelector_ShortExposureTimeEnable
: <a class="el" href="a00091.html#a8fa80e5eec608d7de946644f69e21279">neoapi</a>
</li>
<li>UserSetFeatureSelector_TestPattern
: <a class="el" href="a00091.html#aca73bb1ff3da70c0f349d4c5b2ca4fc4">neoapi</a>
</li>
<li>UserSetFeatureSelector_TimerDelay
: <a class="el" href="a00091.html#a0a51db39dd0574280cb320e1ee632a9a">neoapi</a>
</li>
<li>UserSetFeatureSelector_TimerDuration
: <a class="el" href="a00091.html#a65362edc67ac46af31f4ac9bc810bc62">neoapi</a>
</li>
<li>UserSetFeatureSelector_TimerTriggerActivation
: <a class="el" href="a00091.html#a50660abd37b11dddb19daa747b2ed8e0">neoapi</a>
</li>
<li>UserSetFeatureSelector_TimerTriggerSource
: <a class="el" href="a00091.html#a19b9c10f142e79aa474ec7fa7ddb2fb2">neoapi</a>
</li>
<li>UserSetFeatureSelector_TransferStart
: <a class="el" href="a00091.html#aba143291729de8f6c47517cf2dccadc6">neoapi</a>
</li>
<li>UserSetFeatureSelector_TransferStop
: <a class="el" href="a00091.html#aad3e2787e52667629fa80943f8226e00">neoapi</a>
</li>
<li>UserSetFeatureSelector_TriggerActivation
: <a class="el" href="a00091.html#a8ca5a7c72bf2f07ebbbd7ebe8d246f21">neoapi</a>
</li>
<li>UserSetFeatureSelector_TriggerDelay
: <a class="el" href="a00091.html#a8cb3de2302438dbdb97de78257bca64b">neoapi</a>
</li>
<li>UserSetFeatureSelector_TriggerMode
: <a class="el" href="a00091.html#a95d6e96e21712df0cbd2a63aed6f4ca1">neoapi</a>
</li>
<li>UserSetFeatureSelector_TriggerSource
: <a class="el" href="a00091.html#abd8d2d03c3d493e7313922d0a2536d0f">neoapi</a>
</li>
<li>UserSetFeatureSelector_UserOutputValue
: <a class="el" href="a00091.html#a41d0fda114df8702ddcc18ba82a2edcc">neoapi</a>
</li>
<li>UserSetFeatureSelector_UserOutputValueAll
: <a class="el" href="a00091.html#af8667827d1b789cc243b15b642689a85">neoapi</a>
</li>
<li>UserSetFeatureSelector_Width
: <a class="el" href="a00091.html#a04b77045f51fd64d2ccfab9d8b978a7e">neoapi</a>
</li>
<li>UserSetSelector_Default
: <a class="el" href="a00091.html#ab1040a9c5e3dfdbe06ede30fc41befa5">neoapi</a>
</li>
<li>UserSetSelector_UserSet0
: <a class="el" href="a00091.html#ad1fd08e8fd7e482098247fe7ec9a1bde">neoapi</a>
</li>
<li>UserSetSelector_UserSet1
: <a class="el" href="a00091.html#aeeca0c9f09aeea1dd6b1927a9435857f">neoapi</a>
</li>
<li>UserSetSelector_UserSet2
: <a class="el" href="a00091.html#aa3aff34ca9e205d5421a835a86123f4c">neoapi</a>
</li>
<li>UserSetSelector_UserSet3
: <a class="el" href="a00091.html#adca8a2025a90f233e35581347c398e48">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
