VERSION = "1.0.0"

import sys
import os
import logging
import traceback

# Füge src-Verzeichnis zum Python-Pfad hinzu
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("Python-Pfad:", sys.path[:3])
print("Aktuelles Verzeichnis:", os.getcwd())

try:
    from PyQt5.QtWidgets import QApplication
    print("✓ PyQt5 Import erfolgreich")
except Exception as e:
    print(f"✗ PyQt5 Import Fehler: {e}")
    sys.exit(1)

try:
    from core.config import setup_logging
    print("✓ Config Import erfolgreich")
except Exception as e:
    print(f"✗ Config Import Fehler: {e}")
    traceback.print_exc()

try:
    from gui.main_window import MainWindow
    print("✓ MainWindow Import erfolgreich")
except Exception as e:
    print(f"✗ MainWindow Import Fehler: {e}")
    traceback.print_exc()

def main():
    """Debug-Hauptprogramm"""
    try:
        print("Starte Logging-Setup...")
        setup_logging()
        logging.info(f"Starte Baumer Temperatur Monitor v{VERSION}")
        print("✓ Logging erfolgreich")
        
        print("Erstelle QApplication...")
        app = QApplication(sys.argv)
        app.setApplicationName("Baumer Temperatur Monitor")
        app.setApplicationVersion(VERSION)
        print("✓ QApplication erstellt")
        
        print("Erstelle MainWindow...")
        window = MainWindow()
        print("✓ MainWindow erstellt")
        
        print("Zeige Fenster...")
        window.show()
        print("✓ Fenster angezeigt")
        
        logging.info("GUI gestartet, warte auf Benutzerinteraktion...")
        print("Starte Event-Loop...")
        
        result = app.exec_()
        print(f"Event-Loop beendet mit Code: {result}")
        sys.exit(result)
        
    except Exception as e:
        print(f"KRITISCHER FEHLER: {e}")
        traceback.print_exc()
        logging.critical(f"Kritischer Fehler beim Programmstart: {e}")
        sys.exit(1)

if __name__ == "__main__":
    print("=== DEBUG MAIN START ===")
    main()
    print("=== DEBUG MAIN END ===")
