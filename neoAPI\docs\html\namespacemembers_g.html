<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented namespace members with links to the namespaces they belong to:</div>

<h3><a id="index_g"></a>- g -</h3><ul>
<li>GainAuto_Continuous
: <a class="el" href="a00091.html#abe52fdd3aaae66d66a50da4407dc35e4">neoapi</a>
</li>
<li>GainAuto_Off
: <a class="el" href="a00091.html#a671b8dc760974c5417d91aa828d55212">neoapi</a>
</li>
<li>GainAuto_Once
: <a class="el" href="a00091.html#a2b60bd2c028c35e3fca42388ca05b08b">neoapi</a>
</li>
<li>GainSelector_All
: <a class="el" href="a00091.html#a7dd01122033a2572eab672f432651e9f">neoapi</a>
</li>
<li>GainSelector_AnalogAll
: <a class="el" href="a00091.html#abd04dfe88ac10246f044fff9e9d1928c">neoapi</a>
</li>
<li>GainSelector_AnalogBlue
: <a class="el" href="a00091.html#a838354e52e620b68a60b59a563deb958">neoapi</a>
</li>
<li>GainSelector_AnalogGreen
: <a class="el" href="a00091.html#a3a8be3069b2040de8eb1bb40a2e836c8">neoapi</a>
</li>
<li>GainSelector_AnalogRed
: <a class="el" href="a00091.html#a5a816aa6ba6e2ac5605d1ee07f2c31ee">neoapi</a>
</li>
<li>GainSelector_AnalogTap1
: <a class="el" href="a00091.html#aa99305b01533c1b41bb3c8b09b055c99">neoapi</a>
</li>
<li>GainSelector_AnalogTap2
: <a class="el" href="a00091.html#acb68b29958de322e6009e8cf8ac81c43">neoapi</a>
</li>
<li>GainSelector_AnalogU
: <a class="el" href="a00091.html#accad10de2df7cd40dcaa138a4dafb6ed">neoapi</a>
</li>
<li>GainSelector_AnalogV
: <a class="el" href="a00091.html#add4fd282e893a28bd5f6fca89d7e2a82">neoapi</a>
</li>
<li>GainSelector_AnalogY
: <a class="el" href="a00091.html#a478555d26219f1cc41da8010bd64edff">neoapi</a>
</li>
<li>GainSelector_Blue
: <a class="el" href="a00091.html#afe93de042a43401935735c7d4a185445">neoapi</a>
</li>
<li>GainSelector_DigitalAll
: <a class="el" href="a00091.html#a85d8cbc2d6b7825c757dc8069690ccbd">neoapi</a>
</li>
<li>GainSelector_DigitalBlue
: <a class="el" href="a00091.html#aa8eeb14c7a25b1173bdba678351f7694">neoapi</a>
</li>
<li>GainSelector_DigitalGreen
: <a class="el" href="a00091.html#aa92b66a1eacea3e4f061af1c8f634999">neoapi</a>
</li>
<li>GainSelector_DigitalRed
: <a class="el" href="a00091.html#a0545898c86e39a4606b6df2f19692f56">neoapi</a>
</li>
<li>GainSelector_DigitalTap1
: <a class="el" href="a00091.html#a34f504516c871655e4d49f777654e1a6">neoapi</a>
</li>
<li>GainSelector_DigitalTap2
: <a class="el" href="a00091.html#abf393c58d52d4223a9f2bc3afcff704b">neoapi</a>
</li>
<li>GainSelector_DigitalU
: <a class="el" href="a00091.html#a35445f5bfd769d409a75921813963a7d">neoapi</a>
</li>
<li>GainSelector_DigitalV
: <a class="el" href="a00091.html#a5f14a741cfbb6913cc3c907180ca3b8e">neoapi</a>
</li>
<li>GainSelector_DigitalY
: <a class="el" href="a00091.html#a9b3f914e245bdfe2a150138cf58eda76">neoapi</a>
</li>
<li>GainSelector_Green
: <a class="el" href="a00091.html#a5d14dc4fad268034922efc321d690796">neoapi</a>
</li>
<li>GainSelector_GreenBlue
: <a class="el" href="a00091.html#a9fcaaa74e397e28b6b6e9ba100f9402b">neoapi</a>
</li>
<li>GainSelector_GreenRed
: <a class="el" href="a00091.html#aa0cb62afe353178eb5ed7a94d5958845">neoapi</a>
</li>
<li>GainSelector_Red
: <a class="el" href="a00091.html#aa0debf13a4f6648815ea642b1a1e961c">neoapi</a>
</li>
<li>GainSelector_Tap1
: <a class="el" href="a00091.html#a42d28dcea4d2a6fb5d83518c4de1e6b5">neoapi</a>
</li>
<li>GainSelector_Tap2
: <a class="el" href="a00091.html#ad03027bc3839bc0f51c873a445bf9368">neoapi</a>
</li>
<li>GainSelector_U
: <a class="el" href="a00091.html#ae980899b0549a727c98d999ceb054c91">neoapi</a>
</li>
<li>GainSelector_V
: <a class="el" href="a00091.html#ae4bd73aa1628c6174206ee4281483a4d">neoapi</a>
</li>
<li>GainSelector_Y
: <a class="el" href="a00091.html#aab05cbd7d8994edf5851915dcc78aaa2">neoapi</a>
</li>
<li>GenDCStreamingMode_Automatic
: <a class="el" href="a00091.html#ab3e386377d464f0f5b8246275075b9f5">neoapi</a>
</li>
<li>GenDCStreamingMode_Off
: <a class="el" href="a00091.html#a46494edefd457590892f984c1f4a276b">neoapi</a>
</li>
<li>GenDCStreamingMode_On
: <a class="el" href="a00091.html#a1d94f9aa961111a676f57d94ac3a986c">neoapi</a>
</li>
<li>GenDCStreamingStatus_Off
: <a class="el" href="a00091.html#ab0c1b5620c0fc663c4caeff2daade1b1">neoapi</a>
</li>
<li>GenDCStreamingStatus_On
: <a class="el" href="a00091.html#adac4e55e5c3a190b72b3a583de6c249c">neoapi</a>
</li>
<li>GevCCP_ControlAccess
: <a class="el" href="a00091.html#a1ab0740ab619daeb7560001a872f507a">neoapi</a>
</li>
<li>GevCCP_ControlAccessSwitchoverActive
: <a class="el" href="a00091.html#a21c702fe4caa6206b00daacaff6b26d6">neoapi</a>
</li>
<li>GevCCP_ExclusiveAccess
: <a class="el" href="a00091.html#adbc982dc01b5d6cf1c931db43e68f14b">neoapi</a>
</li>
<li>GevCCP_OpenAccess
: <a class="el" href="a00091.html#a4a942f9ed292546d6aff23492623393b">neoapi</a>
</li>
<li>GevGVCPExtendedStatusCodesSelector_Version1_1
: <a class="el" href="a00091.html#a24f9c013b5b55e69fe8ddd7a6150bc93">neoapi</a>
</li>
<li>GevGVCPExtendedStatusCodesSelector_Version2_0
: <a class="el" href="a00091.html#acd724305f7dc800cbb6e3f027a1eb22b">neoapi</a>
</li>
<li>GevIPConfigurationStatus_DHCP
: <a class="el" href="a00091.html#a9eb44957177dd86cacdf96b34f45ba47">neoapi</a>
</li>
<li>GevIPConfigurationStatus_ForceIP
: <a class="el" href="a00091.html#abc6f164f743a70c2413e824c20687f88">neoapi</a>
</li>
<li>GevIPConfigurationStatus_LLA
: <a class="el" href="a00091.html#afd6ecd476792f135c1af2984c27887c7">neoapi</a>
</li>
<li>GevIPConfigurationStatus_None
: <a class="el" href="a00091.html#a03282fa1c67aee886358db4cc8beb10e">neoapi</a>
</li>
<li>GevIPConfigurationStatus_PersistentIP
: <a class="el" href="a00091.html#a89c88b77f81c53ecf287c1b465b0fc15">neoapi</a>
</li>
<li>GevSupportedOptionSelector_Action
: <a class="el" href="a00091.html#a359529c1270838ed6fa38ebe86dd9219">neoapi</a>
</li>
<li>GevSupportedOptionSelector_CCPApplicationSocket
: <a class="el" href="a00091.html#a08cbbaced3a5ded581cb473960f83cc2">neoapi</a>
</li>
<li>GevSupportedOptionSelector_CommandsConcatenation
: <a class="el" href="a00091.html#ad90ed80a7dc7ffd07c0acd4cbd0da04f">neoapi</a>
</li>
<li>GevSupportedOptionSelector_DiscoveryAckDelay
: <a class="el" href="a00091.html#a800955a8c88d909aa2a2f7a42476eb0e">neoapi</a>
</li>
<li>GevSupportedOptionSelector_DiscoveryAckDelayWritable
: <a class="el" href="a00091.html#a9bc2433d1b09b640ca93834f7c2b16d1">neoapi</a>
</li>
<li>GevSupportedOptionSelector_DynamicLAG
: <a class="el" href="a00091.html#ad402ae8d02220465e37b4442f0d9c255">neoapi</a>
</li>
<li>GevSupportedOptionSelector_Event
: <a class="el" href="a00091.html#af395583e20cc73455b72127c8e76de03">neoapi</a>
</li>
<li>GevSupportedOptionSelector_EventData
: <a class="el" href="a00091.html#a75d18b0c87743c9167312fc74d2d51f0">neoapi</a>
</li>
<li>GevSupportedOptionSelector_ExtendedStatusCodes
: <a class="el" href="a00091.html#a896253417e7e51ca493f95bd95b8b905">neoapi</a>
</li>
<li>GevSupportedOptionSelector_ExtendedStatusCodesVersion2_0
: <a class="el" href="a00091.html#a1a112c9eb1ec6fd46d8ef5d3252574c9">neoapi</a>
</li>
<li>GevSupportedOptionSelector_HeartbeatDisable
: <a class="el" href="a00091.html#aa31473e60c1f53ca299071e68cd89027">neoapi</a>
</li>
<li>GevSupportedOptionSelector_IEEE1588
: <a class="el" href="a00091.html#aaa9abd4f996d010b9987d04fd2d0cef6">neoapi</a>
</li>
<li>GevSupportedOptionSelector_IPConfigurationDHCP
: <a class="el" href="a00091.html#a83a031524a28d9441429c4cbac83b4d2">neoapi</a>
</li>
<li>GevSupportedOptionSelector_IPConfigurationLLA
: <a class="el" href="a00091.html#a9e4b07c25bdc55d1ea74c88b4d292f83">neoapi</a>
</li>
<li>GevSupportedOptionSelector_IPConfigurationPersistentIP
: <a class="el" href="a00091.html#a0ec05ff4cce5349febe484989c9e7273">neoapi</a>
</li>
<li>GevSupportedOptionSelector_LinkSpeed
: <a class="el" href="a00091.html#af80a16efea271f32d0254dfa6c42d8eb">neoapi</a>
</li>
<li>GevSupportedOptionSelector_ManifestTable
: <a class="el" href="a00091.html#a8e4de427caab4e8eb615db6e271a19ed">neoapi</a>
</li>
<li>GevSupportedOptionSelector_MessageChannelSourceSocket
: <a class="el" href="a00091.html#a5b3f54943f9d2055220ef2fbac5ddbb2">neoapi</a>
</li>
<li>GevSupportedOptionSelector_MultiLink
: <a class="el" href="a00091.html#a6d3d81559236ea93b3dc212588052bd1">neoapi</a>
</li>
<li>GevSupportedOptionSelector_PacketResend
: <a class="el" href="a00091.html#ae4d5fb50298f8de8bf253239cfb58af9">neoapi</a>
</li>
<li>GevSupportedOptionSelector_PAUSEFrameGeneration
: <a class="el" href="a00091.html#a212cc099b3eaa8ffbfeffc5d7aed3c65">neoapi</a>
</li>
<li>GevSupportedOptionSelector_PAUSEFrameReception
: <a class="el" href="a00091.html#acd7836c2195e971111e509eada3d0ede">neoapi</a>
</li>
<li>GevSupportedOptionSelector_PendingAck
: <a class="el" href="a00091.html#a0548e2ff95982b32e612eea57944aa51">neoapi</a>
</li>
<li>GevSupportedOptionSelector_PrimaryApplicationSwitchover
: <a class="el" href="a00091.html#a13f4099d3569f48f83a91a0b771cdc16">neoapi</a>
</li>
<li>GevSupportedOptionSelector_Ptp
: <a class="el" href="a00091.html#a8f9a54e17873163a68e6f47b7aef1995">neoapi</a>
</li>
<li>GevSupportedOptionSelector_ScheduledAction
: <a class="el" href="a00091.html#a6fd957ae3db76a779764deed3d45d5a9">neoapi</a>
</li>
<li>GevSupportedOptionSelector_SerialNumber
: <a class="el" href="a00091.html#a8e17e2c6081fd9f3dea2d90f8e51d2e6">neoapi</a>
</li>
<li>GevSupportedOptionSelector_SingleLink
: <a class="el" href="a00091.html#ad916451ce7eb289a640c668d98f82552">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StandardIDMode
: <a class="el" href="a00091.html#a5a5678a47425fee3ad930d5aa1bf2b82">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StaticLAG
: <a class="el" href="a00091.html#a53f271421eec88c344b8707067cff802">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel0AllInTransmission
: <a class="el" href="a00091.html#a8586f1a1b28fdd8eaf1d3d20d005715e">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel0BigAndLittleEndian
: <a class="el" href="a00091.html#a5f5a328a8f379f269dff08716f98f2a3">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel0ExtendedChunkData
: <a class="el" href="a00091.html#a0bb0f57eea067dc72dee641bbd8c1fde">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel0IPReassembly
: <a class="el" href="a00091.html#a81a626fb429a147011e8ec050cfe0e36">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel0MultiZone
: <a class="el" href="a00091.html#a797171a977ce4c5530d4af017c0e31cf">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel0PacketResendDestination
: <a class="el" href="a00091.html#ae3301107d2f925d7098c2d48c296b277">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel0UnconditionalStreaming
: <a class="el" href="a00091.html#acb72517befc71bf03ae6c94b75e396f8">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel1AllInTransmission
: <a class="el" href="a00091.html#a05ab12b23c971aa018a1b87f91d10c8d">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel1BigAndLittleEndian
: <a class="el" href="a00091.html#a2dcad403a7797faaec4e1e94091a6ea2">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel1ExtendedChunkData
: <a class="el" href="a00091.html#aefacddb162165d8c05b1d3bf7f842bfb">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel1IPReassembly
: <a class="el" href="a00091.html#a6d96c96815073c90d86d3f83dbe44759">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel1MultiZone
: <a class="el" href="a00091.html#a3421b96be0c2c3d5bcba417de1a2c3f7">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel1PacketResendDestination
: <a class="el" href="a00091.html#a43117c9e5ee7bb001dad2a55886805ab">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel1UnconditionalStreaming
: <a class="el" href="a00091.html#a87fd425d68dd75b2e6d017a988501041">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel2AllInTransmission
: <a class="el" href="a00091.html#aded0505bbfa03e185f9fef46a1e71f79">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel2BigAndLittleEndian
: <a class="el" href="a00091.html#a6a8126dc27adad617792de8073d4412b">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel2ExtendedChunkData
: <a class="el" href="a00091.html#a234b8adca04fc780c558667a2c93881d">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel2IPReassembly
: <a class="el" href="a00091.html#aca6bb62544b637c93ade943d5c932300">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel2MultiZone
: <a class="el" href="a00091.html#abd60c72652c2e6becc0885075784de60">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel2PacketResendDestination
: <a class="el" href="a00091.html#a5c872dccee8ff1cc99b792976dfa53db">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannel2UnconditionalStreaming
: <a class="el" href="a00091.html#a8133ecb28a56147c3efc3c3aca2dbabf">neoapi</a>
</li>
<li>GevSupportedOptionSelector_StreamChannelSourceSocket
: <a class="el" href="a00091.html#ad0a38c17ac2ce22bd45fc3001b0bb364">neoapi</a>
</li>
<li>GevSupportedOptionSelector_TestData
: <a class="el" href="a00091.html#a0b0fa345fe003067e51f18733f5b0ff0">neoapi</a>
</li>
<li>GevSupportedOptionSelector_UnconditionalAction
: <a class="el" href="a00091.html#ab0f425e0caf0db47dcffb655a9172cf6">neoapi</a>
</li>
<li>GevSupportedOptionSelector_UserDefinedName
: <a class="el" href="a00091.html#a3e6ee530eeb7a5944a6c537b59db60d4">neoapi</a>
</li>
<li>GevSupportedOptionSelector_WriteMem
: <a class="el" href="a00091.html#a10977afa0065fcc444d14693fd3e0235">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
