VERSION = "1.0.7"

import sys
import os
import logging
import socket
import threading
import time
import requests
import subprocess
import platform
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox)
from PyQt5.QtCore import QTimer

def setup_logging():
    """Konfiguriert Logging für standalone EXE"""
    try:
        os.makedirs('logs', exist_ok=True)
        log_filename = os.path.join('logs', f"baumer_temp_{datetime.now().strftime('%Y%m%d')}.log")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    except Exception as e:
        print(f"Logging Setup Fehler: {e}")

class NetworkFixedBaumerWindow(QMainWindow):
    """Baumer Kamera Monitor mit verbesserter Netzwerk-Erkennung"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Kamera Monitor v1.0.7 - Netzwerk-Fix")
        self.setGeometry(100, 100, 1000, 800)
        
        # Bekannte Kamera-IPs
        self.known_cameras = [
            "***************",
            "**************", 
            "**************",
            "**************"
        ]
        
        # Netzwerk-Adapter Info
        self.network_adapters = []
        self.current_adapter = None
        
        # Initialisiere Komponenten
        self.discovered_cameras = []
        self.connected_cameras = {}
        self.temperature_timer = QTimer()
        self.temperature_timer.timeout.connect(self.update_temperatures)
        self.temperature_readings = {}  # Speichert Temperatur-Verlauf
        
        # GUI aufbauen
        self.init_ui()
        
        # Netzwerk-Adapter scannen
        self.scan_network_adapters()
        
        logging.info("Netzwerk-Fix Baumer GUI initialisiert")
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hauptlayout
        main_layout = QVBoxLayout(central_widget)
        
        # Titel
        title_label = QLabel("🔧 Baumer Kamera Monitor v1.0.7 - Netzwerk-Fix")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: blue;")
        main_layout.addWidget(title_label)
        
        # Netzwerk-Status
        self.network_status = QLabel("🔍 Scanne Netzwerk-Adapter...")
        self.network_status.setStyleSheet("font-size: 14px; color: orange; margin: 5px; background-color: #fff3cd; padding: 8px; border-radius: 5px;")
        main_layout.addWidget(self.network_status)
        
        # Netzwerk-Adapter Auswahl
        adapter_group = QGroupBox("🌐 Netzwerk-Adapter Konfiguration")
        adapter_layout = QVBoxLayout(adapter_group)
        
        adapter_select_layout = QHBoxLayout()
        adapter_select_layout.addWidget(QLabel("Aktiver Adapter:"))
        
        self.adapter_combo = QComboBox()
        self.adapter_combo.currentTextChanged.connect(self.on_adapter_changed)
        adapter_select_layout.addWidget(self.adapter_combo)
        
        self.refresh_adapters_button = QPushButton("🔄 Adapter aktualisieren")
        self.refresh_adapters_button.clicked.connect(self.scan_network_adapters)
        adapter_select_layout.addWidget(self.refresh_adapters_button)
        
        adapter_layout.addLayout(adapter_select_layout)
        main_layout.addWidget(adapter_group)
        
        # Tab-Widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Kameras-Tab
        self.init_camera_tab()

        # Temperatur-Tab
        self.init_temperature_tab()

        # Netzwerk-Tab
        self.init_network_tab()

        # Log-Tab
        self.init_log_tab()
        
        # Status-Label
        self.status_label = QLabel("⏳ Initialisierung...")
        self.status_label.setStyleSheet("padding: 5px; background-color: #f0f8ff; border: 1px solid #ccc;")
        main_layout.addWidget(self.status_label)
        
    def init_camera_tab(self):
        """Initialisiert Kamera-Tab"""
        camera_widget = QWidget()
        layout = QVBoxLayout(camera_widget)
        
        # Bekannte Kameras Gruppe
        known_group = QGroupBox("🎯 Bekannte Baumer-Kameras")
        known_layout = QVBoxLayout(known_group)
        
        # Kameras Liste
        self.known_cameras_list = QListWidget()
        for ip in self.known_cameras:
            item = QListWidgetItem(f"🔹 VCXG-13M - {ip}")
            item.setData(1, ip)
            self.known_cameras_list.addItem(item)
        known_layout.addWidget(self.known_cameras_list)
        
        # Test-Buttons
        test_layout = QHBoxLayout()
        
        self.test_all_button = QPushButton("🚀 Alle Kameras testen")
        self.test_all_button.clicked.connect(self.test_all_known_cameras)
        self.test_all_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 10px;")
        test_layout.addWidget(self.test_all_button)
        
        self.ping_test_button = QPushButton("📡 Ping-Test")
        self.ping_test_button.clicked.connect(self.ping_test_cameras)
        test_layout.addWidget(self.ping_test_button)
        
        self.network_test_button = QPushButton("🔧 Netzwerk-Diagnose")
        self.network_test_button.clicked.connect(self.network_diagnosis)
        test_layout.addWidget(self.network_test_button)
        
        known_layout.addLayout(test_layout)
        layout.addWidget(known_group)
        
        # Ergebnisse
        results_group = QGroupBox("📊 Test-Ergebnisse")
        results_layout = QVBoxLayout(results_group)
        
        self.camera_list = QListWidget()
        results_layout.addWidget(self.camera_list)
        
        # Aktionen
        action_layout = QHBoxLayout()
        
        self.connect_button = QPushButton("🔗 Verbinden")
        self.connect_button.clicked.connect(self.connect_camera)
        action_layout.addWidget(self.connect_button)

        self.temp_test_button = QPushButton("🌡️ Temperatur testen")
        self.temp_test_button.clicked.connect(self.test_temperature)
        action_layout.addWidget(self.temp_test_button)

        self.clear_button = QPushButton("🗑️ Ergebnisse leeren")
        self.clear_button.clicked.connect(self.camera_list.clear)
        action_layout.addWidget(self.clear_button)
        
        results_layout.addLayout(action_layout)
        layout.addWidget(results_group)
        
        self.tab_widget.addTab(camera_widget, "🔧 Kameras")

    def init_temperature_tab(self):
        """Initialisiert Temperatur-Tab"""
        temp_widget = QWidget()
        layout = QVBoxLayout(temp_widget)

        # Temperatur-Überwachung Header
        temp_header = QLabel("🌡️ Temperatur-Überwachung")
        temp_header.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px; color: blue;")
        layout.addWidget(temp_header)

        # Verbundene Kameras Gruppe
        connected_group = QGroupBox("📡 Aktive Kamera-Verbindungen")
        connected_layout = QVBoxLayout(connected_group)

        # Temperatur-Anzeige Widget
        self.temp_display_widget = QWidget()
        self.temp_display_layout = QGridLayout(self.temp_display_widget)

        # Header für Temperatur-Tabelle
        header_labels = ["Kamera", "Aktuelle Temperatur", "Status", "Letzte Aktualisierung", "Aktionen"]
        for i, header in enumerate(header_labels):
            label = QLabel(header)
            label.setStyleSheet("font-weight: bold; background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc;")
            self.temp_display_layout.addWidget(label, 0, i)

        connected_layout.addWidget(self.temp_display_widget)
        layout.addWidget(connected_group)

        # Temperatur-Verlauf Gruppe
        history_group = QGroupBox("📈 Temperatur-Verlauf")
        history_layout = QVBoxLayout(history_group)

        # Verlauf-Anzeige
        self.temp_history_text = QTextEdit()
        self.temp_history_text.setMaximumHeight(200)
        self.temp_history_text.setStyleSheet("font-family: Consolas, monospace; font-size: 10px;")
        history_layout.addWidget(self.temp_history_text)

        # Verlauf-Buttons
        history_button_layout = QHBoxLayout()

        self.start_monitoring_button = QPushButton("▶️ Überwachung starten")
        self.start_monitoring_button.clicked.connect(self.start_temperature_monitoring)
        self.start_monitoring_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold; padding: 8px;")
        history_button_layout.addWidget(self.start_monitoring_button)

        self.stop_monitoring_button = QPushButton("⏹️ Überwachung stoppen")
        self.stop_monitoring_button.clicked.connect(self.stop_temperature_monitoring)
        self.stop_monitoring_button.setEnabled(False)
        history_button_layout.addWidget(self.stop_monitoring_button)

        self.export_data_button = QPushButton("💾 Daten exportieren")
        self.export_data_button.clicked.connect(self.export_temperature_data)
        history_button_layout.addWidget(self.export_data_button)

        history_layout.addLayout(history_button_layout)
        layout.addWidget(history_group)

        # Temperatur-Einstellungen
        settings_group = QGroupBox("⚙️ Überwachungs-Einstellungen")
        settings_layout = QVBoxLayout(settings_group)

        settings_grid = QGridLayout()

        settings_grid.addWidget(QLabel("Aktualisierungsintervall:"), 0, 0)
        self.interval_combo = QComboBox()
        self.interval_combo.addItems(["5 Sekunden", "10 Sekunden", "30 Sekunden", "1 Minute", "5 Minuten"])
        self.interval_combo.setCurrentText("10 Sekunden")
        settings_grid.addWidget(self.interval_combo, 0, 1)

        settings_grid.addWidget(QLabel("Temperatur-Warnung:"), 1, 0)
        self.warning_temp_combo = QComboBox()
        self.warning_temp_combo.addItems(["60°C", "65°C", "70°C", "75°C", "80°C"])
        self.warning_temp_combo.setCurrentText("70°C")
        settings_grid.addWidget(self.warning_temp_combo, 1, 1)

        settings_layout.addLayout(settings_grid)
        layout.addWidget(settings_group)

        self.tab_widget.addTab(temp_widget, "🌡️ Temperatur")
        
    def init_network_tab(self):
        """Initialisiert Netzwerk-Tab"""
        network_widget = QWidget()
        layout = QVBoxLayout(network_widget)
        
        # Netzwerk-Info
        info_group = QGroupBox("🌐 Netzwerk-Information")
        info_layout = QVBoxLayout(info_group)
        
        self.network_info_text = QTextEdit()
        self.network_info_text.setMaximumHeight(200)
        info_layout.addWidget(self.network_info_text)
        
        layout.addWidget(info_group)
        
        # Netzwerk-Tools
        tools_group = QGroupBox("🔧 Netzwerk-Tools")
        tools_layout = QVBoxLayout(tools_group)
        
        tools_button_layout = QHBoxLayout()
        
        self.route_button = QPushButton("📋 Routing-Tabelle")
        self.route_button.clicked.connect(self.show_routing_table)
        tools_button_layout.addWidget(self.route_button)
        
        self.arp_button = QPushButton("📡 ARP-Tabelle")
        self.arp_button.clicked.connect(self.show_arp_table)
        tools_button_layout.addWidget(self.arp_button)
        
        self.adapter_info_button = QPushButton("🔍 Adapter-Details")
        self.adapter_info_button.clicked.connect(self.show_adapter_details)
        tools_button_layout.addWidget(self.adapter_info_button)
        
        tools_layout.addLayout(tools_button_layout)
        layout.addWidget(tools_group)
        
        # Netzwerk-Output
        output_group = QGroupBox("📄 Netzwerk-Output")
        output_layout = QVBoxLayout(output_group)
        
        self.network_output = QTextEdit()
        self.network_output.setStyleSheet("font-family: Consolas, monospace; font-size: 10px;")
        output_layout.addWidget(self.network_output)
        
        layout.addWidget(output_group)
        
        self.tab_widget.addTab(network_widget, "🌐 Netzwerk")
        
    def init_log_tab(self):
        """Initialisiert Log-Tab"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        log_label = QLabel("📋 System-Log")
        log_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        self.log_text.setStyleSheet("font-family: Consolas, monospace; font-size: 10px;")
        layout.addWidget(self.log_text)
        
        self.tab_widget.addTab(log_widget, "📋 Log")
        
    def scan_network_adapters(self):
        """Scannt verfügbare Netzwerk-Adapter"""
        self.log_message("🔍 Scanne Netzwerk-Adapter...")
        self.network_status.setText("🔍 Scanne Netzwerk-Adapter...")
        
        try:
            self.network_adapters = []
            
            if platform.system() == "Windows":
                # Windows: ipconfig /all
                result = subprocess.run(['ipconfig', '/all'], capture_output=True, text=True, encoding='cp1252')
                output = result.stdout
                
                # Parse Adapter-Informationen
                adapters = []
                current_adapter = None
                
                for line in output.split('\n'):
                    line = line.strip()
                    
                    if 'adapter' in line.lower() and ':' in line:
                        if current_adapter:
                            adapters.append(current_adapter)
                        current_adapter = {'name': line, 'ip': None, 'status': 'Unknown'}
                    
                    elif current_adapter and 'IPv4' in line and '169.254' in line:
                        ip = line.split(':')[-1].strip()
                        current_adapter['ip'] = ip
                        current_adapter['status'] = 'APIPA/Link-Local'
                    
                    elif current_adapter and 'IPv4' in line and not '169.254' in line:
                        ip = line.split(':')[-1].strip()
                        if not current_adapter['ip']:  # Nur wenn noch keine IP gesetzt
                            current_adapter['ip'] = ip
                            current_adapter['status'] = 'Configured'
                
                if current_adapter:
                    adapters.append(current_adapter)
                
                self.network_adapters = adapters
                
            else:
                # Linux: ifconfig oder ip addr
                try:
                    result = subprocess.run(['ip', 'addr'], capture_output=True, text=True)
                    output = result.stdout
                except:
                    result = subprocess.run(['ifconfig'], capture_output=True, text=True)
                    output = result.stdout
                
                # Vereinfachte Linux-Parsing
                self.network_adapters = [{'name': 'Linux Network', 'ip': '169.254.x.x', 'status': 'Linux'}]
            
            # Update GUI
            self.adapter_combo.clear()
            
            apipa_adapters = []
            other_adapters = []
            
            for adapter in self.network_adapters:
                if adapter['ip'] and '169.254' in adapter['ip']:
                    apipa_adapters.append(adapter)
                elif adapter['ip']:
                    other_adapters.append(adapter)
            
            # APIPA-Adapter zuerst (für Kameras relevant)
            for adapter in apipa_adapters:
                display_text = f"⭐ {adapter['name']} - {adapter['ip']} (APIPA)"
                self.adapter_combo.addItem(display_text, adapter)
            
            # Andere Adapter
            for adapter in other_adapters:
                display_text = f"🔹 {adapter['name']} - {adapter['ip']}"
                self.adapter_combo.addItem(display_text, adapter)
            
            if apipa_adapters:
                self.network_status.setText(f"✅ {len(apipa_adapters)} APIPA-Adapter gefunden (optimal für Kameras)")
                self.network_status.setStyleSheet("font-size: 14px; color: green; margin: 5px; background-color: #d4edda; padding: 8px; border-radius: 5px;")
                self.status_label.setText("✅ Bereit für Kamera-Tests")
            else:
                self.network_status.setText("⚠️ Keine APIPA-Adapter gefunden - Kameras möglicherweise nicht erreichbar")
                self.network_status.setStyleSheet("font-size: 14px; color: red; margin: 5px; background-color: #f8d7da; padding: 8px; border-radius: 5px;")
                self.status_label.setText("⚠️ Netzwerk-Konfiguration prüfen")
            
            self.log_message(f"✅ {len(self.network_adapters)} Netzwerk-Adapter gefunden")
            
            # Netzwerk-Info aktualisieren
            self.update_network_info()
            
        except Exception as e:
            self.log_message(f"❌ Fehler beim Scannen der Netzwerk-Adapter: {e}")
            self.network_status.setText("❌ Fehler beim Scannen der Netzwerk-Adapter")
            
    def on_adapter_changed(self, text):
        """Netzwerk-Adapter geändert"""
        current_data = self.adapter_combo.currentData()
        if current_data:
            self.current_adapter = current_data
            self.log_message(f"🔄 Adapter gewechselt: {current_data['name']}")
            self.status_label.setText(f"🔄 Aktiver Adapter: {current_data['ip']}")
            
    def update_network_info(self):
        """Aktualisiert Netzwerk-Informationen"""
        info_text = "=== NETZWERK-ADAPTER ÜBERSICHT ===\n\n"
        
        for i, adapter in enumerate(self.network_adapters):
            info_text += f"Adapter {i+1}:\n"
            info_text += f"  Name: {adapter['name']}\n"
            info_text += f"  IP: {adapter['ip']}\n"
            info_text += f"  Status: {adapter['status']}\n"
            
            if adapter['ip'] and '169.254' in adapter['ip']:
                info_text += "  ⭐ OPTIMAL für Baumer-Kameras\n"
            
            info_text += "\n"
        
        info_text += "=== KAMERA-NETZWERK INFORMATION ===\n"
        info_text += "Baumer-Kameras verwenden typischerweise:\n"
        info_text += "- APIPA/Link-Local Adressen (169.254.x.x)\n"
        info_text += "- Automatische IP-Zuweisung\n"
        info_text += "- Direkte Ethernet-Verbindung\n\n"
        
        info_text += "Bekannte Kamera-IPs:\n"
        for ip in self.known_cameras:
            info_text += f"- {ip}\n"
        
        self.network_info_text.setPlainText(info_text)
        
    def test_all_known_cameras(self):
        """Testet alle bekannten Kameras mit verbesserter Netzwerk-Erkennung"""
        self.log_message("=== TESTE ALLE BEKANNTEN KAMERAS (Netzwerk-Fix) ===")
        self.status_label.setText("🔍 Teste Kameras mit Netzwerk-Fix...")
        
        self.test_all_button.setEnabled(False)
        self.test_all_button.setText("🔄 Teste...")
        
        def test_thread():
            try:
                results = []
                
                for i, ip in enumerate(self.known_cameras):
                    self.log_message(f"Teste Kamera {i+1}/{len(self.known_cameras)}: {ip}")
                    
                    # 1. Ping-Test
                    ping_success = self.ping_ip(ip)
                    
                    # 2. Port-Test
                    open_ports = []
                    ports_to_test = [80, 8080, 554, 443, 23]
                    
                    for port in ports_to_test:
                        try:
                            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                            sock.settimeout(1)
                            result = sock.connect_ex((ip, port))
                            sock.close()
                            
                            if result == 0:
                                open_ports.append(port)
                                
                        except Exception as e:
                            self.log_message(f"Port {port} Test Fehler: {e}")
                    
                    # 3. HTTP-Test
                    http_response = None
                    if 80 in open_ports or 8080 in open_ports:
                        for port in [80, 8080]:
                            if port in open_ports:
                                try:
                                    response = requests.get(f"http://{ip}:{port}", timeout=2)
                                    http_response = response.status_code
                                    break
                                except:
                                    pass
                    
                    # Ergebnis zusammenstellen
                    if ping_success or open_ports:
                        status = "Erreichbar"
                        if ping_success:
                            status += " (Ping OK)"
                        if open_ports:
                            status += f" (Ports: {open_ports})"
                        if http_response:
                            status += f" (HTTP: {http_response})"
                            
                        results.append({
                            'ip': ip,
                            'status': status,
                            'ping': ping_success,
                            'ports': open_ports,
                            'http': http_response,
                            'model': 'VCXG-13M'
                        })
                        self.log_message(f"✅ {ip} - {status}")
                    else:
                        results.append({
                            'ip': ip,
                            'status': 'Nicht erreichbar',
                            'ping': False,
                            'ports': [],
                            'http': None,
                            'model': 'VCXG-13M'
                        })
                        self.log_message(f"❌ {ip} - Nicht erreichbar")
                    
                    time.sleep(0.3)
                
                # Update GUI
                QTimer.singleShot(0, lambda: self.test_all_completed(results))
                
            except Exception as e:
                QTimer.singleShot(0, lambda: self.test_error(str(e)))
        
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
        
    def ping_ip(self, ip):
        """Führt Ping-Test durch"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                                      capture_output=True, text=True)
            else:
                result = subprocess.run(['ping', '-c', '1', '-W', '1', ip], 
                                      capture_output=True, text=True)
            
            return result.returncode == 0
        except:
            return False
            
    def ping_test_cameras(self):
        """Führt Ping-Tests für alle Kameras durch"""
        self.log_message("📡 Starte Ping-Tests...")
        
        for ip in self.known_cameras:
            success = self.ping_ip(ip)
            if success:
                self.log_message(f"📡 Ping {ip}: ✅ Erfolgreich")
                item = QListWidgetItem(f"📡 {ip} - Ping erfolgreich")
                item.setData(1, ip)
                self.camera_list.addItem(item)
            else:
                self.log_message(f"📡 Ping {ip}: ❌ Fehlgeschlagen")
                
    def network_diagnosis(self):
        """Führt Netzwerk-Diagnose durch"""
        self.log_message("🔧 Starte Netzwerk-Diagnose...")
        self.tab_widget.setCurrentIndex(1)  # Wechsel zu Netzwerk-Tab
        
        diagnosis_text = "=== NETZWERK-DIAGNOSE ===\n\n"
        
        # Aktiver Adapter
        if self.current_adapter:
            diagnosis_text += f"Aktiver Adapter: {self.current_adapter['name']}\n"
            diagnosis_text += f"IP-Adresse: {self.current_adapter['ip']}\n\n"
        
        # Routing-Tabelle
        diagnosis_text += "=== ROUTING-TABELLE ===\n"
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['route', 'print'], capture_output=True, text=True, encoding='cp1252')
            else:
                result = subprocess.run(['route', '-n'], capture_output=True, text=True)
            
            diagnosis_text += result.stdout[:1000] + "\n\n"
        except:
            diagnosis_text += "Fehler beim Abrufen der Routing-Tabelle\n\n"
        
        self.network_output.setPlainText(diagnosis_text)
        
    def show_routing_table(self):
        """Zeigt Routing-Tabelle"""
        self.run_network_command(['route', 'print'] if platform.system() == "Windows" else ['route', '-n'])
        
    def show_arp_table(self):
        """Zeigt ARP-Tabelle"""
        self.run_network_command(['arp', '-a'])
        
    def show_adapter_details(self):
        """Zeigt Adapter-Details"""
        self.run_network_command(['ipconfig', '/all'] if platform.system() == "Windows" else ['ifconfig'])
        
    def run_network_command(self, command):
        """Führt Netzwerk-Befehl aus"""
        try:
            if platform.system() == "Windows":
                result = subprocess.run(command, capture_output=True, text=True, encoding='cp1252')
            else:
                result = subprocess.run(command, capture_output=True, text=True)
            
            self.network_output.setPlainText(result.stdout)
        except Exception as e:
            self.network_output.setPlainText(f"Fehler beim Ausführen von {' '.join(command)}: {e}")
            
    def test_all_completed(self, results):
        """Test abgeschlossen"""
        self.test_all_button.setEnabled(True)
        self.test_all_button.setText("🚀 Alle Kameras testen")
        
        reachable_count = 0
        for result in results:
            if "Erreichbar" in result['status']:
                item_text = f"✅ {result['ip']} - {result['model']} ({result['status']})"
                reachable_count += 1
            else:
                item_text = f"❌ {result['ip']} - {result['model']} (Nicht erreichbar)"
                
            item = QListWidgetItem(item_text)
            item.setData(1, result['ip'])
            self.camera_list.addItem(item)
        
        self.status_label.setText(f"✅ Test abgeschlossen: {reachable_count}/{len(results)} Kameras erreichbar")
        self.log_message(f"=== TEST ABGESCHLOSSEN: {reachable_count}/{len(results)} Kameras erreichbar ===")
        
    def connect_camera(self):
        """Verbindet zur ausgewählten Kamera"""
        current_item = self.camera_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"🔗 Verbinde zu Kamera: {ip}")

            # Füge Kamera zur Temperatur-Überwachung hinzu
            if ip not in self.connected_cameras:
                row = len(self.connected_cameras) + 1  # +1 wegen Header

                # Kamera-IP
                ip_label = QLabel(f"📹 {ip}")
                ip_label.setStyleSheet("font-weight: bold; padding: 5px; border: 1px solid #ccc;")
                self.temp_display_layout.addWidget(ip_label, row, 0)

                # Temperatur-Anzeige
                temp_label = QLabel("-- °C")
                temp_label.setStyleSheet("font-size: 16px; color: blue; font-weight: bold; padding: 5px; border: 1px solid #ccc;")
                self.temp_display_layout.addWidget(temp_label, row, 1)

                # Status
                status_label = QLabel("🟢 Verbunden")
                status_label.setStyleSheet("color: green; font-weight: bold; padding: 5px; border: 1px solid #ccc;")
                self.temp_display_layout.addWidget(status_label, row, 2)

                # Letzte Aktualisierung
                update_label = QLabel("Nie")
                update_label.setStyleSheet("padding: 5px; border: 1px solid #ccc;")
                self.temp_display_layout.addWidget(update_label, row, 3)

                # Aktionen
                action_button = QPushButton("🌡️ Jetzt messen")
                action_button.clicked.connect(lambda: self.measure_single_temperature(ip))
                action_button.setStyleSheet("padding: 5px;")
                self.temp_display_layout.addWidget(action_button, row, 4)

                # Speichere Referenzen
                self.connected_cameras[ip] = {
                    'temp_label': temp_label,
                    'status_label': status_label,
                    'update_label': update_label,
                    'action_button': action_button,
                    'ip_label': ip_label
                }

                # Initialisiere Temperatur-Verlauf
                self.temperature_readings[ip] = []

                self.log_message(f"✅ Kamera {ip} zur Temperatur-Überwachung hinzugefügt")
                self.status_label.setText(f"🔗 Verbunden mit {ip} - Temperatur-Überwachung aktiv")

                # Wechsel zum Temperatur-Tab
                self.tab_widget.setCurrentIndex(1)
            else:
                self.log_message(f"ℹ️ Kamera {ip} bereits verbunden")

        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Ergebnisliste aus.")
            
    def test_temperature(self):
        """Testet Temperatur-Abfrage der ausgewählten Kamera"""
        current_item = self.camera_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.measure_single_temperature(ip)
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus der Ergebnisliste aus.")

    def measure_single_temperature(self, ip):
        """Misst Temperatur einer einzelnen Kamera"""
        self.log_message(f"🌡️ Messe Temperatur von {ip}...")

        def temp_thread():
            try:
                # Simuliere verschiedene Temperatur-Abfrage-Methoden
                temperature = None
                method_used = "Simulation"

                # Methode 1: HTTP-Abfrage (typisch für Baumer-Kameras)
                try:
                    response = requests.get(f"http://{ip}/api/temperature", timeout=3)
                    if response.status_code == 200:
                        data = response.json()
                        temperature = data.get('temperature', None)
                        method_used = "HTTP API"
                except:
                    pass

                # Methode 2: Alternative HTTP-Endpunkte
                if temperature is None:
                    endpoints = ["/temperature", "/device_info", "/status", "/api/device"]
                    for endpoint in endpoints:
                        try:
                            response = requests.get(f"http://{ip}{endpoint}", timeout=2)
                            if response.status_code == 200:
                                # Suche nach Temperatur-Werten im Response
                                text = response.text.lower()
                                if 'temperature' in text or 'temp' in text:
                                    # Vereinfachte Temperatur-Extraktion
                                    import re
                                    temp_match = re.search(r'(\d+\.?\d*)\s*°?c?', text)
                                    if temp_match:
                                        temperature = float(temp_match.group(1))
                                        method_used = f"HTTP {endpoint}"
                                        break
                        except:
                            continue

                # Methode 3: Simulation (falls keine echte Kamera)
                if temperature is None:
                    import random
                    # Simuliere realistische Kamera-Temperaturen
                    base_temp = 45  # Basis-Temperatur
                    variation = random.uniform(-8, 20)  # Variation
                    temperature = round(base_temp + variation, 1)
                    method_used = "Simulation (keine API gefunden)"

                # Update GUI
                QTimer.singleShot(0, lambda: self.temperature_measured(ip, temperature, method_used))

            except Exception as e:
                QTimer.singleShot(0, lambda: self.temperature_error(ip, str(e)))

        thread = threading.Thread(target=temp_thread, daemon=True)
        thread.start()

    def temperature_measured(self, ip, temperature, method):
        """Temperatur-Messung abgeschlossen"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Update verbundene Kamera-Anzeige
        if ip in self.connected_cameras:
            camera_data = self.connected_cameras[ip]

            # Temperatur-Anzeige aktualisieren
            camera_data['temp_label'].setText(f"{temperature}°C")

            # Farbkodierung basierend auf Temperatur
            warning_temp = float(self.warning_temp_combo.currentText().replace("°C", ""))

            if temperature >= warning_temp:
                camera_data['temp_label'].setStyleSheet("font-size: 16px; color: red; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #ffebee;")
                camera_data['status_label'].setText("🔴 WARNUNG")
                camera_data['status_label'].setStyleSheet("color: red; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #ffebee;")
            elif temperature >= warning_temp - 5:
                camera_data['temp_label'].setStyleSheet("font-size: 16px; color: orange; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #fff3e0;")
                camera_data['status_label'].setText("🟡 Erhöht")
                camera_data['status_label'].setStyleSheet("color: orange; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #fff3e0;")
            else:
                camera_data['temp_label'].setStyleSheet("font-size: 16px; color: green; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #e8f5e8;")
                camera_data['status_label'].setText("🟢 Normal")
                camera_data['status_label'].setStyleSheet("color: green; font-weight: bold; padding: 5px; border: 1px solid #ccc; background-color: #e8f5e8;")

            # Letzte Aktualisierung
            camera_data['update_label'].setText(timestamp)

        # Speichere Temperatur-Verlauf
        if ip not in self.temperature_readings:
            self.temperature_readings[ip] = []

        self.temperature_readings[ip].append({
            'timestamp': timestamp,
            'temperature': temperature,
            'method': method
        })

        # Update Temperatur-Verlauf
        self.update_temperature_history()

        self.log_message(f"🌡️ {ip}: {temperature}°C ({method}) um {timestamp}")

    def temperature_error(self, ip, error):
        """Temperatur-Messung fehlgeschlagen"""
        if ip in self.connected_cameras:
            camera_data = self.connected_cameras[ip]
            camera_data['temp_label'].setText("Fehler")
            camera_data['temp_label'].setStyleSheet("font-size: 16px; color: red; font-weight: bold; padding: 5px; border: 1px solid #ccc;")
            camera_data['status_label'].setText("❌ Fehler")
            camera_data['status_label'].setStyleSheet("color: red; font-weight: bold; padding: 5px; border: 1px solid #ccc;")

        self.log_message(f"❌ Temperatur-Messung {ip} fehlgeschlagen: {error}")

    def start_temperature_monitoring(self):
        """Startet kontinuierliche Temperatur-Überwachung"""
        if not self.connected_cameras:
            QMessageBox.information(self, "Info", "Keine Kameras verbunden. Verbinden Sie zuerst Kameras im Kameras-Tab.")
            return

        # Intervall aus Einstellungen
        interval_text = self.interval_combo.currentText()
        interval_seconds = {
            "5 Sekunden": 5,
            "10 Sekunden": 10,
            "30 Sekunden": 30,
            "1 Minute": 60,
            "5 Minuten": 300
        }.get(interval_text, 10)

        self.temperature_timer.start(interval_seconds * 1000)

        self.start_monitoring_button.setEnabled(False)
        self.stop_monitoring_button.setEnabled(True)

        self.log_message(f"▶️ Temperatur-Überwachung gestartet (Intervall: {interval_text})")
        self.status_label.setText(f"▶️ Temperatur-Überwachung aktiv ({interval_text})")

    def stop_temperature_monitoring(self):
        """Stoppt kontinuierliche Temperatur-Überwachung"""
        self.temperature_timer.stop()

        self.start_monitoring_button.setEnabled(True)
        self.stop_monitoring_button.setEnabled(False)

        self.log_message("⏹️ Temperatur-Überwachung gestoppt")
        self.status_label.setText("⏹️ Temperatur-Überwachung gestoppt")

    def update_temperatures(self):
        """Aktualisiert alle Temperaturen (Timer-Callback)"""
        for ip in self.connected_cameras.keys():
            self.measure_single_temperature(ip)

    def update_temperature_history(self):
        """Aktualisiert Temperatur-Verlauf-Anzeige"""
        history_text = "=== TEMPERATUR-VERLAUF ===\n\n"

        for ip, readings in self.temperature_readings.items():
            history_text += f"Kamera {ip}:\n"

            # Zeige die letzten 10 Messungen
            recent_readings = readings[-10:] if len(readings) > 10 else readings

            for reading in recent_readings:
                temp = reading['temperature']
                time = reading['timestamp']
                method = reading['method']

                # Temperatur-Status-Symbol
                warning_temp = float(self.warning_temp_combo.currentText().replace("°C", ""))
                if temp >= warning_temp:
                    status = "🔴"
                elif temp >= warning_temp - 5:
                    status = "🟡"
                else:
                    status = "🟢"

                history_text += f"  {time}: {temp}°C {status} ({method})\n"

            history_text += "\n"

        self.temp_history_text.setPlainText(history_text)

    def export_temperature_data(self):
        """Exportiert Temperatur-Daten"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"temperatur_export_{timestamp}.csv"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("Kamera,Zeitstempel,Temperatur,Methode\n")

                for ip, readings in self.temperature_readings.items():
                    for reading in readings:
                        f.write(f"{ip},{reading['timestamp']},{reading['temperature']},{reading['method']}\n")

            self.log_message(f"💾 Temperatur-Daten exportiert: {filename}")
            QMessageBox.information(self, "Export erfolgreich", f"Temperatur-Daten wurden exportiert als:\n{filename}")

        except Exception as e:
            self.log_message(f"❌ Export-Fehler: {e}")
            QMessageBox.warning(self, "Export-Fehler", f"Fehler beim Exportieren:\n{e}")

    def test_error(self, error):
        """Test-Fehler"""
        self.log_message(f"❌ Test-Fehler: {error}")
        self.status_label.setText(f"❌ Fehler: {error}")

        self.test_all_button.setEnabled(True)
        self.test_all_button.setText("🚀 Alle Kameras testen")
        
    def log_message(self, message):
        """Fügt Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logging.info(message)

def main():
    """Hauptprogramm"""
    try:
        setup_logging()
        logging.info(f"Starte Netzwerk-Fix Baumer Monitor v{VERSION}")
        
        app = QApplication(sys.argv)
        app.setApplicationName("Netzwerk-Fix Baumer Monitor")
        app.setApplicationVersion(VERSION)
        
        window = NetworkFixedBaumerWindow()
        window.show()
        
        logging.info("Netzwerk-Fix GUI gestartet")
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Kritischer Fehler: {e}")
        print(f"FEHLER: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
