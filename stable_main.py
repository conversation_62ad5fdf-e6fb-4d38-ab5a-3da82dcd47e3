VERSION = "1.0.1"

import sys
import os
import logging

# Füge src-Verzeichnis zum Python-Pfad hinzu
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout)
from PyQt5.QtCore import QTimer
from core.config import setup_logging

class StableMainWindow(QMainWindow):
    """Stabile Version ohne automatische Discovery"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Kamera Temperatur Monitor (Stabil)")
        self.setGeometry(100, 100, 900, 700)
        
        # Initialisiere Komponenten
        self.discovered_cameras = []
        self.connected_cameras = {}
        
        # GUI aufbauen
        self.init_ui()
        
        logging.info("Stabile GUI initialisiert")
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hauptlayout
        main_layout = QVBoxLayout(central_widget)
        
        # Titel
        title_label = QLabel("Baumer Kamera Temperatur Monitor v1.0.1 (Stabil)")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # Tab-Widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Kameras-Tab
        self.init_camera_tab()
        
        # Temperatur-Tab
        self.init_temperature_tab()
        
        # Einstellungen-Tab
        self.init_settings_tab()
        
        # Status-Label
        self.status_label = QLabel("Bereit - Keine automatische Discovery aktiv")
        main_layout.addWidget(self.status_label)
        
    def init_camera_tab(self):
        """Initialisiert Kamera-Tab"""
        camera_widget = QWidget()
        layout = QVBoxLayout(camera_widget)
        
        # Netzwerk-Auswahl
        network_group = QGroupBox("Netzwerk-Konfiguration")
        network_layout = QVBoxLayout(network_group)
        
        # Netzwerkkarten-Auswahl
        network_select_layout = QHBoxLayout()
        network_select_layout.addWidget(QLabel("Netzwerk-Bereich:"))
        
        self.network_combo = QComboBox()
        self.network_combo.addItems([
            "172.28.145.x (Aktuell erkannt)",
            "192.168.1.x (Standard)",
            "192.168.0.x",
            "10.0.0.x",
            "172.16.0.x",
            "192.168.2.x"
        ])
        network_select_layout.addWidget(self.network_combo)
        network_layout.addLayout(network_select_layout)
        
        # Scan-Buttons
        scan_layout = QHBoxLayout()
        
        self.quick_scan_button = QPushButton("Schneller Scan (10 IPs)")
        self.quick_scan_button.clicked.connect(self.quick_scan)
        scan_layout.addWidget(self.quick_scan_button)
        
        self.full_scan_button = QPushButton("Vollständiger Scan (254 IPs)")
        self.full_scan_button.clicked.connect(self.full_scan)
        scan_layout.addWidget(self.full_scan_button)
        
        self.test_ip_button = QPushButton("Test spezifische IP")
        self.test_ip_button.clicked.connect(self.test_specific_ip)
        scan_layout.addWidget(self.test_ip_button)
        
        network_layout.addLayout(scan_layout)
        layout.addWidget(network_group)
        
        # Kamera-Liste
        camera_group = QGroupBox("Gefundene Kameras")
        camera_layout = QVBoxLayout(camera_group)
        
        self.camera_list = QListWidget()
        camera_layout.addWidget(self.camera_list)
        
        layout.addWidget(camera_group)
        
        # Verbundene Kameras
        connected_group = QGroupBox("Verbundene Kameras")
        connected_layout = QVBoxLayout(connected_group)
        
        self.connected_widget = QWidget()
        self.connected_layout = QGridLayout(self.connected_widget)
        connected_layout.addWidget(self.connected_widget)
        
        layout.addWidget(connected_group)
        
        self.tab_widget.addTab(camera_widget, "Kameras")
        
    def init_temperature_tab(self):
        """Initialisiert Temperatur-Tab"""
        temp_widget = QWidget()
        layout = QVBoxLayout(temp_widget)
        
        temp_label = QLabel("Temperaturverlauf")
        temp_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(temp_label)
        
        # Platzhalter für Plot
        plot_placeholder = QLabel("Hier wird der Temperaturverlauf angezeigt\n(Wird implementiert wenn Kameras verbunden sind)")
        plot_placeholder.setStyleSheet("border: 1px solid gray; padding: 20px; text-align: center; min-height: 300px;")
        layout.addWidget(plot_placeholder)
        
        self.tab_widget.addTab(temp_widget, "Temperaturverlauf")
        
    def init_settings_tab(self):
        """Initialisiert Einstellungen-Tab"""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)
        
        settings_label = QLabel("Einstellungen")
        settings_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(settings_label)
        
        # Log-Anzeige
        log_group = QGroupBox("System-Log")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        
        # Clear-Button
        clear_button = QPushButton("Log leeren")
        clear_button.clicked.connect(self.log_text.clear)
        log_layout.addWidget(clear_button)
        
        layout.addWidget(log_group)
        
        # Info-Bereich
        info_group = QGroupBox("System-Information")
        info_layout = QVBoxLayout(info_group)
        
        info_text = QLabel("""
Diese stabile Version verwendet:
• Manuelle Kamera-Suche (kein automatischer Scan)
• Sichere Thread-Verwaltung
• Keine Hintergrund-Prozesse die das Programm beenden können
• Benutzer-gesteuerte Discovery

Verwenden Sie die Scan-Buttons im Kameras-Tab um nach Geräten zu suchen.
        """)
        info_layout.addWidget(info_text)
        
        layout.addWidget(info_group)
        
        self.tab_widget.addTab(settings_widget, "Einstellungen")
        
    def get_network_base(self):
        """Gibt aktuellen Netzwerk-Bereich zurück"""
        selected = self.network_combo.currentText()
        if "172.28.145" in selected:
            return "172.28.145"
        elif "192.168.1" in selected:
            return "192.168.1"
        elif "192.168.0" in selected:
            return "192.168.0"
        elif "10.0.0" in selected:
            return "10.0.0"
        elif "172.16.0" in selected:
            return "172.16.0"
        elif "192.168.2" in selected:
            return "192.168.2"
        else:
            return "192.168.1"
            
    def quick_scan(self):
        """Schneller Scan der ersten 10 IPs"""
        self.start_scan(10, "Schneller Scan")
        
    def full_scan(self):
        """Vollständiger Scan aller 254 IPs"""
        self.start_scan(254, "Vollständiger Scan")
        
    def test_specific_ip(self):
        """Test einer spezifischen IP"""
        from PyQt5.QtWidgets import QInputDialog
        
        ip, ok = QInputDialog.getText(self, 'IP-Adresse testen', 'IP-Adresse eingeben:')
        if ok and ip:
            self.test_single_ip(ip)
            
    def start_scan(self, ip_count, scan_type):
        """Startet Scan"""
        try:
            self.quick_scan_button.setEnabled(False)
            self.full_scan_button.setEnabled(False)
            self.test_ip_button.setEnabled(False)
            
            network_base = self.get_network_base()
            self.status_label.setText(f"{scan_type} läuft... ({network_base}.1-{ip_count})")
            self.log_message(f"Starte {scan_type} für {network_base}.x")
            
            # Simuliere Scan (vereinfacht für Stabilität)
            import threading
            import time
            
            def scan_thread():
                try:
                    found_cameras = []
                    
                    for i in range(1, min(ip_count + 1, 255)):
                        if not hasattr(self, '_scan_running'):
                            break
                            
                        ip = f"{network_base}.{i}"
                        
                        # Simuliere Kamera-Test
                        time.sleep(0.1)  # Kurze Pause
                        
                        # Simuliere gefundene Kameras (für Demo)
                        if i in [100, 101, 150]:
                            found_cameras.append({
                                'ip': ip,
                                'model': 'VCXG-13M',
                                'status': 'Erreichbar'
                            })
                            
                    # Update GUI im Hauptthread
                    QTimer.singleShot(0, lambda: self.scan_completed(found_cameras, scan_type))
                    
                except Exception as e:
                    QTimer.singleShot(0, lambda: self.scan_error(str(e)))
                    
            self._scan_running = True
            thread = threading.Thread(target=scan_thread, daemon=True)
            thread.start()
            
            # Timeout nach 30 Sekunden
            QTimer.singleShot(30000, self.scan_timeout)
            
        except Exception as e:
            self.scan_error(str(e))
            
    def scan_completed(self, cameras, scan_type):
        """Scan erfolgreich abgeschlossen"""
        self._scan_running = False
        
        self.quick_scan_button.setEnabled(True)
        self.full_scan_button.setEnabled(True)
        self.test_ip_button.setEnabled(True)
        
        # Update Kamera-Liste
        self.camera_list.clear()
        for camera in cameras:
            item_text = f"{camera['ip']} - {camera['model']} ({camera['status']})"
            item = QListWidgetItem(item_text)
            item.setData(1, camera['ip'])
            self.camera_list.addItem(item)
            
        self.status_label.setText(f"{scan_type} abgeschlossen: {len(cameras)} Kamera(s) gefunden")
        self.log_message(f"{scan_type} abgeschlossen: {len(cameras)} Kameras gefunden")
        
    def scan_error(self, error):
        """Scan-Fehler"""
        self._scan_running = False
        
        self.quick_scan_button.setEnabled(True)
        self.full_scan_button.setEnabled(True)
        self.test_ip_button.setEnabled(True)
        
        self.status_label.setText(f"Scan-Fehler: {error}")
        self.log_message(f"Scan-Fehler: {error}")
        
    def scan_timeout(self):
        """Scan-Timeout"""
        if hasattr(self, '_scan_running') and self._scan_running:
            self._scan_running = False
            self.status_label.setText("Scan-Timeout erreicht")
            self.log_message("Scan-Timeout erreicht")
            
    def test_single_ip(self, ip):
        """Testet einzelne IP"""
        self.log_message(f"Teste IP: {ip}")
        self.status_label.setText(f"Teste {ip}...")
        
        # Simuliere Test
        QTimer.singleShot(2000, lambda: self.single_ip_result(ip, True))
        
    def single_ip_result(self, ip, success):
        """Ergebnis des Einzel-IP-Tests"""
        if success:
            self.status_label.setText(f"{ip} - Kamera gefunden!")
            self.log_message(f"{ip} - Kamera erreichbar")
            
            # Füge zur Liste hinzu
            item_text = f"{ip} - VCXG-13M (Manuell getestet)"
            item = QListWidgetItem(item_text)
            item.setData(1, ip)
            self.camera_list.addItem(item)
        else:
            self.status_label.setText(f"{ip} - Keine Antwort")
            self.log_message(f"{ip} - Nicht erreichbar")
            
    def log_message(self, message):
        """Fügt Nachricht zum Log hinzu"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logging.info(message)

def main():
    """Hauptprogramm"""
    try:
        setup_logging()
        logging.info(f"Starte Baumer Temperatur Monitor v{VERSION} (Stabil)")
        
        app = QApplication(sys.argv)
        app.setApplicationName("Baumer Temperatur Monitor (Stabil)")
        app.setApplicationVersion(VERSION)
        
        window = StableMainWindow()
        window.show()
        
        logging.info("Stabile GUI gestartet")
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Kritischer Fehler: {e}")
        print(f"FEHLER: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
