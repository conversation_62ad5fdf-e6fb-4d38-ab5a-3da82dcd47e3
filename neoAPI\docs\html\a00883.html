<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.CamInfo Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00883.html">CamInfo</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00880.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.CamInfo Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00086.html">Cam Interface Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Camera info container class which offers basic information about an available camera If <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">neoapi.CamInfoList</a> is called it will return a list of cameras, you can use the <a class="el" href="a00883.html" title="Camera info container class which offers basic information about an available camera If neoapi....">neoapi.CamInfo()</a> class to get information about a camera in this list.  
 <a href="a00883.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.CamInfo:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00883.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a1444e63e26d810f16085eee8e5c1e4dc"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a1444e63e26d810f16085eee8e5c1e4dc">__init__</a> (self, *args)</td></tr>
<tr class="separator:a1444e63e26d810f16085eee8e5c1e4dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b280fc28767fa40b626fa7d55e05e22"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a3b280fc28767fa40b626fa7d55e05e22">GetId</a> (self)</td></tr>
<tr class="memdesc:a3b280fc28767fa40b626fa7d55e05e22"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the ID of the camera, this is unique to each camera in combination with a Baumer producer.  <a href="#a3b280fc28767fa40b626fa7d55e05e22">More...</a><br /></td></tr>
<tr class="separator:a3b280fc28767fa40b626fa7d55e05e22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5b279a8ae9ce9fe893d619a8cd94d40"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#ad5b279a8ae9ce9fe893d619a8cd94d40">GetModelName</a> (self)</td></tr>
<tr class="memdesc:ad5b279a8ae9ce9fe893d619a8cd94d40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the camera model name.  <a href="#ad5b279a8ae9ce9fe893d619a8cd94d40">More...</a><br /></td></tr>
<tr class="separator:ad5b279a8ae9ce9fe893d619a8cd94d40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9a7e2ddc7afebf27dfaf9f39142ae004"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a9a7e2ddc7afebf27dfaf9f39142ae004">GetSerialNumber</a> (self)</td></tr>
<tr class="memdesc:a9a7e2ddc7afebf27dfaf9f39142ae004"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the camera serial number.  <a href="#a9a7e2ddc7afebf27dfaf9f39142ae004">More...</a><br /></td></tr>
<tr class="separator:a9a7e2ddc7afebf27dfaf9f39142ae004"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37322389fc2a46340e7b5a2127b8d5fd"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a37322389fc2a46340e7b5a2127b8d5fd">GetTLType</a> (self)</td></tr>
<tr class="memdesc:a37322389fc2a46340e7b5a2127b8d5fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the transport layer type.  <a href="#a37322389fc2a46340e7b5a2127b8d5fd">More...</a><br /></td></tr>
<tr class="separator:a37322389fc2a46340e7b5a2127b8d5fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab32d45aaec3dd88fd8710536cd3992e9"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#ab32d45aaec3dd88fd8710536cd3992e9">GetVendorName</a> (self)</td></tr>
<tr class="memdesc:ab32d45aaec3dd88fd8710536cd3992e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the camera vendor name.  <a href="#ab32d45aaec3dd88fd8710536cd3992e9">More...</a><br /></td></tr>
<tr class="separator:ab32d45aaec3dd88fd8710536cd3992e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22ac983ae10d350dc8ddfc267550e81c"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a22ac983ae10d350dc8ddfc267550e81c">GetUSB3VisionGUID</a> (self)</td></tr>
<tr class="memdesc:a22ac983ae10d350dc8ddfc267550e81c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the USB3 vision GUID.  <a href="#a22ac983ae10d350dc8ddfc267550e81c">More...</a><br /></td></tr>
<tr class="separator:a22ac983ae10d350dc8ddfc267550e81c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5de8d52a362cb493cd9e59ac694c835c"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a5de8d52a362cb493cd9e59ac694c835c">GetUSBPortID</a> (self)</td></tr>
<tr class="memdesc:a5de8d52a362cb493cd9e59ac694c835c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the USB port ID.  <a href="#a5de8d52a362cb493cd9e59ac694c835c">More...</a><br /></td></tr>
<tr class="separator:a5de8d52a362cb493cd9e59ac694c835c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a175411ea401a6181a618fc17d2e5679c"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a175411ea401a6181a618fc17d2e5679c">GetGevIpAddress</a> (self)</td></tr>
<tr class="memdesc:a175411ea401a6181a618fc17d2e5679c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the cameras ethernet IP address.  <a href="#a175411ea401a6181a618fc17d2e5679c">More...</a><br /></td></tr>
<tr class="separator:a175411ea401a6181a618fc17d2e5679c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d8c1ef2009f95f8c0f1151d3aa6e81b"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a4d8c1ef2009f95f8c0f1151d3aa6e81b">GetGevSubnetMask</a> (self)</td></tr>
<tr class="memdesc:a4d8c1ef2009f95f8c0f1151d3aa6e81b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the cameras ethernet subnet mask.  <a href="#a4d8c1ef2009f95f8c0f1151d3aa6e81b">More...</a><br /></td></tr>
<tr class="separator:a4d8c1ef2009f95f8c0f1151d3aa6e81b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a0352d41af150acea7033ec7cffe4cf"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a6a0352d41af150acea7033ec7cffe4cf">GetGevGateway</a> (self)</td></tr>
<tr class="memdesc:a6a0352d41af150acea7033ec7cffe4cf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the cameras ethernet gateway IP.  <a href="#a6a0352d41af150acea7033ec7cffe4cf">More...</a><br /></td></tr>
<tr class="separator:a6a0352d41af150acea7033ec7cffe4cf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab16917727ebcd2c47cd17bae14f8ee90"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#ab16917727ebcd2c47cd17bae14f8ee90">GetGevMACAddress</a> (self)</td></tr>
<tr class="memdesc:ab16917727ebcd2c47cd17bae14f8ee90"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the cameras ethernet MAC address.  <a href="#ab16917727ebcd2c47cd17bae14f8ee90">More...</a><br /></td></tr>
<tr class="separator:ab16917727ebcd2c47cd17bae14f8ee90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27191e69df494ad411b19603074cece0"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a27191e69df494ad411b19603074cece0">IsConnectable</a> (self)</td></tr>
<tr class="memdesc:a27191e69df494ad411b19603074cece0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Check if this camera is available for connection.  <a href="#a27191e69df494ad411b19603074cece0">More...</a><br /></td></tr>
<tr class="separator:a27191e69df494ad411b19603074cece0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a58c833eb8d0490277872e0ed78e29534"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00883.html#a58c833eb8d0490277872e0ed78e29534">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:a58c833eb8d0490277872e0ed78e29534"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#a58c833eb8d0490277872e0ed78e29534">More...</a><br /></td></tr>
<tr class="separator:a58c833eb8d0490277872e0ed78e29534"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Camera info container class which offers basic information about an available camera If <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">neoapi.CamInfoList</a> is called it will return a list of cameras, you can use the <a class="el" href="a00883.html" title="Camera info container class which offers basic information about an available camera If neoapi....">neoapi.CamInfo()</a> class to get information about a camera in this list. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a1444e63e26d810f16085eee8e5c1e4dc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1444e63e26d810f16085eee8e5c1e4dc">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.CamInfo.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">   \brief      Constructor
</pre> <dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>object</b> (optional) A <a class="el" href="a00883.html" title="Camera info container class which offers basic information about an available camera If neoapi....">CamInfo</a> object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a3b280fc28767fa40b626fa7d55e05e22"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3b280fc28767fa40b626fa7d55e05e22">&#9670;&nbsp;</a></span>GetId()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetId </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the ID of the camera, this is unique to each camera in combination with a Baumer producer. </p>
<dl class="section return"><dt>Returns</dt><dd>The ID </dd></dl>

</div>
</div>
<a id="ad5b279a8ae9ce9fe893d619a8cd94d40"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad5b279a8ae9ce9fe893d619a8cd94d40">&#9670;&nbsp;</a></span>GetModelName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetModelName </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the camera model name. </p>
<dl class="section return"><dt>Returns</dt><dd>The model name </dd></dl>

</div>
</div>
<a id="a9a7e2ddc7afebf27dfaf9f39142ae004"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9a7e2ddc7afebf27dfaf9f39142ae004">&#9670;&nbsp;</a></span>GetSerialNumber()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetSerialNumber </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the camera serial number. </p>
<dl class="section return"><dt>Returns</dt><dd>The serial number </dd></dl>

</div>
</div>
<a id="a37322389fc2a46340e7b5a2127b8d5fd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a37322389fc2a46340e7b5a2127b8d5fd">&#9670;&nbsp;</a></span>GetTLType()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetTLType </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the transport layer type. </p>
<dl class="section return"><dt>Returns</dt><dd>The transport layer type </dd></dl>

</div>
</div>
<a id="ab32d45aaec3dd88fd8710536cd3992e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab32d45aaec3dd88fd8710536cd3992e9">&#9670;&nbsp;</a></span>GetVendorName()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetVendorName </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the camera vendor name. </p>
<dl class="section return"><dt>Returns</dt><dd>The vendor name </dd></dl>

</div>
</div>
<a id="a22ac983ae10d350dc8ddfc267550e81c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a22ac983ae10d350dc8ddfc267550e81c">&#9670;&nbsp;</a></span>GetUSB3VisionGUID()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetUSB3VisionGUID </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the USB3 vision GUID. </p>
<dl class="section note"><dt>Note</dt><dd>Only valid for USB3 Vision cameras </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>The USB3 Vision GUID </dd></dl>

</div>
</div>
<a id="a5de8d52a362cb493cd9e59ac694c835c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5de8d52a362cb493cd9e59ac694c835c">&#9670;&nbsp;</a></span>GetUSBPortID()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetUSBPortID </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the USB port ID. </p>
<dl class="section note"><dt>Note</dt><dd>Only valid for USB3 Vision cameras </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>The port ID </dd></dl>

</div>
</div>
<a id="a175411ea401a6181a618fc17d2e5679c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a175411ea401a6181a618fc17d2e5679c">&#9670;&nbsp;</a></span>GetGevIpAddress()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetGevIpAddress </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the cameras ethernet IP address. </p>
<dl class="section note"><dt>Note</dt><dd>Only valid for GigE Vision cameras </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>The IP address </dd></dl>

</div>
</div>
<a id="a4d8c1ef2009f95f8c0f1151d3aa6e81b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4d8c1ef2009f95f8c0f1151d3aa6e81b">&#9670;&nbsp;</a></span>GetGevSubnetMask()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetGevSubnetMask </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the cameras ethernet subnet mask. </p>
<dl class="section note"><dt>Note</dt><dd>Only valid for GigE Vision cameras </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>The subnet mask </dd></dl>

</div>
</div>
<a id="a6a0352d41af150acea7033ec7cffe4cf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6a0352d41af150acea7033ec7cffe4cf">&#9670;&nbsp;</a></span>GetGevGateway()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetGevGateway </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the cameras ethernet gateway IP. </p>
<dl class="section note"><dt>Note</dt><dd>Only valid for GigE Vision cameras </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>The gateway </dd></dl>

</div>
</div>
<a id="ab16917727ebcd2c47cd17bae14f8ee90"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab16917727ebcd2c47cd17bae14f8ee90">&#9670;&nbsp;</a></span>GetGevMACAddress()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.CamInfo.GetGevMACAddress </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the cameras ethernet MAC address. </p>
<dl class="section note"><dt>Note</dt><dd>Only valid for GigE Vision cameras </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>The MAC address </dd></dl>

</div>
</div>
<a id="a27191e69df494ad411b19603074cece0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27191e69df494ad411b19603074cece0">&#9670;&nbsp;</a></span>IsConnectable()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.CamInfo.IsConnectable </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Check if this camera is available for connection. </p>
<dl class="section return"><dt>Returns</dt><dd>true if this camera can be connected, false otherwise </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a58c833eb8d0490277872e0ed78e29534"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a58c833eb8d0490277872e0ed78e29534">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.CamInfo.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
