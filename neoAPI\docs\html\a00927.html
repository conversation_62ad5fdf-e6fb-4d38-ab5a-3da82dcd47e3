<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Trace Programming Concepts of the neoAPI</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Trace Programming Concepts of the neoAPI </div>  </div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#autotoc_md104">Trace</a><ul><li class="level2"><a href="#autotoc_md105">Trace to Callback</a></li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><p>This document describes the concepts around Trace and how they can be used in the neoAPI.</p>
<h1><a class="anchor" id="autotoc_md104"></a>
Trace</h1>
<p>The neoAPI trace logs internal operations, data and events. It can help you to understand how the API and the camera is working and helps uncover common errors. The shared library <code>tracelogger.dll/.so</code> provides the <code>NeoAPI::NeoTrace</code> class which is used for tracing.</p>
<p>The user application can also use the tracelogger this will ensure that all messages are compiled in one file and helps to follow the flow of events.</p>
<p>The trace can be enabled in different ways:</p>
<ul>
<li>By creating an (empty) file named 'tracelogger.ini' in <code>%LOCALAPPDATA%\Baumer</code> (Windows) or <code>$home/.local/share/Baumer</code> (Linux). The trace file(s) will be generated in the same folder. This is especially useful to debug applications in production where you might not be able to change the existing application code.</li>
<li>By a call to 'EnableLogfile' for file logging. Every subsequent call 'EnableLogCallback' or 'EnableLogfile' will replace the old trace target with a new one.</li>
<li>By a call to 'EnableLogCallback' where all trace messages are received by a callback method.</li>
</ul>
<blockquote class="doxtable">
<p>Note:</p>
<p>Trace to callback and file can be used in parallel. </p>
</blockquote>
<p>## Trace to Trace-File </p><div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line"></div><div class="line">camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line">camera.Connect()</div><div class="line"></div><div class="line">logfile = <span class="stringliteral">&quot;test.log&quot;</span></div><div class="line">trace = <a class="code" href="a00899.html">neoapi.NeoTrace</a>()</div><div class="line">trace.SetSeverity(neoapi.NeoTraceSeverity_All)          <span class="comment"># set the severity</span></div><div class="line">trace.EnableLogfile(logfile)                            <span class="comment"># enable trace to logfile</span></div><div class="line"></div><div class="line">trace.Info(<span class="stringliteral">&quot;The Info&quot;</span>, <span class="stringliteral">&quot;Module Name&quot;</span>, <span class="stringliteral">&quot;Function name&quot;</span>, trace)  <span class="comment"># trace something</span></div><div class="line">trace.Warning(<span class="stringliteral">&quot;The Warning&quot;</span>, <span class="stringliteral">&quot;Module Name&quot;</span>, <span class="stringliteral">&quot;Function name&quot;</span>, camera)</div><div class="line">trace.Error(<span class="stringliteral">&quot;The Error&quot;</span>)</div><div class="line"></div><div class="line"><span class="comment"># calling this (and other) methods will produce a trace message from the API</span></div><div class="line">print(<span class="stringliteral">&quot;prog: &quot;</span>, <a class="code" href="a00859.html#aca5a01fb66bf17b8f9cbe250d3f3dc14">neoapi.Cam.GetLibraryVersion</a>())</div><div class="line">trace.DisableLogfile()</div></div><!-- fragment --><h2><a class="anchor" id="autotoc_md105"></a>
Trace to Callback</h2>
<p>Alternatively you can trace to a callback. This gives you the option to trace neoAPI messages to your own system.</p>
<div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line"></div><div class="line"><span class="comment"># write the callback to handle the trace</span></div><div class="line"><span class="keyword">class </span>TestTraceCallback():</div><div class="line">    <span class="keyword">def </span>tracecallback(self, msg):</div><div class="line">        print(<span class="stringliteral">&quot;trace: &quot;</span>, str(msg))</div><div class="line"></div><div class="line">camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line">camera.Connect()</div><div class="line"></div><div class="line">trace = <a class="code" href="a00899.html">neoapi.NeoTrace</a>()</div><div class="line">callback = TestTraceCallback()</div><div class="line">trace.EnableLogCallback(callback.tracecallback)         <span class="comment"># register the callback</span></div><div class="line">trace.SetSeverity(neoapi.NeoTraceSeverity_All)          <span class="comment"># set the severity</span></div><div class="line"></div><div class="line">trace.Info(<span class="stringliteral">&quot;The Info&quot;</span>, <span class="stringliteral">&quot;my app&quot;</span>, <span class="stringliteral">&quot;main&quot;</span>, camera)        <span class="comment"># trace something</span></div><div class="line">trace.Warning(<span class="stringliteral">&quot;The Warning&quot;</span>)</div><div class="line">trace.Error(<span class="stringliteral">&quot;The Error&quot;</span>)</div><div class="line"></div><div class="line"><span class="comment"># calling this (and other) methods will produce a trace message from the API</span></div><div class="line">print(<span class="stringliteral">&quot;prog: &quot;</span>, <a class="code" href="a00859.html#aca5a01fb66bf17b8f9cbe250d3f3dc14">neoapi.Cam.GetLibraryVersion</a>())</div><div class="line">trace.DisableLogCallback()</div></div><!-- fragment --> </div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
