<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Classes</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">Classes</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Overview of all neoAPI classes</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span><span onclick="javascript:toggleLevel(3);">3</span><span onclick="javascript:toggleLevel(4);">4</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>_object</b></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00763.html" target="_self">neoapi.FeatureAccess</a></td><td class="desc">Class to controll GenICam features </td></tr>
<tr id="row_1_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_1_" class="arrow" onclick="toggleFolder('1_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>Exception</b></td><td class="desc"></td></tr>
<tr id="row_1_0_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_1_0_" class="arrow" onclick="toggleFolder('1_0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00767.html" target="_self">neoapi.NeoException</a></td><td class="desc">Base neoAPI Exception class </td></tr>
<tr id="row_1_0_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00779.html" target="_self">neoapi.FeatureAccessException</a></td><td class="desc"><a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> not accessible Exception </td></tr>
<tr id="row_1_0_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00787.html" target="_self">neoapi.FileAccessException</a></td><td class="desc">File not accessible Exception </td></tr>
<tr id="row_1_0_2_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00791.html" target="_self">neoapi.InvalidArgumentException</a></td><td class="desc">Invalid Arguments Exception </td></tr>
<tr id="row_1_0_3_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00775.html" target="_self">neoapi.NoAccessException</a></td><td class="desc">Camera not accessible Exception </td></tr>
<tr id="row_1_0_4_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00783.html" target="_self">neoapi.NoImageBufferException</a></td><td class="desc">Requesting an image while holding all available image resources </td></tr>
<tr id="row_1_0_5_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00771.html" target="_self">neoapi.NotConnectedException</a></td><td class="desc">No camera connected Exception </td></tr>
<tr id="row_2_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_2_" class="arrow" onclick="toggleFolder('2_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>object</b></td><td class="desc"></td></tr>
<tr id="row_2_0_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_2_0_" class="arrow" onclick="toggleFolder('2_0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00823.html" target="_self">neoapi.BaseFeature</a></td><td class="desc">Base <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> class providing the interface to be used independent of <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> data-type </td></tr>
<tr id="row_2_0_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00839.html" target="_self">neoapi.BoolFeature</a></td><td class="desc">Class providing the 'IBoolean' interface </td></tr>
<tr id="row_2_0_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00843.html" target="_self">neoapi.CommandFeature</a></td><td class="desc">Class providing the 'ICommand' interface </td></tr>
<tr id="row_2_0_2_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00827.html" target="_self">neoapi.DoubleFeature</a></td><td class="desc">Class providing the 'IFloat' interface </td></tr>
<tr id="row_2_0_3_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span id="arr_2_0_3_" class="arrow" onclick="toggleFolder('2_0_3_')">&#9658;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00847.html" target="_self">neoapi.EnumerationFeature</a></td><td class="desc">Base class providing the 'IEnumeration' interface </td></tr>
<tr id="row_2_0_3_0_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00095.html" target="_self">neoapi.CAcquisitionMode</a></td><td class="desc">Sets the acquisition mode of the device </td></tr>
<tr id="row_2_0_3_1_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00099.html" target="_self">neoapi.CAcquisitionStatusSelector</a></td><td class="desc">Selects the internal acquisition signal to read using AcquisitionStatus </td></tr>
<tr id="row_2_0_3_2_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00103.html" target="_self">neoapi.CApertureStatus</a></td><td class="desc">Reads the status of the aperture </td></tr>
<tr id="row_2_0_3_3_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00107.html" target="_self">neoapi.CAutoFeatureRegionMode</a></td><td class="desc">Controls the mode of the selected Auto <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> Region </td></tr>
<tr id="row_2_0_3_4_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00111.html" target="_self">neoapi.CAutoFeatureRegionReference</a></td><td class="desc">Selects the Reference Region of interest </td></tr>
<tr id="row_2_0_3_5_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00115.html" target="_self">neoapi.CAutoFeatureRegionSelector</a></td><td class="desc">Selects the region of interest to control </td></tr>
<tr id="row_2_0_3_6_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00123.html" target="_self">neoapi.CBalanceWhiteAuto</a></td><td class="desc">Controls the mode for automatic white balancing between the color channels </td></tr>
<tr id="row_2_0_3_7_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00127.html" target="_self">neoapi.CBalanceWhiteAutoStatus</a></td><td class="desc">Returns the status of BalanceWhiteAuto </td></tr>
<tr id="row_2_0_3_8_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00131.html" target="_self">neoapi.CBaudrate</a></td><td class="desc">Sets the baud rate of the RS232 interface </td></tr>
<tr id="row_2_0_3_9_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00135.html" target="_self">neoapi.CBinningHorizontalMode</a></td><td class="desc">Sets the mode to use to combine horizontal photo-sensitive cells together when BinningHorizontal is used </td></tr>
<tr id="row_2_0_3_10_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00139.html" target="_self">neoapi.CBinningSelector</a></td><td class="desc">Selects which binning engine is controlled by the BinningHorizontal and BinningVertical features </td></tr>
<tr id="row_2_0_3_11_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00143.html" target="_self">neoapi.CBinningVerticalMode</a></td><td class="desc">Sets the mode to use to combine vertical photo-sensitive cells together when BinningVertical is used </td></tr>
<tr id="row_2_0_3_12_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00147.html" target="_self">neoapi.CBlackLevelSelector</a></td><td class="desc">Selects which Black Level is controlled by the various Black Level features </td></tr>
<tr id="row_2_0_3_13_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00151.html" target="_self">neoapi.CBlackSunSuppression</a></td><td class="desc">Controls the sensor internal feature for avoiding the black sun effect </td></tr>
<tr id="row_2_0_3_14_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00715.html" target="_self">neoapi.CboCalibrationDataConfigurationMode</a></td><td class="desc">Controls if the calibration data configuration mode is active </td></tr>
<tr id="row_2_0_3_15_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00719.html" target="_self">neoapi.CboCalibrationMatrixSelector</a></td><td class="desc">Selects the calibration matrix </td></tr>
<tr id="row_2_0_3_16_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00723.html" target="_self">neoapi.CboCalibrationMatrixValueSelector</a></td><td class="desc">Value selector of calibration matrix </td></tr>
<tr id="row_2_0_3_17_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00727.html" target="_self">neoapi.CboCalibrationVectorSelector</a></td><td class="desc">Selects the calibration vector </td></tr>
<tr id="row_2_0_3_18_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00731.html" target="_self">neoapi.CboCalibrationVectorValueSelector</a></td><td class="desc">Value selector of calibration vector </td></tr>
<tr id="row_2_0_3_19_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00735.html" target="_self">neoapi.CboGeometryDistortionValueSelector</a></td><td class="desc">Value Selector of geometry distortion </td></tr>
<tr id="row_2_0_3_20_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00119.html" target="_self">neoapi.CBOPFShift</a></td><td class="desc">Selects the shift factor for 8bit pixel format calculated from 12 bit mode </td></tr>
<tr id="row_2_0_3_21_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00155.html" target="_self">neoapi.CBoSequencerEnable</a></td><td class="desc">Enables the sequencer for special multi-frame mode </td></tr>
<tr id="row_2_0_3_22_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00159.html" target="_self">neoapi.CBoSequencerIOSelector</a></td><td class="desc">Selects the Sequencers output lines </td></tr>
<tr id="row_2_0_3_23_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00163.html" target="_self">neoapi.CBoSequencerMode</a></td><td class="desc">Specifies the running mode of the sequencer </td></tr>
<tr id="row_2_0_3_24_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00167.html" target="_self">neoapi.CBoSequencerSensorDigitizationTaps</a></td><td class="desc">Sets the number of digitized samples outputted simultaneously by the camera A/D conversion stage for the sequencer </td></tr>
<tr id="row_2_0_3_25_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00171.html" target="_self">neoapi.CBoSequencerStart</a></td><td class="desc">Starts or stopps the configured sequence </td></tr>
<tr id="row_2_0_3_26_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00739.html" target="_self">neoapi.CboSerialConfigBaudRate</a></td><td class="desc">Serial interface clock frequency </td></tr>
<tr id="row_2_0_3_27_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00743.html" target="_self">neoapi.CboSerialConfigDataBits</a></td><td class="desc">Number of data bits </td></tr>
<tr id="row_2_0_3_28_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00747.html" target="_self">neoapi.CboSerialConfigParity</a></td><td class="desc">Serial interface parity </td></tr>
<tr id="row_2_0_3_29_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00751.html" target="_self">neoapi.CboSerialConfigStopBits</a></td><td class="desc">Number of stop bits </td></tr>
<tr id="row_2_0_3_30_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00755.html" target="_self">neoapi.CboSerialMode</a></td><td class="desc">States the interface mode of the serial interface </td></tr>
<tr id="row_2_0_3_31_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00759.html" target="_self">neoapi.CboSerialSelector</a></td><td class="desc">Selects which serial interface to configure </td></tr>
<tr id="row_2_0_3_32_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00175.html" target="_self">neoapi.CBrightnessAutoPriority</a></td><td class="desc">Sets the highest priority auto feature to adjust the brightness </td></tr>
<tr id="row_2_0_3_33_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00179.html" target="_self">neoapi.CBrightnessCorrection</a></td><td class="desc">Enables the Brightness Correction </td></tr>
<tr id="row_2_0_3_34_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00183.html" target="_self">neoapi.CCalibrationMatrixColorSelector</a></td><td class="desc">Selects the color calibration matrix </td></tr>
<tr id="row_2_0_3_35_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00187.html" target="_self">neoapi.CCalibrationMatrixValueSelector</a></td><td class="desc">Selects the gain factor of the selected calibration matrix </td></tr>
<tr id="row_2_0_3_36_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00191.html" target="_self">neoapi.CChunkSelector</a></td><td class="desc">Selects which Chunk to enable or control </td></tr>
<tr id="row_2_0_3_37_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00195.html" target="_self">neoapi.CClConfiguration</a></td><td class="desc">This Camera Link specific feature describes the configuration used by the camera </td></tr>
<tr id="row_2_0_3_38_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00199.html" target="_self">neoapi.CClTimeSlotsCount</a></td><td class="desc">This Camera Link specific feature describes the time multiplexing of the camera link connection to transfer more than the configuration allows, in one single clock </td></tr>
<tr id="row_2_0_3_39_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00203.html" target="_self">neoapi.CColorTransformationAuto</a></td><td class="desc">Controls the mode for automatic adjusting the gains of the active transformation matrix </td></tr>
<tr id="row_2_0_3_40_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00207.html" target="_self">neoapi.CColorTransformationFactoryListSelector</a></td><td class="desc">Selects the color transformation factory list tuned to the given color temeperature </td></tr>
<tr id="row_2_0_3_41_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00211.html" target="_self">neoapi.CColorTransformationSelector</a></td><td class="desc">Selects which Color Transformation module is controlled by the various Color Transformation features </td></tr>
<tr id="row_2_0_3_42_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00215.html" target="_self">neoapi.CColorTransformationValueSelector</a></td><td class="desc">Selects the Gain factor or Offset of the Transformation matrix to access in the selected Color Transformation module </td></tr>
<tr id="row_2_0_3_43_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00219.html" target="_self">neoapi.CComponentSelector</a></td><td class="desc">Selects a component to activate/deactivate its data streaming </td></tr>
<tr id="row_2_0_3_44_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00223.html" target="_self">neoapi.CCounterEventActivation</a></td><td class="desc">Selects the Activation mode Event Source signal </td></tr>
<tr id="row_2_0_3_45_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00227.html" target="_self">neoapi.CCounterEventSource</a></td><td class="desc">Select the events that will be the source to increment the Counter </td></tr>
<tr id="row_2_0_3_46_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00231.html" target="_self">neoapi.CCounterResetActivation</a></td><td class="desc">Selects the Activation mode of the Counter Reset Source signal </td></tr>
<tr id="row_2_0_3_47_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00235.html" target="_self">neoapi.CCounterResetSource</a></td><td class="desc">Selects the signals that will be the source to reset the Counter </td></tr>
<tr id="row_2_0_3_48_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00239.html" target="_self">neoapi.CCounterSelector</a></td><td class="desc">Selects which Counter to configure </td></tr>
<tr id="row_2_0_3_49_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00243.html" target="_self">neoapi.CCustomDataConfigurationMode</a></td><td class="desc">Controls if the custom data configuration mode is active </td></tr>
<tr id="row_2_0_3_50_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00247.html" target="_self">neoapi.CDecimationHorizontalMode</a></td><td class="desc">Sets the mode used to reduce the horizontal resolution when DecimationHorizontal is used </td></tr>
<tr id="row_2_0_3_51_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00251.html" target="_self">neoapi.CDecimationVerticalMode</a></td><td class="desc">Sets the mode used to reduce the Vertical resolution when DecimationVertical is used </td></tr>
<tr id="row_2_0_3_52_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00255.html" target="_self">neoapi.CDefectPixelListSelector</a></td><td class="desc">Selects which Defect Pixel List to control </td></tr>
<tr id="row_2_0_3_53_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00259.html" target="_self">neoapi.CDeviceCharacterSet</a></td><td class="desc">Character set used by the strings of the device </td></tr>
<tr id="row_2_0_3_54_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00263.html" target="_self">neoapi.CDeviceClockSelector</a></td><td class="desc">Selects the clock frequency to access from the device </td></tr>
<tr id="row_2_0_3_55_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00267.html" target="_self">neoapi.CDeviceFrontUARTSource</a></td><td class="desc">Source control for frontside UART interface </td></tr>
<tr id="row_2_0_3_56_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00271.html" target="_self">neoapi.CDeviceLicense</a></td><td class="desc">Returns if the license at the device is valid or not for the license type, selected by the DeviceLicenseTypeSelector feature </td></tr>
<tr id="row_2_0_3_57_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00275.html" target="_self">neoapi.CDeviceLicenseTypeSelector</a></td><td class="desc">Selects the available License types </td></tr>
<tr id="row_2_0_3_58_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00279.html" target="_self">neoapi.CDeviceLinkHeartbeatMode</a></td><td class="desc">Activate or deactivate the Link's heartbeat </td></tr>
<tr id="row_2_0_3_59_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00283.html" target="_self">neoapi.CDeviceLinkSelector</a></td><td class="desc">Selects which Link of the device to control </td></tr>
<tr id="row_2_0_3_60_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00287.html" target="_self">neoapi.CDeviceLinkThroughputLimitMode</a></td><td class="desc">Controls if the DeviceLinkThroughputLimit is active </td></tr>
<tr id="row_2_0_3_61_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00291.html" target="_self">neoapi.CDeviceRegistersEndianness</a></td><td class="desc">Endianness of the registers of the device </td></tr>
<tr id="row_2_0_3_62_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00295.html" target="_self">neoapi.CDeviceScanType</a></td><td class="desc">Scan type of the sensor of the device </td></tr>
<tr id="row_2_0_3_63_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00299.html" target="_self">neoapi.CDeviceSensorSelector</a></td><td class="desc">Selects which sensor is controlled by the various sensor specific features </td></tr>
<tr id="row_2_0_3_64_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00303.html" target="_self">neoapi.CDeviceSensorType</a></td><td class="desc">Specifies the type of the sensor </td></tr>
<tr id="row_2_0_3_65_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00307.html" target="_self">neoapi.CDeviceSensorVersion</a></td><td class="desc">Specifies the version of the CMOSIS sensor </td></tr>
<tr id="row_2_0_3_66_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00311.html" target="_self">neoapi.CDeviceSerialPortBaudRate</a></td><td class="desc">This feature controls the baud rate used by the selected serial port </td></tr>
<tr id="row_2_0_3_67_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00315.html" target="_self">neoapi.CDeviceSerialPortSelector</a></td><td class="desc">Selects which serial port of the device to control </td></tr>
<tr id="row_2_0_3_68_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00319.html" target="_self">neoapi.CDeviceStreamChannelEndianness</a></td><td class="desc">Endianness of multi-byte pixel data for this stream </td></tr>
<tr id="row_2_0_3_69_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00323.html" target="_self">neoapi.CDeviceStreamChannelType</a></td><td class="desc">Reports the type of the stream channel </td></tr>
<tr id="row_2_0_3_70_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00331.html" target="_self">neoapi.CDeviceTapGeometry</a></td><td class="desc">This device tap geometry feature describes the geometrical properties characterizing the taps of a camera as presented at the output of the device </td></tr>
<tr id="row_2_0_3_71_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00335.html" target="_self">neoapi.CDeviceTemperatureSelector</a></td><td class="desc">Selects the location within the device, where the temperature will be measured </td></tr>
<tr id="row_2_0_3_72_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00339.html" target="_self">neoapi.CDeviceTemperatureStatus</a></td><td class="desc">Returns the current temperature status of the device </td></tr>
<tr id="row_2_0_3_73_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00343.html" target="_self">neoapi.CDeviceTemperatureStatusTransitionSelector</a></td><td class="desc">Selects which temperature transition is controlled by the feature DeviceTemperatureStatusTransition </td></tr>
<tr id="row_2_0_3_74_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00327.html" target="_self">neoapi.CDeviceTLType</a></td><td class="desc">Transport Layer type of the device </td></tr>
<tr id="row_2_0_3_75_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00347.html" target="_self">neoapi.CDeviceType</a></td><td class="desc">Returns the device type </td></tr>
<tr id="row_2_0_3_76_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00351.html" target="_self">neoapi.CEventNotification</a></td><td class="desc">Activate or deactivate the notification to the host application of the occurrence of the selected Event </td></tr>
<tr id="row_2_0_3_77_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00355.html" target="_self">neoapi.CEventSelector</a></td><td class="desc">Selects which Event to signal to the host application </td></tr>
<tr id="row_2_0_3_78_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00359.html" target="_self">neoapi.CExposureAuto</a></td><td class="desc">Sets the automatic exposure mode when ExposureMode is Timed </td></tr>
<tr id="row_2_0_3_79_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00363.html" target="_self">neoapi.CExposureMode</a></td><td class="desc">Sets the operation mode of the Exposure </td></tr>
<tr id="row_2_0_3_80_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00367.html" target="_self">neoapi.CFileOpenMode</a></td><td class="desc">Selects the access mode in which a file is opened in the device </td></tr>
<tr id="row_2_0_3_81_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00371.html" target="_self">neoapi.CFileOperationSelector</a></td><td class="desc">Selects the target operation for the selected file in the device </td></tr>
<tr id="row_2_0_3_82_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00375.html" target="_self">neoapi.CFileOperationStatus</a></td><td class="desc">Represents the file operation execution status </td></tr>
<tr id="row_2_0_3_83_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00379.html" target="_self">neoapi.CFileSelector</a></td><td class="desc">Selects the target file in the device </td></tr>
<tr id="row_2_0_3_84_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00383.html" target="_self">neoapi.CFocalLengthStatus</a></td><td class="desc">Reads the status of the focal length </td></tr>
<tr id="row_2_0_3_85_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00387.html" target="_self">neoapi.CFocusStatus</a></td><td class="desc">Reads the status of the focus </td></tr>
<tr id="row_2_0_3_86_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00391.html" target="_self">neoapi.CGainAuto</a></td><td class="desc">Sets the automatic gain control (AGC) mode </td></tr>
<tr id="row_2_0_3_87_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00395.html" target="_self">neoapi.CGainSelector</a></td><td class="desc">Selects which Gain is controlled by the various Gain features </td></tr>
<tr id="row_2_0_3_88_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00399.html" target="_self">neoapi.CGenDCStreamingMode</a></td><td class="desc">Controls the device's streaming format </td></tr>
<tr id="row_2_0_3_89_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00403.html" target="_self">neoapi.CGenDCStreamingStatus</a></td><td class="desc">Returns whether the current device data streaming format is GenDC </td></tr>
<tr id="row_2_0_3_90_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00407.html" target="_self">neoapi.CGevCCP</a></td><td class="desc">Controls the device access privilege of an application </td></tr>
<tr id="row_2_0_3_91_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00411.html" target="_self">neoapi.CGevGVCPExtendedStatusCodesSelector</a></td><td class="desc">Selects the GigE Vision version to control extended status codes for </td></tr>
<tr id="row_2_0_3_92_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00415.html" target="_self">neoapi.CGevIPConfigurationStatus</a></td><td class="desc">Reports the current IP configuration status </td></tr>
<tr id="row_2_0_3_93_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00419.html" target="_self">neoapi.CGevSupportedOptionSelector</a></td><td class="desc">Selects the GEV option to interrogate for existing support </td></tr>
<tr id="row_2_0_3_94_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00423.html" target="_self">neoapi.CHDRGainRatioSelector</a></td><td class="desc">Selects the gain ratio for HDR mode </td></tr>
<tr id="row_2_0_3_95_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00427.html" target="_self">neoapi.CHDRTonemappingCurvePresetSelector</a></td><td class="desc">Selects the predefined transfer curve for global tone-mapping of the calculated HDR image </td></tr>
<tr id="row_2_0_3_96_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00431.html" target="_self">neoapi.CImageCompressionJPEGFormatOption</a></td><td class="desc">When JPEG is selected as the compression format, a device might optionally offer better control over JPEG-specific options through this feature </td></tr>
<tr id="row_2_0_3_97_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00435.html" target="_self">neoapi.CImageCompressionMode</a></td><td class="desc">Enable a specific image compression mode as the base mode for image transfer </td></tr>
<tr id="row_2_0_3_98_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00439.html" target="_self">neoapi.CImageCompressionRateOption</a></td><td class="desc">Two rate controlling options are offered: fixed bit rate or fixed quality </td></tr>
<tr id="row_2_0_3_99_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00443.html" target="_self">neoapi.CInterfaceSpeedMode</a></td><td class="desc">Returns the interface speed mode as string </td></tr>
<tr id="row_2_0_3_100_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00455.html" target="_self">neoapi.CLineFormat</a></td><td class="desc">Controls the current electrical format of the selected physical input or output Line </td></tr>
<tr id="row_2_0_3_101_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00459.html" target="_self">neoapi.CLineMode</a></td><td class="desc">Controls if the physical Line is used to Input or Output a signal </td></tr>
<tr id="row_2_0_3_102_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00463.html" target="_self">neoapi.CLinePWMConfigurationMode</a></td><td class="desc">Enables the line PWM configuration mode </td></tr>
<tr id="row_2_0_3_103_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00467.html" target="_self">neoapi.CLinePWMMode</a></td><td class="desc">Enables the line PWM configuration mode </td></tr>
<tr id="row_2_0_3_104_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00471.html" target="_self">neoapi.CLineSelector</a></td><td class="desc">Selects the physical line (or pin) of the external device connector or the virtual line of the Transport Layer to configure </td></tr>
<tr id="row_2_0_3_105_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00475.html" target="_self">neoapi.CLineSource</a></td><td class="desc">Selects which internal acquisition or I/O source signal to output on the selected Line </td></tr>
<tr id="row_2_0_3_106_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00447.html" target="_self">neoapi.CLUTContent</a></td><td class="desc">Specifies the content of the selected LUT </td></tr>
<tr id="row_2_0_3_107_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00451.html" target="_self">neoapi.CLUTSelector</a></td><td class="desc">Selects which LUT to control </td></tr>
<tr id="row_2_0_3_108_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00479.html" target="_self">neoapi.CMemoryActivePart</a></td><td class="desc">Returns the active memory part to write the images in </td></tr>
<tr id="row_2_0_3_109_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00483.html" target="_self">neoapi.CMemoryMode</a></td><td class="desc">Controls the mode to use the memory </td></tr>
<tr id="row_2_0_3_110_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00487.html" target="_self">neoapi.CMemoryPartIncrementSource</a></td><td class="desc">Selects the source to switch the active memory part </td></tr>
<tr id="row_2_0_3_111_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00491.html" target="_self">neoapi.CMemoryPartMode</a></td><td class="desc">Selects the mode to use for the selected memory part </td></tr>
<tr id="row_2_0_3_112_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00495.html" target="_self">neoapi.CMemoryPartSelector</a></td><td class="desc">Selects on of the available memory parts </td></tr>
<tr id="row_2_0_3_113_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00499.html" target="_self">neoapi.COpticControllerSelector</a></td><td class="desc">Selects which optic controller to configure </td></tr>
<tr id="row_2_0_3_114_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00503.html" target="_self">neoapi.COpticControllerStatus</a></td><td class="desc">Reads the status of the optic controller </td></tr>
<tr id="row_2_0_3_115_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00507.html" target="_self">neoapi.CPartialScanEnabled</a></td><td class="desc">Enables the partial scan readout </td></tr>
<tr id="row_2_0_3_116_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00511.html" target="_self">neoapi.CPixelFormat</a></td><td class="desc">Format of the pixels provided by the device </td></tr>
<tr id="row_2_0_3_117_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00515.html" target="_self">neoapi.CPtpClockAccuracy</a></td><td class="desc">Indicates the expected accuracy of the device PTP clock when it is the grandmaster, or in the event it becomes the grandmaster </td></tr>
<tr id="row_2_0_3_118_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00519.html" target="_self">neoapi.CPtpClockOffsetMode</a></td><td class="desc">Sets the mode to handle PtpClockOffset for command PtpClockOffsetSet </td></tr>
<tr id="row_2_0_3_119_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00523.html" target="_self">neoapi.CPtpDriftOffsetMode</a></td><td class="desc">Sets the mode to handle PtpDriftOffset for command PtpDriftOffsetSet </td></tr>
<tr id="row_2_0_3_120_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00527.html" target="_self">neoapi.CPtpMode</a></td><td class="desc">Selects the PTP clock type the device will act as </td></tr>
<tr id="row_2_0_3_121_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00531.html" target="_self">neoapi.CPtpServoStatus</a></td><td class="desc">Returns the latched state of the clock servo </td></tr>
<tr id="row_2_0_3_122_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00535.html" target="_self">neoapi.CPtpStatus</a></td><td class="desc">Returns the latched state of the PTP clock </td></tr>
<tr id="row_2_0_3_123_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00539.html" target="_self">neoapi.CPtpSyncMessageIntervalStatus</a></td><td class="desc">Returns if the latched sync message interval from the PTP master clock is supported by the device </td></tr>
<tr id="row_2_0_3_124_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00543.html" target="_self">neoapi.CPtpTimestampOffsetMode</a></td><td class="desc">Sets the mode to handle PtpTimestampOffset for command PtpTimestampOffsetSet </td></tr>
<tr id="row_2_0_3_125_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00547.html" target="_self">neoapi.CReadOutBuffering</a></td><td class="desc">Selects the number of image buffers filled with data of sensor output </td></tr>
<tr id="row_2_0_3_126_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00551.html" target="_self">neoapi.CReadoutMode</a></td><td class="desc">Specifies the operation mode of the readout for the acquisition </td></tr>
<tr id="row_2_0_3_127_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00555.html" target="_self">neoapi.CRegionAcquisitionMode</a></td><td class="desc">Returns the acquisition mode of the regions </td></tr>
<tr id="row_2_0_3_128_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00559.html" target="_self">neoapi.CRegionConfigurationMode</a></td><td class="desc">Returns the configuration mode of the regions </td></tr>
<tr id="row_2_0_3_129_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00563.html" target="_self">neoapi.CRegionMode</a></td><td class="desc">Controls if the selected Region of interest is active and streaming </td></tr>
<tr id="row_2_0_3_130_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00567.html" target="_self">neoapi.CRegionSelector</a></td><td class="desc">Selects the Region of interest to control </td></tr>
<tr id="row_2_0_3_131_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00571.html" target="_self">neoapi.CRegionTransferMode</a></td><td class="desc">Returns the transfer mode of the regions </td></tr>
<tr id="row_2_0_3_132_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00579.html" target="_self">neoapi.CSensorADDigitization</a></td><td class="desc">Controls the sensors AD digitization in bits per pixels </td></tr>
<tr id="row_2_0_3_133_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00583.html" target="_self">neoapi.CSensorCutConfigurationMode</a></td><td class="desc">Controls if the sensor adjustment configuration mode is active </td></tr>
<tr id="row_2_0_3_134_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00587.html" target="_self">neoapi.CSensorDigitizationTaps</a></td><td class="desc">Number of digitized samples outputted simultaneously by the camera A/D conversion stage </td></tr>
<tr id="row_2_0_3_135_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00591.html" target="_self">neoapi.CSensorShutterMode</a></td><td class="desc">Specifies the shutter mode of the device </td></tr>
<tr id="row_2_0_3_136_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00595.html" target="_self">neoapi.CSensorTaps</a></td><td class="desc">Number of taps of the camera sensor </td></tr>
<tr id="row_2_0_3_137_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00599.html" target="_self">neoapi.CSequencerConfigurationMode</a></td><td class="desc">Controls if the sequencer configuration mode is active </td></tr>
<tr id="row_2_0_3_138_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00603.html" target="_self">neoapi.CSequencerFeatureSelector</a></td><td class="desc">Selects which sequencer features to control </td></tr>
<tr id="row_2_0_3_139_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00607.html" target="_self">neoapi.CSequencerMode</a></td><td class="desc">Controls if the sequencer mechanism is active </td></tr>
<tr id="row_2_0_3_140_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00611.html" target="_self">neoapi.CSequencerTriggerActivation</a></td><td class="desc">Specifies the activation mode of the sequencer trigger </td></tr>
<tr id="row_2_0_3_141_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00615.html" target="_self">neoapi.CSequencerTriggerSource</a></td><td class="desc">Specifies the internal signal or physical input line to use as the sequencer trigger source </td></tr>
<tr id="row_2_0_3_142_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00619.html" target="_self">neoapi.CShadingSelector</a></td><td class="desc">Selects the Shading Port Address </td></tr>
<tr id="row_2_0_3_143_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00623.html" target="_self">neoapi.CSharpeningMode</a></td><td class="desc">Selects the Sharpening Mode </td></tr>
<tr id="row_2_0_3_144_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00575.html" target="_self">neoapi.CSIControl</a></td><td class="desc">Controls the streaming operation </td></tr>
<tr id="row_2_0_3_145_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00627.html" target="_self">neoapi.CSourceID</a></td><td class="desc">Returns a unique Identifier value that correspond to the selected Source </td></tr>
<tr id="row_2_0_3_146_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00631.html" target="_self">neoapi.CSourceSelector</a></td><td class="desc">Selects the source to control </td></tr>
<tr id="row_2_0_3_147_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00635.html" target="_self">neoapi.CSwitchPortSelector</a></td><td class="desc">Selects the port for the port related features </td></tr>
<tr id="row_2_0_3_148_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00639.html" target="_self">neoapi.CTestPattern</a></td><td class="desc">Selects the type of test pattern that is generated by the device as image source </td></tr>
<tr id="row_2_0_3_149_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00643.html" target="_self">neoapi.CTestPatternGeneratorSelector</a></td><td class="desc">Selects which test pattern generator is controlled by the TestPattern feature </td></tr>
<tr id="row_2_0_3_150_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00647.html" target="_self">neoapi.CTestPayloadFormatMode</a></td><td class="desc">This feature allows setting a device in test mode and to output a specific payload format for validation of data streaming </td></tr>
<tr id="row_2_0_3_151_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00651.html" target="_self">neoapi.CTimerSelector</a></td><td class="desc">Selects which Timer to configure </td></tr>
<tr id="row_2_0_3_152_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00655.html" target="_self">neoapi.CTimerTriggerActivation</a></td><td class="desc">Selects the activation mode of the trigger to start the Timer </td></tr>
<tr id="row_2_0_3_153_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00659.html" target="_self">neoapi.CTimerTriggerSource</a></td><td class="desc">Selects the source of the trigger to start the Timer </td></tr>
<tr id="row_2_0_3_154_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00663.html" target="_self">neoapi.CTransferControlMode</a></td><td class="desc">Selects the control method for the transfers </td></tr>
<tr id="row_2_0_3_155_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00667.html" target="_self">neoapi.CTransferOperationMode</a></td><td class="desc">Selects the operation mode of the transfer </td></tr>
<tr id="row_2_0_3_156_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00671.html" target="_self">neoapi.CTransferSelector</a></td><td class="desc">Selects which stream transfers are currently controlled by the selected Transfer features </td></tr>
<tr id="row_2_0_3_157_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00675.html" target="_self">neoapi.CTransferStatusSelector</a></td><td class="desc">Selects which status of the transfer module to read </td></tr>
<tr id="row_2_0_3_158_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00679.html" target="_self">neoapi.CTriggerActivation</a></td><td class="desc">Specifies the activation mode of the trigger </td></tr>
<tr id="row_2_0_3_159_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00683.html" target="_self">neoapi.CTriggerMode</a></td><td class="desc">Controls if the selected trigger is active </td></tr>
<tr id="row_2_0_3_160_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00687.html" target="_self">neoapi.CTriggerOverlap</a></td><td class="desc">Specifies the type trigger overlap permitted with the previous frame or line </td></tr>
<tr id="row_2_0_3_161_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00691.html" target="_self">neoapi.CTriggerSelector</a></td><td class="desc">Selects the type of trigger to configure </td></tr>
<tr id="row_2_0_3_162_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00695.html" target="_self">neoapi.CTriggerSource</a></td><td class="desc">Specifies the internal signal or physical input Line to use as the trigger source </td></tr>
<tr id="row_2_0_3_163_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00699.html" target="_self">neoapi.CUserOutputSelector</a></td><td class="desc">Selects which bit of the User Output register will be set by UserOutputValue </td></tr>
<tr id="row_2_0_3_164_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00703.html" target="_self">neoapi.CUserSetDefault</a></td><td class="desc">Selects the feature User Set to load and make active by default when the device is reset </td></tr>
<tr id="row_2_0_3_165_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00707.html" target="_self">neoapi.CUserSetFeatureSelector</a></td><td class="desc">Selects which individual UserSet feature to control </td></tr>
<tr id="row_2_0_3_166_" class="even" style="display:none;"><td class="entry"><span style="width:64px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00711.html" target="_self">neoapi.CUserSetSelector</a></td><td class="desc">Selects the feature User Set to load, save or configure </td></tr>
<tr id="row_2_0_4_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00831.html" target="_self">neoapi.IntegerFeature</a></td><td class="desc">Class providing the 'IInteger' interface </td></tr>
<tr id="row_2_0_5_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00851.html" target="_self">neoapi.RegisterFeature</a></td><td class="desc">Base class providing the 'IRegister' interface </td></tr>
<tr id="row_2_0_6_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00835.html" target="_self">neoapi.StringFeature</a></td><td class="desc">Class providing the 'IString' interface </td></tr>
<tr id="row_2_1_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00879.html" target="_self">neoapi.BufferBase</a></td><td class="desc">Base class to derive from for use as user buffer </td></tr>
<tr id="row_2_2_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_2_2_" class="arrow" onclick="toggleFolder('2_2_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00859.html" target="_self">neoapi.CamBase</a></td><td class="desc">Base camera class from which other camera classes inherit functionality This class provides all methods to work with a camera </td></tr>
<tr id="row_2_2_0_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00863.html" target="_self">neoapi.Cam</a></td><td class="desc">Main camera class &mdash; connect, set features, retrieve images This class provides all methods to work with a camera </td></tr>
<tr id="row_2_3_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00883.html" target="_self">neoapi.CamInfo</a></td><td class="desc">Camera info container class which offers basic information about an available camera If <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">neoapi.CamInfoList</a> is called it will return a list of cameras, you can use the <a class="el" href="a00883.html" title="Camera info container class which offers basic information about an available camera If neoapi....">neoapi.CamInfo()</a> class to get information about a camera in this list </td></tr>
<tr id="row_2_4_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00891.html" target="_self">neoapi.CamInfoList</a></td><td class="desc">Provides a list of physically connected cameras available to be used/connected with neoAPI You can use this class if you don't know what camera(s) might be connected during program operation </td></tr>
<tr id="row_2_5_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00887.html" target="_self">neoapi.CamInfoListIterator</a></td><td class="desc">Provides iterator functionality for the <a class="el" href="a00891.html" title="Provides a list of physically connected cameras available to be used/connected with neoAPI You can us...">CamInfoList</a> </td></tr>
<tr id="row_2_6_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00795.html" target="_self">neoapi.ColorMatrix</a></td><td class="desc">Class for the Color Transformation Matrix This class provides methods to configure the Color Matrix for color cameras </td></tr>
<tr id="row_2_7_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00799.html" target="_self">neoapi.ConverterSettings</a></td><td class="desc"><a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> post processing settings This class provides methods to configure image conversions </td></tr>
<tr id="row_2_8_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00811.html" target="_self">neoapi.Feature</a></td><td class="desc">Provides access to camera features This class provides an easy way to work with camera features </td></tr>
<tr id="row_2_9_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00815.html" target="_self">neoapi.FeatureList</a></td><td class="desc">Provides list functionality for camera features </td></tr>
<tr id="row_2_10_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00819.html" target="_self">neoapi.FeatureListIterator</a></td><td class="desc">Provides iterator functionality for the <a class="el" href="a00815.html" title="Provides list functionality for camera features.">FeatureList</a> </td></tr>
<tr id="row_2_11_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00867.html" target="_self">neoapi.FeatureStack</a></td><td class="desc">A collection of camera features The <a class="el" href="a00867.html" title="A collection of camera features The FeatureStack provides you with the ability to write many GenICam ...">FeatureStack</a> provides you with the ability to write many GenICam <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> values quickly to a camera </td></tr>
<tr id="row_2_12_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_2_12_" class="arrow" onclick="toggleFolder('2_12_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00803.html" target="_self">neoapi.ImageInfo</a></td><td class="desc">Provides an object to get access to image properties even before streaming The <a class="el" href="a00803.html" title="Provides an object to get access to image properties even before streaming The ImageInfo object give ...">ImageInfo</a> object give access to basic image information like width and height </td></tr>
<tr id="row_2_12_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00855.html" target="_self">neoapi.Image</a></td><td class="desc">Provides an object to get access to image data and its properties The <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> object is used to retrieve, store and convert images obtained from a camera </td></tr>
<tr id="row_2_13_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00807.html" target="_self">neoapi.NeoEvent</a></td><td class="desc">Provides access to events This class provides an easy way to work with events </td></tr>
<tr id="row_2_14_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00875.html" target="_self">neoapi.NeoEventCallback</a></td><td class="desc">Event callback class to derive from an get event data </td></tr>
<tr id="row_2_15_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00871.html" target="_self">neoapi.NeoImageCallback</a></td><td class="desc"><a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> callback class to derive from an get image data </td></tr>
<tr id="row_2_16_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00899.html" target="_self">neoapi.NeoTrace</a></td><td class="desc">Trace class which offers the possibility to enable trace for different targets </td></tr>
<tr id="row_2_17_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="a00895.html" target="_self">neoapi.NeoTraceCallback</a></td><td class="desc">Trace callback class to derive from an get Trace messages </td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
