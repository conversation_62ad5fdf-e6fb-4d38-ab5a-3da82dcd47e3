<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.ImageInfo Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00803.html">ImageInfo</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00800.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.ImageInfo Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00086.html">Cam Interface Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides an object to get access to image properties even before streaming The <a class="el" href="a00803.html" title="Provides an object to get access to image properties even before streaming The ImageInfo object give ...">ImageInfo</a> object give access to basic image information like width and height.  
 <a href="a00803.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.ImageInfo:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00803.png" usemap="#neoapi.ImageInfo_map" alt=""/>
  <map id="neoapi.ImageInfo_map" name="neoapi.ImageInfo_map">
<area href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev..." alt="neoapi.Image" shape="rect" coords="0,112,108,136"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a8b9c3ab561324333874160df5007205f"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a8b9c3ab561324333874160df5007205f">__init__</a> (self, *args)</td></tr>
<tr class="memdesc:a8b9c3ab561324333874160df5007205f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#a8b9c3ab561324333874160df5007205f">More...</a><br /></td></tr>
<tr class="separator:a8b9c3ab561324333874160df5007205f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2d5b973f50c053e96ecc7edfe9da635"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ae2d5b973f50c053e96ecc7edfe9da635">GetHeight</a> (self)</td></tr>
<tr class="memdesc:ae2d5b973f50c053e96ecc7edfe9da635"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the height of the image in pixel, returns zero as long the image is not filled with data.  <a href="#ae2d5b973f50c053e96ecc7edfe9da635">More...</a><br /></td></tr>
<tr class="separator:ae2d5b973f50c053e96ecc7edfe9da635"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74ea98a5251ad92bcfc39dffb7e46ced"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a74ea98a5251ad92bcfc39dffb7e46ced">GetYOffset</a> (self)</td></tr>
<tr class="memdesc:a74ea98a5251ad92bcfc39dffb7e46ced"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the y offset in pixel.  <a href="#a74ea98a5251ad92bcfc39dffb7e46ced">More...</a><br /></td></tr>
<tr class="separator:a74ea98a5251ad92bcfc39dffb7e46ced"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa68a32ae42c15e79caace60e346a4ed6"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#aa68a32ae42c15e79caace60e346a4ed6">GetYPadding</a> (self)</td></tr>
<tr class="memdesc:aa68a32ae42c15e79caace60e346a4ed6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the number of extra bytes transmitted at the end of the image.  <a href="#aa68a32ae42c15e79caace60e346a4ed6">More...</a><br /></td></tr>
<tr class="separator:aa68a32ae42c15e79caace60e346a4ed6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae70432b723d9c832f6abb5cae6030bd5"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ae70432b723d9c832f6abb5cae6030bd5">GetWidth</a> (self)</td></tr>
<tr class="memdesc:ae70432b723d9c832f6abb5cae6030bd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the width of the image in pixel, returns zero as long the image is not filled with data.  <a href="#ae70432b723d9c832f6abb5cae6030bd5">More...</a><br /></td></tr>
<tr class="separator:ae70432b723d9c832f6abb5cae6030bd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a27e12c31a0a6257a2f34c5549412341a"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a27e12c31a0a6257a2f34c5549412341a">GetXOffset</a> (self)</td></tr>
<tr class="memdesc:a27e12c31a0a6257a2f34c5549412341a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the x offset in pixel.  <a href="#a27e12c31a0a6257a2f34c5549412341a">More...</a><br /></td></tr>
<tr class="separator:a27e12c31a0a6257a2f34c5549412341a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae264d4741c2e3768699e8305f4502bbe"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ae264d4741c2e3768699e8305f4502bbe">GetXPadding</a> (self)</td></tr>
<tr class="memdesc:ae264d4741c2e3768699e8305f4502bbe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the the number of extra bytes transmitted at the end of each line.  <a href="#ae264d4741c2e3768699e8305f4502bbe">More...</a><br /></td></tr>
<tr class="separator:ae264d4741c2e3768699e8305f4502bbe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66e5a1a4759118139478186645dcde8e"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a66e5a1a4759118139478186645dcde8e">GetPixelFormat</a> (self)</td></tr>
<tr class="memdesc:a66e5a1a4759118139478186645dcde8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the pixelformat of the image, returns an empty string as long the image is not filled with data The GenICam SFNC defines many different PixelFormats which are used to transfer image data from a camera such as RGB, YUV and BayerRG.  <a href="#a66e5a1a4759118139478186645dcde8e">More...</a><br /></td></tr>
<tr class="separator:a66e5a1a4759118139478186645dcde8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a4b22523f4c2591f18a92aa6672cb3b"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a2a4b22523f4c2591f18a92aa6672cb3b">GetSize</a> (self)</td></tr>
<tr class="memdesc:a2a4b22523f4c2591f18a92aa6672cb3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the whole size of the image in bytes.  <a href="#a2a4b22523f4c2591f18a92aa6672cb3b">More...</a><br /></td></tr>
<tr class="separator:a2a4b22523f4c2591f18a92aa6672cb3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4d5595e748e4efee67fb72bca5d12d51"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a4d5595e748e4efee67fb72bca5d12d51">GetGroupID</a> (self)</td></tr>
<tr class="memdesc:a4d5595e748e4efee67fb72bca5d12d51"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the group id of the image.  <a href="#a4d5595e748e4efee67fb72bca5d12d51">More...</a><br /></td></tr>
<tr class="separator:a4d5595e748e4efee67fb72bca5d12d51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c62cef40e061aa4b5648e15f6d45739"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a4c62cef40e061aa4b5648e15f6d45739">GetSourceID</a> (self)</td></tr>
<tr class="memdesc:a4c62cef40e061aa4b5648e15f6d45739"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the source id of the image.  <a href="#a4c62cef40e061aa4b5648e15f6d45739">More...</a><br /></td></tr>
<tr class="separator:a4c62cef40e061aa4b5648e15f6d45739"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1dd62ce238c06939313a60842098db1b"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a1dd62ce238c06939313a60842098db1b">GetRegionID</a> (self)</td></tr>
<tr class="memdesc:a1dd62ce238c06939313a60842098db1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the region id of the image.  <a href="#a1dd62ce238c06939313a60842098db1b">More...</a><br /></td></tr>
<tr class="separator:a1dd62ce238c06939313a60842098db1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a501b21913ccb8e7ad6836707f96511e9"><td class="memItemLeft" align="right" valign="top">&quot;NeoImageCompression&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a501b21913ccb8e7ad6836707f96511e9">GetCompression</a> (self)</td></tr>
<tr class="memdesc:a501b21913ccb8e7ad6836707f96511e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get compression method of the image.  <a href="#a501b21913ccb8e7ad6836707f96511e9">More...</a><br /></td></tr>
<tr class="separator:a501b21913ccb8e7ad6836707f96511e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0f6176d9aba067023fc07d32cd374d7"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ab0f6176d9aba067023fc07d32cd374d7">IsSegmentShared</a> (self)</td></tr>
<tr class="memdesc:ab0f6176d9aba067023fc07d32cd374d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Give information if the memory segment in a buffer is shared with other components.  <a href="#ab0f6176d9aba067023fc07d32cd374d7">More...</a><br /></td></tr>
<tr class="separator:ab0f6176d9aba067023fc07d32cd374d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0026a34eafc3b9412a7bc3cb58c4db6d"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a0026a34eafc3b9412a7bc3cb58c4db6d">GetSegmentOffset</a> (self)</td></tr>
<tr class="memdesc:a0026a34eafc3b9412a7bc3cb58c4db6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the image data offset related to the begin of the memory segment in the buffer.  <a href="#a0026a34eafc3b9412a7bc3cb58c4db6d">More...</a><br /></td></tr>
<tr class="separator:a0026a34eafc3b9412a7bc3cb58c4db6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00db0b522111679788d2624d5c4e32bb"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a00db0b522111679788d2624d5c4e32bb">GetSegmentSize</a> (self)</td></tr>
<tr class="memdesc:a00db0b522111679788d2624d5c4e32bb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the whole size of the memory segment in the buffer.  <a href="#a00db0b522111679788d2624d5c4e32bb">More...</a><br /></td></tr>
<tr class="separator:a00db0b522111679788d2624d5c4e32bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab40fa50855bca84a534c90af65c32230"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#ab40fa50855bca84a534c90af65c32230">GetSegmentIndex</a> (self)</td></tr>
<tr class="memdesc:ab40fa50855bca84a534c90af65c32230"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the index of the memory segment in the buffer.  <a href="#ab40fa50855bca84a534c90af65c32230">More...</a><br /></td></tr>
<tr class="separator:ab40fa50855bca84a534c90af65c32230"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a27ecc4d388b740e2749c167c4565dd79"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00803.html#a27ecc4d388b740e2749c167c4565dd79">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:a27ecc4d388b740e2749c167c4565dd79"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#a27ecc4d388b740e2749c167c4565dd79">More...</a><br /></td></tr>
<tr class="separator:a27ecc4d388b740e2749c167c4565dd79"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Provides an object to get access to image properties even before streaming The <a class="el" href="a00803.html" title="Provides an object to get access to image properties even before streaming The ImageInfo object give ...">ImageInfo</a> object give access to basic image information like width and height. </p>
<p>These informations can be obtained by the camera even before the streaming starts. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a8b9c3ab561324333874160df5007205f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b9c3ab561324333874160df5007205f">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.ImageInfo.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructor. </p>

<p>Reimplemented in <a class="el" href="a00855.html#a9c163a4c5d6c5372acfb297c2c62ff08">neoapi.Image</a>.</p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ae2d5b973f50c053e96ecc7edfe9da635"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae2d5b973f50c053e96ecc7edfe9da635">&#9670;&nbsp;</a></span>GetHeight()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetHeight </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the height of the image in pixel, returns zero as long the image is not filled with data. </p>
<dl class="section return"><dt>Returns</dt><dd>The height of the image in pixel. // TODO: kepp after convert </dd></dl>

</div>
</div>
<a id="a74ea98a5251ad92bcfc39dffb7e46ced"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a74ea98a5251ad92bcfc39dffb7e46ced">&#9670;&nbsp;</a></span>GetYOffset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetYOffset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the y offset in pixel. </p>
<dl class="section return"><dt>Returns</dt><dd>The y offset in pixel // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="aa68a32ae42c15e79caace60e346a4ed6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa68a32ae42c15e79caace60e346a4ed6">&#9670;&nbsp;</a></span>GetYPadding()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetYPadding </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the number of extra bytes transmitted at the end of the image. </p>
<dl class="section return"><dt>Returns</dt><dd>The y padding of the image in byte. // TODO: 0 after convert </dd></dl>

</div>
</div>
<a id="ae70432b723d9c832f6abb5cae6030bd5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae70432b723d9c832f6abb5cae6030bd5">&#9670;&nbsp;</a></span>GetWidth()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetWidth </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the width of the image in pixel, returns zero as long the image is not filled with data. </p>
<dl class="section return"><dt>Returns</dt><dd>The width of the image in pixel. // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="a27e12c31a0a6257a2f34c5549412341a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27e12c31a0a6257a2f34c5549412341a">&#9670;&nbsp;</a></span>GetXOffset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetXOffset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the x offset in pixel. </p>
<dl class="section return"><dt>Returns</dt><dd>The x offset in pixel // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="ae264d4741c2e3768699e8305f4502bbe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae264d4741c2e3768699e8305f4502bbe">&#9670;&nbsp;</a></span>GetXPadding()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetXPadding </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the the number of extra bytes transmitted at the end of each line. </p>
<dl class="section return"><dt>Returns</dt><dd>The x padding of the image in byte // TODO: 0 after convert </dd></dl>

</div>
</div>
<a id="a66e5a1a4759118139478186645dcde8e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66e5a1a4759118139478186645dcde8e">&#9670;&nbsp;</a></span>GetPixelFormat()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.ImageInfo.GetPixelFormat </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the pixelformat of the image, returns an empty string as long the image is not filled with data The GenICam SFNC defines many different PixelFormats which are used to transfer image data from a camera such as RGB, YUV and BayerRG. </p>
<p>Knowledge about it is essential so the image can be converted to a format needed to process it </p><dl class="section note"><dt>Note</dt><dd>More information about the GenICam SFNC (Standard Feature Naming Convention) can be found in the GenICam SFNC Document on the <a href="https://www.emva.org/wp-content/uploads/GenICam_SFNC_v2_4.pdf">EMVA Website</a> </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>The PixelFormat of the <a class="el" href="a00855.html" title="Provides an object to get access to image data and its properties The Image object is used to retriev...">Image</a> // TODO: ask image after convert </dd></dl>

</div>
</div>
<a id="a2a4b22523f4c2591f18a92aa6672cb3b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a4b22523f4c2591f18a92aa6672cb3b">&#9670;&nbsp;</a></span>GetSize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSize </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the whole size of the image in bytes. </p>
<dl class="section return"><dt>Returns</dt><dd>the image size in bytes // TODO: ask image after convert </dd></dl>

</div>
</div>
<a id="a4d5595e748e4efee67fb72bca5d12d51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4d5595e748e4efee67fb72bca5d12d51">&#9670;&nbsp;</a></span>GetGroupID()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetGroupID </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the group id of the image. </p>
<dl class="section note"><dt>Note</dt><dd>in case of a non GenDC buffer this returns always 0 </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the group id // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="a4c62cef40e061aa4b5648e15f6d45739"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4c62cef40e061aa4b5648e15f6d45739">&#9670;&nbsp;</a></span>GetSourceID()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSourceID </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the source id of the image. </p>
<dl class="section note"><dt>Note</dt><dd>in case of a non GenDC buffer this returns always 0 </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the source id // TODO: kepp after convert </dd></dl>

</div>
</div>
<a id="a1dd62ce238c06939313a60842098db1b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1dd62ce238c06939313a60842098db1b">&#9670;&nbsp;</a></span>GetRegionID()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetRegionID </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the region id of the image. </p>
<dl class="section note"><dt>Note</dt><dd>in case of a non GenDC buffer this returns always 0 </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the region id // TODO: keep after convert </dd></dl>

</div>
</div>
<a id="a501b21913ccb8e7ad6836707f96511e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a501b21913ccb8e7ad6836707f96511e9">&#9670;&nbsp;</a></span>GetCompression()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;NeoImageCompression&quot; neoapi.ImageInfo.GetCompression </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get compression method of the image. </p>
<dl class="section return"><dt>Returns</dt><dd>The compression method // TODO: RAW after convert </dd></dl>

</div>
</div>
<a id="ab0f6176d9aba067023fc07d32cd374d7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab0f6176d9aba067023fc07d32cd374d7">&#9670;&nbsp;</a></span>IsSegmentShared()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.ImageInfo.IsSegmentShared </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Give information if the memory segment in a buffer is shared with other components. </p>
<dl class="section return"><dt>Returns</dt><dd>true if segment is shared with other components, otherwise false // TODO: refresh value after convert </dd></dl>

</div>
</div>
<a id="a0026a34eafc3b9412a7bc3cb58c4db6d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0026a34eafc3b9412a7bc3cb58c4db6d">&#9670;&nbsp;</a></span>GetSegmentOffset()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSegmentOffset </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the image data offset related to the begin of the memory segment in the buffer. </p>
<dl class="section return"><dt>Returns</dt><dd>the offset of the image data // TODO: refresh value after convert </dd></dl>

</div>
</div>
<a id="a00db0b522111679788d2624d5c4e32bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a00db0b522111679788d2624d5c4e32bb">&#9670;&nbsp;</a></span>GetSegmentSize()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSegmentSize </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the whole size of the memory segment in the buffer. </p>
<dl class="section note"><dt>Note</dt><dd>when the segment only contains this image the result is identical to <a class="el" href="a00803.html#a2a4b22523f4c2591f18a92aa6672cb3b" title="Get the whole size of the image in bytes.">GetSize()</a> </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the memory size in bytes // TODO: refresh value after convert </dd></dl>

</div>
</div>
<a id="ab40fa50855bca84a534c90af65c32230"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab40fa50855bca84a534c90af65c32230">&#9670;&nbsp;</a></span>GetSegmentIndex()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.ImageInfo.GetSegmentIndex </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the index of the memory segment in the buffer. </p>
<dl class="section return"><dt>Returns</dt><dd>the segment index // TODO: 0 after convert </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a27ecc4d388b740e2749c167c4565dd79"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a27ecc4d388b740e2749c167c4565dd79">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.ImageInfo.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
