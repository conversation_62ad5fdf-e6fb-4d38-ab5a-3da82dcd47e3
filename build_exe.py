#!/usr/bin/env python3
"""
Build-Skript für Baumer Temperatur Monitor EXE
Erstellt eine standalone EXE-Datei mit PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """Installiert PyInstaller falls nicht vorhanden"""
    try:
        import PyInstaller
        print("✓ PyInstaller bereits installiert")
        return True
    except ImportError:
        print("PyInstaller nicht gefunden, installiere...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✓ PyInstaller erfolgreich installiert")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ Fehler beim Installieren von PyInstaller: {e}")
            return False

def create_spec_file():
    """Erstellt PyInstaller .spec Datei"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ethernet5_main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('src', 'src'),
        ('logs', 'logs'),
        ('data', 'data'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtWidgets', 
        'PyQt5.QtGui',
        'sqlite3',
        'requests',
        'socket',
        'threading',
        'logging',
        'datetime',
        'json',
        'xml.etree.ElementTree',
        'concurrent.futures',
        'ipaddress',
        'subprocess',
        'platform'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'opencv-python',
        'Pillow',
        'pyqtgraph'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='BaumerTemperaturMonitor',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='baumer_icon.ico'
)
'''
    
    with open('baumer_monitor.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content.strip())
    
    print("✓ .spec Datei erstellt")

def create_icon():
    """Erstellt ein einfaches Icon (falls nicht vorhanden)"""
    if not os.path.exists('baumer_icon.ico'):
        print("ℹ Kein Icon gefunden, verwende Standard-Icon")
        return False
    return True

def build_exe():
    """Erstellt die EXE-Datei"""
    print("\n=== ERSTELLE EXE-DATEI ===")
    
    try:
        # Bereinige alte Builds
        if os.path.exists('dist'):
            shutil.rmtree('dist')
            print("✓ Alte dist/ bereinigt")
            
        if os.path.exists('build'):
            shutil.rmtree('build')
            print("✓ Alte build/ bereinigt")
        
        # Erstelle EXE
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed", 
            "--name=BaumerTemperaturMonitor",
            "--add-data=src;src",
            "--hidden-import=PyQt5.QtCore",
            "--hidden-import=PyQt5.QtWidgets",
            "--hidden-import=PyQt5.QtGui",
            "--exclude-module=matplotlib",
            "--exclude-module=numpy", 
            "--exclude-module=opencv-python",
            "--exclude-module=Pillow",
            "--exclude-module=pyqtgraph",
            "ethernet5_main.py"
        ]
        
        print("Starte PyInstaller...")
        print(f"Befehl: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ EXE erfolgreich erstellt!")
            
            # Prüfe ob EXE existiert
            exe_path = Path("dist/BaumerTemperaturMonitor.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ EXE-Datei: {exe_path}")
                print(f"✓ Größe: {size_mb:.1f} MB")
                return True
            else:
                print("✗ EXE-Datei nicht gefunden")
                return False
        else:
            print("✗ PyInstaller Fehler:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Fehler beim Erstellen der EXE: {e}")
        return False

def create_portable_package():
    """Erstellt portables Paket"""
    print("\n=== ERSTELLE PORTABLES PAKET ===")
    
    try:
        # Erstelle Paket-Ordner
        package_dir = Path("BaumerTemperaturMonitor_Portable")
        if package_dir.exists():
            shutil.rmtree(package_dir)
        
        package_dir.mkdir()
        
        # Kopiere EXE
        exe_source = Path("dist/BaumerTemperaturMonitor.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, package_dir / "BaumerTemperaturMonitor.exe")
            print("✓ EXE kopiert")
        
        # Erstelle Ordner-Struktur
        (package_dir / "logs").mkdir()
        (package_dir / "data").mkdir()
        
        # Erstelle README
        readme_content = """
# Baumer Temperatur Monitor - Portable Version

## Installation:
Keine Installation erforderlich! Einfach BaumerTemperaturMonitor.exe starten.

## Verwendung:
1. BaumerTemperaturMonitor.exe doppelklicken
2. "Alle bekannten Kameras testen" klicken
3. Erreichbare Kameras werden angezeigt

## Bekannte Kamera-IPs:
- ***************
- **************  
- **************
- **************

## Systemanforderungen:
- Windows 7/8/10/11
- Ethernet 5 Verbindung zu Baumer-Kameras
- Keine Python-Installation erforderlich
- Keine Baumer-Treiber erforderlich

## Ordner:
- logs/ - System-Logs
- data/ - Temperatur-Datenbank

## Version: 1.0.5
## Erstellt: """ + str(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        
        with open(package_dir / "README.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✓ README erstellt")
        
        # Erstelle Batch-Datei
        batch_content = """@echo off
echo Starte Baumer Temperatur Monitor...
BaumerTemperaturMonitor.exe
pause"""
        
        with open(package_dir / "Start.bat", 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print("✓ Start.bat erstellt")
        
        print(f"✓ Portables Paket erstellt: {package_dir}")
        return True
        
    except Exception as e:
        print(f"✗ Fehler beim Erstellen des Pakets: {e}")
        return False

def main():
    """Hauptfunktion"""
    print("Baumer Temperatur Monitor - EXE Builder")
    print("=" * 50)
    
    from datetime import datetime
    print(f"Build-Zeit: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Prüfe Voraussetzungen
    if not os.path.exists('ethernet5_main.py'):
        print("✗ ethernet5_main.py nicht gefunden!")
        return False
    
    if not os.path.exists('src'):
        print("✗ src/ Ordner nicht gefunden!")
        return False
    
    print("✓ Quelldateien gefunden")
    
    # Installiere PyInstaller
    if not install_pyinstaller():
        return False
    
    # Erstelle Icon (optional)
    create_icon()
    
    # Erstelle EXE
    if not build_exe():
        return False
    
    # Erstelle portables Paket
    if not create_portable_package():
        return False
    
    print("\n" + "=" * 50)
    print("🎉 BUILD ERFOLGREICH!")
    print("=" * 50)
    print("📁 Portable Version: BaumerTemperaturMonitor_Portable/")
    print("📄 EXE-Datei: BaumerTemperaturMonitor_Portable/BaumerTemperaturMonitor.exe")
    print("📋 Anleitung: BaumerTemperaturMonitor_Portable/README.txt")
    print("🚀 Start: BaumerTemperaturMonitor_Portable/Start.bat")
    print("\nDie EXE kann auf jedem Windows-PC ohne Python ausgeführt werden!")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\nDrücken Sie Enter zum Beenden...")
    sys.exit(0 if success else 1)
