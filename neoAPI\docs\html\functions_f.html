<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_f"></a>- f -</h3><ul>
<li>f()
: <a class="el" href="a00863.html#a177b60302bc6c88dfe82a8fc938f3885">neoapi.Cam</a>
</li>
<li>FileAccessBuffer()
: <a class="el" href="a00763.html#af5f920a86f3f9feff0b581b547731652">neoapi.FeatureAccess</a>
</li>
<li>FileAccessLength()
: <a class="el" href="a00763.html#a593ca60d9b696985dcbfe57e29b30535">neoapi.FeatureAccess</a>
</li>
<li>FileAccessOffset()
: <a class="el" href="a00763.html#a0a3d461f94252767f8b64a6944fbc44d">neoapi.FeatureAccess</a>
</li>
<li>FileOpenMode()
: <a class="el" href="a00763.html#a9319b682ee57bacdb943baa08bea24fe">neoapi.FeatureAccess</a>
</li>
<li>FileOperationExecute()
: <a class="el" href="a00763.html#a2646e239b47e4c513020a4857a847367">neoapi.FeatureAccess</a>
</li>
<li>FileOperationResult()
: <a class="el" href="a00763.html#a096bb6ff80fad46041d67073d06f38f6">neoapi.FeatureAccess</a>
</li>
<li>FileOperationSelector()
: <a class="el" href="a00763.html#a58ed7768de6c85357abf969afd052032">neoapi.FeatureAccess</a>
</li>
<li>FileOperationStatus()
: <a class="el" href="a00763.html#af0337c8de83d19c12bfaebe4a37a6266">neoapi.FeatureAccess</a>
</li>
<li>FileSelector()
: <a class="el" href="a00763.html#a52bc609a832cde726d7708f5c426d5fe">neoapi.FeatureAccess</a>
</li>
<li>FileSize()
: <a class="el" href="a00763.html#a6f77f64d40471a401ffb6efc522cf623">neoapi.FeatureAccess</a>
</li>
<li>find()
: <a class="el" href="a00815.html#ae9bf38228fee9d4ce2d9696a8dc1435f">neoapi.FeatureList</a>
</li>
<li>FixedPatternNoiseCorrection()
: <a class="el" href="a00763.html#ac2523ff9c093fcf037c14317336fc68f">neoapi.FeatureAccess</a>
</li>
<li>FocalLength()
: <a class="el" href="a00763.html#a93e695f3b2e4bdc642e97edab3d4abcc">neoapi.FeatureAccess</a>
</li>
<li>FocalLengthInitialize()
: <a class="el" href="a00763.html#ad03c521b51123d6ead56e918c5e17895">neoapi.FeatureAccess</a>
</li>
<li>FocalLengthStatus()
: <a class="el" href="a00763.html#aaee92a24afdc13193b25b9c545413f13">neoapi.FeatureAccess</a>
</li>
<li>FocalPower()
: <a class="el" href="a00763.html#aefb677a66147827b39215e5b95c572a9">neoapi.FeatureAccess</a>
</li>
<li>FocusInitialize()
: <a class="el" href="a00763.html#afddd6e3f9f036f47a928f63909a7b1f8">neoapi.FeatureAccess</a>
</li>
<li>FocusStatus()
: <a class="el" href="a00763.html#a12ae37d194f5d321650dea7fcebf81e7">neoapi.FeatureAccess</a>
</li>
<li>FocusStepper()
: <a class="el" href="a00763.html#ae58dac185fdea5ec23876d23fb4058c2">neoapi.FeatureAccess</a>
</li>
<li>FrameCounter()
: <a class="el" href="a00763.html#a3b422578203d973228c401bc0f5c63a6">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
