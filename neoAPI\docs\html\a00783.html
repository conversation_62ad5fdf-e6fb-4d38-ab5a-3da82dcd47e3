<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.NoImageBufferException Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00783.html">NoImageBufferException</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00780.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.NoImageBufferException Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00089.html">Exception Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Requesting an image while holding all available image resources.  
 <a href="a00783.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.NoImageBufferException:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00783.png" usemap="#neoapi.NoImageBufferException_map" alt=""/>
  <map id="neoapi.NoImageBufferException_map" name="neoapi.NoImageBufferException_map">
<area href="a00767.html" title="Base neoAPI Exception class." alt="neoapi.NeoException" shape="rect" coords="0,56,193,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a924db8b0d7117a591f1e62910da91eb5"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00783.html#a924db8b0d7117a591f1e62910da91eb5">__init__</a> (self, &quot;str&quot; description)</td></tr>
<tr class="memdesc:a924db8b0d7117a591f1e62910da91eb5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#a924db8b0d7117a591f1e62910da91eb5">More...</a><br /></td></tr>
<tr class="separator:a924db8b0d7117a591f1e62910da91eb5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f22221fc865d68ef678f5a6bc90e244"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00767.html#a7f22221fc865d68ef678f5a6bc90e244">GetDescription</a> (self)</td></tr>
<tr class="memdesc:a7f22221fc865d68ef678f5a6bc90e244"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the Description of the thrown Exception.  <a href="#a7f22221fc865d68ef678f5a6bc90e244">More...</a><br /></td></tr>
<tr class="separator:a7f22221fc865d68ef678f5a6bc90e244"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a2bcfdb13c5e54769019926e1600910aa"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00783.html#a2bcfdb13c5e54769019926e1600910aa">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:a2bcfdb13c5e54769019926e1600910aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#a2bcfdb13c5e54769019926e1600910aa">More...</a><br /></td></tr>
<tr class="separator:a2bcfdb13c5e54769019926e1600910aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Requesting an image while holding all available image resources. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a924db8b0d7117a591f1e62910da91eb5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a924db8b0d7117a591f1e62910da91eb5">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.NoImageBufferException.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;str&quot;&#160;</td>
          <td class="paramname"><em>description</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructor. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">description</td><td>A description </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a7f22221fc865d68ef678f5a6bc90e244"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f22221fc865d68ef678f5a6bc90e244">&#9670;&nbsp;</a></span>GetDescription()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.NeoException.GetDescription </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the Description of the thrown Exception. </p>
<dl class="section return"><dt>Returns</dt><dd>The Description of the thrown Exception </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a2bcfdb13c5e54769019926e1600910aa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2bcfdb13c5e54769019926e1600910aa">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.NoImageBufferException.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
