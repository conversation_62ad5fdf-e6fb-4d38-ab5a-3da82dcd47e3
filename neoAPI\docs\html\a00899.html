<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.NeoTrace Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00899.html">NeoTrace</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00896.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.NeoTrace Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Trace class which offers the possibility to enable trace for different targets.  
 <a href="a00899.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.NeoTrace:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00899.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:abd79543934ba49197c679d21cb211d41"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#abd79543934ba49197c679d21cb211d41">__init__</a> (self)</td></tr>
<tr class="memdesc:abd79543934ba49197c679d21cb211d41"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#abd79543934ba49197c679d21cb211d41">More...</a><br /></td></tr>
<tr class="separator:abd79543934ba49197c679d21cb211d41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c052e6ff01de7ce082a7bbdd91ad333"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#a7c052e6ff01de7ce082a7bbdd91ad333">Info</a> (self, msg, mod=&quot;mod&quot;, fct=&quot;fct&quot;, obj=None)</td></tr>
<tr class="memdesc:a7c052e6ff01de7ce082a7bbdd91ad333"><td class="mdescLeft">&#160;</td><td class="mdescRight">Trace an info message.  <a href="#a7c052e6ff01de7ce082a7bbdd91ad333">More...</a><br /></td></tr>
<tr class="separator:a7c052e6ff01de7ce082a7bbdd91ad333"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14c913e26b892bbaae5b93c6ea5ce723"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#a14c913e26b892bbaae5b93c6ea5ce723">Warning</a> (self, msg, mod=&quot;mod&quot;, fct=&quot;fct&quot;, obj=None)</td></tr>
<tr class="memdesc:a14c913e26b892bbaae5b93c6ea5ce723"><td class="mdescLeft">&#160;</td><td class="mdescRight">Trace a warning message.  <a href="#a14c913e26b892bbaae5b93c6ea5ce723">More...</a><br /></td></tr>
<tr class="separator:a14c913e26b892bbaae5b93c6ea5ce723"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae46b6f5d72ae3c718fb262090378253c"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#ae46b6f5d72ae3c718fb262090378253c">Error</a> (self, msg, mod=&quot;mod&quot;, fct=&quot;fct&quot;, obj=None)</td></tr>
<tr class="memdesc:ae46b6f5d72ae3c718fb262090378253c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Trace an error message.  <a href="#ae46b6f5d72ae3c718fb262090378253c">More...</a><br /></td></tr>
<tr class="separator:ae46b6f5d72ae3c718fb262090378253c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5bce17ee5dd648f0fc381c65b0e5e97c"><td class="memItemLeft" align="right" valign="top">&quot;None&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#a5bce17ee5dd648f0fc381c65b0e5e97c">EnableLogfile</a> (self, &quot;str&quot; filename)</td></tr>
<tr class="memdesc:a5bce17ee5dd648f0fc381c65b0e5e97c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enable trace output to file.  <a href="#a5bce17ee5dd648f0fc381c65b0e5e97c">More...</a><br /></td></tr>
<tr class="separator:a5bce17ee5dd648f0fc381c65b0e5e97c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2bfbc8f0848cb91c577680ad03ddf5da"><td class="memItemLeft" align="right" valign="top">&quot;None&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#a2bfbc8f0848cb91c577680ad03ddf5da">DisableLogfile</a> (self)</td></tr>
<tr class="memdesc:a2bfbc8f0848cb91c577680ad03ddf5da"><td class="mdescLeft">&#160;</td><td class="mdescRight">Disable trace output to file.  <a href="#a2bfbc8f0848cb91c577680ad03ddf5da">More...</a><br /></td></tr>
<tr class="separator:a2bfbc8f0848cb91c577680ad03ddf5da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41c17e313b0679816038997211f18da6"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#a41c17e313b0679816038997211f18da6">EnableLogCallback</a> (self, callback)</td></tr>
<tr class="memdesc:a41c17e313b0679816038997211f18da6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Enable trace callback.  <a href="#a41c17e313b0679816038997211f18da6">More...</a><br /></td></tr>
<tr class="separator:a41c17e313b0679816038997211f18da6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9fe4ab1821cfecda000f37802097614"><td class="memItemLeft" align="right" valign="top">&quot;None&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#aa9fe4ab1821cfecda000f37802097614">DisableLogCallback</a> (self)</td></tr>
<tr class="memdesc:aa9fe4ab1821cfecda000f37802097614"><td class="mdescLeft">&#160;</td><td class="mdescRight">Disable trace callback.  <a href="#aa9fe4ab1821cfecda000f37802097614">More...</a><br /></td></tr>
<tr class="separator:aa9fe4ab1821cfecda000f37802097614"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf653fbda7506a4664adcfb0be61b473"><td class="memItemLeft" align="right" valign="top">&quot;None&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#adf653fbda7506a4664adcfb0be61b473">SetSeverity</a> (self, &quot;NeoTraceSeverity&quot; severity)</td></tr>
<tr class="memdesc:adf653fbda7506a4664adcfb0be61b473"><td class="mdescLeft">&#160;</td><td class="mdescRight">Filter trace messages by setting a minimum severity.  <a href="#adf653fbda7506a4664adcfb0be61b473">More...</a><br /></td></tr>
<tr class="separator:adf653fbda7506a4664adcfb0be61b473"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:ab6337840e87e1ffc6d4db4358ccb476e"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00899.html#ab6337840e87e1ffc6d4db4358ccb476e">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:ab6337840e87e1ffc6d4db4358ccb476e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#ab6337840e87e1ffc6d4db4358ccb476e">More...</a><br /></td></tr>
<tr class="separator:ab6337840e87e1ffc6d4db4358ccb476e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Trace class which offers the possibility to enable trace for different targets. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="abd79543934ba49197c679d21cb211d41"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abd79543934ba49197c679d21cb211d41">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.NeoTrace.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructor. </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a7c052e6ff01de7ce082a7bbdd91ad333"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7c052e6ff01de7ce082a7bbdd91ad333">&#9670;&nbsp;</a></span>Info()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.NeoTrace.Info </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>msg</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>mod</em> = <code>&quot;mod&quot;</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>fct</em> = <code>&quot;fct&quot;</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>obj</em> = <code>None</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Trace an info message. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">msg</td><td>The message to trace. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mod</td><td>The module or unit name to trace. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fct</td><td>The function or device name to trace. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">object</td><td>The object to trace. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a14c913e26b892bbaae5b93c6ea5ce723"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a14c913e26b892bbaae5b93c6ea5ce723">&#9670;&nbsp;</a></span>Warning()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.NeoTrace.Warning </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>msg</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>mod</em> = <code>&quot;mod&quot;</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>fct</em> = <code>&quot;fct&quot;</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>obj</em> = <code>None</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Trace a warning message. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">msg</td><td>The message to trace. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mod</td><td>The module or unit name to trace. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fct</td><td>The function or device name to trace. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">object</td><td>The object to trace. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ae46b6f5d72ae3c718fb262090378253c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae46b6f5d72ae3c718fb262090378253c">&#9670;&nbsp;</a></span>Error()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.NeoTrace.Error </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>msg</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>mod</em> = <code>&quot;mod&quot;</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>fct</em> = <code>&quot;fct&quot;</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>obj</em> = <code>None</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Trace an error message. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">msg</td><td>The message to trace. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">mod</td><td>The module or unit name to trace. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">fct</td><td>The function or device name to trace. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">object</td><td>The object to trace. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a5bce17ee5dd648f0fc381c65b0e5e97c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5bce17ee5dd648f0fc381c65b0e5e97c">&#9670;&nbsp;</a></span>EnableLogfile()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;None&quot; neoapi.NeoTrace.EnableLogfile </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;str&quot;&#160;</td>
          <td class="paramname"><em>filename</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Enable trace output to file. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">filename</td><td>The filename(including folder) to store the trace. </td></tr>
  </table>
  </dd>
</dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00771.html" title="No camera connected Exception.">NotConnectedException</a></td><td>No device connected. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a2bfbc8f0848cb91c577680ad03ddf5da"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2bfbc8f0848cb91c577680ad03ddf5da">&#9670;&nbsp;</a></span>DisableLogfile()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;None&quot; neoapi.NeoTrace.DisableLogfile </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Disable trace output to file. </p>

</div>
</div>
<a id="a41c17e313b0679816038997211f18da6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41c17e313b0679816038997211f18da6">&#9670;&nbsp;</a></span>EnableLogCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.NeoTrace.EnableLogCallback </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>callback</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Enable trace callback. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">callback</td><td>The LogCallback method of the given object will be called for every trace message. Auto created callback class. Auto created callback methode. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa9fe4ab1821cfecda000f37802097614"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa9fe4ab1821cfecda000f37802097614">&#9670;&nbsp;</a></span>DisableLogCallback()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;None&quot; neoapi.NeoTrace.DisableLogCallback </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Disable trace callback. </p>
<dl class="section return"><dt>Returns</dt><dd>None </dd></dl>

</div>
</div>
<a id="adf653fbda7506a4664adcfb0be61b473"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adf653fbda7506a4664adcfb0be61b473">&#9670;&nbsp;</a></span>SetSeverity()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;None&quot; neoapi.NeoTrace.SetSeverity </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;NeoTraceSeverity&quot;&#160;</td>
          <td class="paramname"><em>severity</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Filter trace messages by setting a minimum severity. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">severity</td><td>The target severity. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="ab6337840e87e1ffc6d4db4358ccb476e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab6337840e87e1ffc6d4db4358ccb476e">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.NeoTrace.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
