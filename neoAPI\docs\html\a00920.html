<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Working with Camera Features using neoAPI</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Working with Camera Features using neoAPI </div>  </div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#autotoc_md54">Working with camera features</a><ul><li class="level2"><a href="#autotoc_md55">How do camera features work?</a></li>
<li class="level2"><a href="#autotoc_md56">Feature types</a></li>
<li class="level2"><a href="#autotoc_md57">Is the feature implemented and available?</a></li>
<li class="level2"><a href="#autotoc_md58">Is the feature available for reading or writing?</a></li>
<li class="level2"><a href="#autotoc_md59">Read and write feature values</a><ul><li class="level3"><a href="#autotoc_md60">Automatic adjustment of feature values</a></li>
<li class="level3"><a href="#autotoc_md61">When do changed feature settings become active?</a></li>
</ul>
</li>
<li class="level2"><a href="#autotoc_md62">Execute ICommand features</a></li>
<li class="level2"><a href="#autotoc_md63">Using IEnumeration features</a></li>
<li class="level2"><a href="#autotoc_md64">Further feature attributes</a></li>
<li class="level2"><a href="#autotoc_md65">Alternative access to features</a></li>
<li class="level2"><a href="#autotoc_md66">Feature stacking — change many features at once</a></li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><h1><a class="anchor" id="autotoc_md54"></a>
Working with camera features</h1>
<p>Every camera project requires configuring at least some basic settings (called features) of the camera. GenICam compatible devices use the SFNC naming convention to standardize how features are named <a class="el" href="a00925.html">see here</a> for details about the machine vision standards.</p>
<p>With neoAPI the configuration of GenICam cameras is really easy as the feature-names and feature-attributes are implemented as class members or attributes so your IDE can provide you with an auto-complete while you type.</p>
<p>The methods to access the features are generated from the current SFNC document and includes all features included there and any Baumer specific features. In case you are missing a feature (i.e. the autocomplete doesn't offer the feature) please check the section “Alternative feature access”</p>
<h2><a class="anchor" id="autotoc_md55"></a>
How do camera features work?</h2>
<p>Available features of the camera are provided during the camera connect. Here the neoAPI reads which features are available and what properties they have. After the camera is connected you can read or write features the camera provides. The availability and access-status of a feature are handled by the camera. Their status can change at any point for example depending on the values of other features. So it is important to keep in mind that a feature which was readable or writable a while ago is not necessarily writable now.</p>
<h2><a class="anchor" id="autotoc_md56"></a>
Feature types</h2>
<p>The GenICam describes 6 different feature types:</p>
<ul>
<li>IInteger, IFloat, IBool, IString — Those are simple features, the take a value which will have some influence on the camera.</li>
<li>IEnumeration — Those features are used in two ways. Firstly, like in “PixelFormat” they can behave like an enum, the feature can only take a set amount of values. The second use case for IEnumeration features are “Selector features” here the feature is used to switch one or more other features. A good example is the “GainSelector” which selects which color-channel the “Gain” feature represents for reading or writing a value.</li>
<li>ICommand — These are features which will execute something on the camera, good examples are the “AcquisitionStart” or the “TriggerSoftware” features.</li>
</ul>
<h2><a class="anchor" id="autotoc_md57"></a>
Is the feature implemented and available?</h2>
<p>The Standard Feature Naming Convention (SFNC) describes many features a camera can implement, however not all features are implemented on all cameras. That means, before you can access a feature, you need to check if the feature is available on a camera using the <code><a class="el" href="a00859.html#a374c5e3200cab8d7464bc907811ba035" title="Check if a feature is supported by a camera, see Cam.f for a more convenient way to access features.">neoapi.Cam.HasFeature()</a></code> method.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    if camera.HasFeature(ExposureTime):  # check if this feature is implemented</div><div class="line">        print(&quot;The ExposureTime feature is implemented&quot;)</div></div><!-- fragment --> <div class="caption">Example: Check if a feature is implemented</div><p>Some features can change depending on other features set on the camera. So for example the availability of feature ExposureTime depends on the value of feature ExposureAuto. ExposureTime will only be available if the ExposureAuto is switched off. You can use the <code><a class="el" href="a00811.html#acd8890c4c4e5d90ff9aabd069c1e9af4" title="Indicates whether the Feature object is available &quot;Not available&quot; or False is equivalent to the acces...">neoapi.Feature.IsAvailable()</a></code> method to get the current status.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    if camera.f.ExposureTime.IsAvailable():  # check if this feature is available</div><div class="line">        print(&quot;The ExposureTime feature is available&quot;)</div></div><!-- fragment --> <div class="caption">Example: Check if a feature is accessible</div><h2><a class="anchor" id="autotoc_md58"></a>
Is the feature available for reading or writing?</h2>
<p>To check if a feature is readable or writable the methods <code><a class="el" href="a00811.html#a6012ab5cf826fbf4e47147e56f2c00cd" title="Indicates that the Feature object is readable.">neoapi.Feature.IsReadable()</a></code> and <code><a class="el" href="a00811.html#a0c3ef92f53d96f95324e130c56ecb1bb" title="Indicates if a Feature object is writeable.">neoapi.Feature.IsWritable()</a></code> are implemented. Some features will never be writable such as the device serial number or the model name. Other features might change their access status depending on the value of another feature. As an example, if the feature “ExposureAuto” is switched on, the feature “ExposureTime” will be set to read-only thereby ensuring nobody does interfere with the AutoExposure algorithm.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    if camera.f.DeviceSerialNumber.IsAvailable() and camera.f.DeviceSerialNumber.IsReadable():</div><div class="line">        print(camera.f.DeviceSerialNumber.value)</div><div class="line">    else:</div><div class="line">        print(&quot;DeviceSerialNumber is not readable&quot;)</div><div class="line">    if (camera.f.DeviceSerialNumber.IsAvailable() and camera.f.DeviceSerialNumber.IsWritable()) :</div><div class="line">        camera.f.DeviceSerialNumber.value = &quot;111111&quot;</div><div class="line">    else :</div><div class="line">        print(&quot;DeviceSerialNumber is read-only&quot;)</div></div><!-- fragment --> <div class="caption">Example: Check if a feature is readable or writable</div><h2><a class="anchor" id="autotoc_md59"></a>
Read and write feature values</h2>
<p>Once the feature is checked for availability and read/write-ability reading and writing a value is trivial. The <code>neoapi.Feature.Get()</code> or <code>neoapi.Feature.Set()</code> methods can be omitted as seen in the example below.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    print(camera.f.ExposureTime.value)   # print the current exposure time</div><div class="line">    print(camera.f.ExposureTime.Get())   # equivalent to above</div><div class="line"></div><div class="line">    camera.f.ExposureTime.value = 20000  # set exposure time to 20000</div><div class="line">    camera.f.ExposureTime.Set(20000)     # equivalent to above</div></div><!-- fragment --> <div class="caption">Example: Read and write features</div><h3><a class="anchor" id="autotoc_md60"></a>
Automatic adjustment of feature values</h3>
<p>Features can have a minimum, maximum or increment for the allowed values set. This means it is not possible to set the full range of numbers but only a subset. A good example are the features to set a region or area of interest (ROI/AOI). The features “Width”, “Height”, “OffsetX” and “OffsetY” can typically only take values which are multiples of 2, 4, 8 or 16 and which have a maximum depending on the sensor of the camera. If an increment is set for a feature, the neoAPI will automatically choose the closest possible value in case the given value could not be written to a feature. If you rather prefer to get an exception in such a case, you can disable this behavior using the <code><a class="el" href="a00863.html#a3299965f197821f2de6f63b43eefd251" title="With AdjustFeatureValueMode enabled feature values will be checked an adjusted where necessary Some F...">neoapi.Cam.SetAdjustFeatureValueMode()</a></code> method.</p>
<div class="fragment"><div class="line">import sys</div><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    camera.f.Width.value = 201               # will set the value to the closest possible value</div><div class="line">    print(camera.f.Width.value)              # will print the set value</div><div class="line">    camera.f.Width.value = 100000            # will set the value to the maximum sensor size</div><div class="line">    print(camera.f.Width.value)              # will print the set value depending on sensor size</div><div class="line"></div><div class="line">    camera.SetAdjustFeatureValueMode(False)  # switch off the automated value adjustment</div><div class="line">    try:</div><div class="line">        camera.f.Width = 201                 # now the same call will throw an exception</div><div class="line">        camera.f.Width = 100000              # this would also throw an exception</div><div class="line">    except (neoapi.FeatureAccessException) as exc:</div><div class="line">        print(sys.exc_info())</div><div class="line">        print(&quot;FeatureAccessException: &quot;, exc)</div></div><!-- fragment --> <div class="caption">Example: Working with the automatic feature value adjustment</div><p><br />
</p>
<blockquote class="doxtable">
<p>Some features do not have an increment but are rounded inside of a camera so writing 2.2 to the Gain feature will result in a value of 2.1877 read back from the camera. </p>
</blockquote>
<h3><a class="anchor" id="autotoc_md61"></a>
When do changed feature settings become active?</h3>
<p>If you change the value of a feature, this might not be honored by the next image you retrieve from the camera. This can be for two reasons. Firstly, the changed feature must be written to the camera before start of the exposure, otherwise the image will be taken using the old settings. Secondly, because there is a buffer queue on the host it might be that the next image you request is already in a buffer and therefore will also be taken with older settings.</p>
<p>The neoAPI offers the <code><a class="el" href="a00863.html#ab14f94f0300b589b9a502a592c1cf7d5" title="In synchronous mode the acquisition will restart for every feature change, to ensure new values will ...">neoapi.Cam.SetSynchronFeatureMode()</a></code> to change that behavior. If set to true, the neoAPI will ensure that a feature change is reflected in the next image. However, this will slow down the time it takes from changing a feature until the next image is ready.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    camera.f.ExposureTime.value = 50      # set a small exposure time</div><div class="line">    camera.f.Gain.value = 1               # set the gain to 1</div><div class="line">    camera.SetSynchronFeatureMode(False)  # switch syncronous mode off</div><div class="line">    camera.f.Gain.value = 4               # set a different gain</div><div class="line">    img = camera.GetImage()               # might not come with the expected gain of 4</div><div class="line">    img.Save(&quot;test1&quot;)</div><div class="line">    img = camera.GetImage()               # will come with the expected gain of 4</div><div class="line">    img.Save(&quot;test2&quot;)</div></div><!-- fragment --> <div class="caption">Example: Feature synchronization</div><h2><a class="anchor" id="autotoc_md62"></a>
Execute ICommand features</h2>
<p>One special category are features which execute something on the camera. Examples are the software trigger or the acquisition start/stop features.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    camera.f.TriggerMode.value = neoapi.TriggerMode_On  # switch camera to trigger mode</div><div class="line">    f = camera.f.TriggerSoftware</div><div class="line">    f.Execute()                                         # execute the software trigger</div></div><!-- fragment --> <div class="caption">Example: Working with an executable feature</div><h2><a class="anchor" id="autotoc_md63"></a>
Using IEnumeration features</h2>
<p>Another special case are enumeration features. They can be two different things, either a “selector feature” such as the “GainSelector” which is used to change other features on the camera, or an enumeration like the “PixelFormat” which can only be assigned a set number of values.</p>
<p>For writing a value to IEnumeration features like the “PixelFormat”, the possible values for the enumeration are implemented as an enum class with the same name as the feature.</p>
<p>The “PixelFormat” is a good example for an IEnumeration feature. The example below shows, how to loop through the possible values using the <code><a class="el" href="a00811.html#a251c3b500b730799f531cebe1e4ab652" title="Get a list of all possible values of the Feature object Only valid for interface type &#39;IEnumeration&#39;.">neoapi.Feature.GetEnumValueList()</a></code> method. With the call <code>GetEnumValueList().IsReadable("RGB8")</code> we check if a value is allowed on the camera.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    f = camera.f.PixelFormat</div><div class="line">    # loop through all elements and print the name of the possible enums</div><div class="line">    for a in f.GetEnumValueList():</div><div class="line">        print(a.GetName())</div><div class="line">    if camera.f.PixelFormat.GetEnumValueList().IsReadable(&quot;RGB8&quot;):  # check if the camera supports the format</div><div class="line">        f.value = neoapi.PixelFormat_RGB8                           # set the pixel format to RGB8</div><div class="line">    elif f.GetEnumValueList().IsReadable(&quot;Mono8&quot;):</div><div class="line">        f.value = neoapi.PixelFormat_Mono8</div><div class="line">    print(f.value, &quot;; &quot;, f.GetString())                             # print the pixel format value</div></div><!-- fragment --> <div class="caption">Example: Working with the PixelFormat feature (enumeration feature)</div><p>The next example shows how a selector feature is used to access a set of similar features. A camera can have more than one GPIO line, each of the lines has a set of attributes. To access them for one of the GPIO lines we set the LineSelector to this line and can than read or write the attributes of this line.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    f = camera.f.LineSelector</div><div class="line">    print(f.IsSelector())  # Is this feature a selector for another feature</div><div class="line">    f.value = neoapi.LineSelector_Line0</div><div class="line"></div><div class="line">    # loop through GetEnumValueList() to get all possible values of the selector feature</div><div class="line">    for a in f.GetEnumValueList():</div><div class="line">        print(a.GetName())</div><div class="line">    print(&quot;-----------&quot;)</div><div class="line">    # loop through GetSelectedFeatureList() to get all selected features and their values for the current selection (Line0)</div><div class="line">    for a in f.GetSelectedFeatureList():</div><div class="line">        print(a.GetDisplayName(), &quot;: &quot;, a.GetString())</div></div><!-- fragment --> <div class="caption">Example: Working with a selector feature (GainSelector)</div><h2><a class="anchor" id="autotoc_md64"></a>
Further feature attributes</h2>
<p>Each feature has a set of methods which should make it easy to use them for calculations or in a GUI. The available methods do depend on the type of the feature.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    # IString feature methods</div><div class="line">    f = camera.f.DeviceUserID</div><div class="line">    f.SetString(&quot;MyCamera&quot;)         # set the Device User ID to &quot;MyCamera&quot;</div><div class="line">    print(f.GetString())            # get the value back as a String</div><div class="line">    print(f.GetMaxStringLength())   # get the maximum length of the string</div><div class="line"></div><div class="line">    # IInteger, IFloat or IBool feature methods</div><div class="line">    f = camera.f.ExposureTime</div><div class="line">    f.Set(10000)                    # set the value</div><div class="line">    print(f.Get())                  # get the value</div><div class="line">    print(f.GetMin())               # get the smallest possible value</div><div class="line">    print(f.GetMax())               # get the largest possible value</div><div class="line">    print(f.GetInc())               # get the increment to the next possible value</div><div class="line">    print(f.GetUnit())              # get a string with the unit to be displayed</div><div class="line">    print(f.GetRepresentation())    # How should the data be presented (linear, logarithmic, number)</div><div class="line">    print(f.GetPrecision())         # the display precision (only for IDouble features)</div><div class="line"></div><div class="line">    # feature methods available for all features</div><div class="line">    print(f.GetString())            # get the value back as a String</div><div class="line"></div><div class="line">    print(f.GetName())              # get the Name of the feature (CamelCase)</div><div class="line">    print(f.GetDisplayName())       # get the name as it should be displayed (with spaces)</div><div class="line">    print(f.GetToolTip())           # get a short description of the feature</div><div class="line">    print(f.GetDescription())       # get a longer description of the feature</div><div class="line">    print(f.GetExtension())         # get additional vendor specific description</div><div class="line"></div><div class="line">    print(f.IsAvailable())          # the feature methods for access control</div><div class="line">    print(f.IsImplemented())</div><div class="line">    print(f.IsReadable())</div><div class="line">    print(f.IsWritable())</div><div class="line"></div><div class="line">    print(f.GetInterface())         # get the GenICam interface (type) of the current feature</div><div class="line">    print(f.GetVisibility())        # get the GenICam recommended level (beginner, expert, guru)</div></div><!-- fragment --> <div class="caption">Example: Working with a additional feature methods</div><h2><a class="anchor" id="autotoc_md65"></a>
Alternative access to features</h2>
<p>In rare cases you might need a feature which is not accessible through the <code>camera.f.FeatureName</code> methods. If this is the case, an alternative way to access those features is shown in the example below.</p>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    # first check if the feature exists and is accessible</div><div class="line">    if camera.HasFeature(&quot;MySpecialFeature&quot;) and camera.IsWritable(&quot;MySpecialFeature&quot;):</div><div class="line">        f = camera.GetFeature(&quot;MySpecialFeature&quot;)</div><div class="line">        f.SetDouble(3.12)   # write to the feature</div><div class="line">        print(f.GetDisplayName(), &quot;: &quot;, f.GetString())</div><div class="line">    else:</div><div class="line">        print(&quot;Feature is not available&quot;)</div></div><!-- fragment --> <div class="caption">Example: Alternative feature access</div><h2><a class="anchor" id="autotoc_md66"></a>
Feature stacking — change many features at once</h2>
<p>In some cases it might be necessary to change camera features very quickly between two images. In those cases the feature stacking might help you. The feature stacking reduces the round-trips necessary if each feature is written separately to the camera by preparing and then sending many features in one go to the camera.</p>
<blockquote class="doxtable">
<p>Attention:</p>
<p>Enumeration feature values must provided as string without the prefixed feature name e.g. "neoapi.ExposureAuto_Off" becomes to "Off". If the predefined values e.g. "neoapi.ExposureAuto_Off" are used directly the behaviour is undefined. </p>
</blockquote>
<div class="fragment"><div class="line">import neoapi</div><div class="line">camera = neoapi.Cam()</div><div class="line">camera.Connect()</div><div class="line">if camera.IsConnected():</div><div class="line">    fs = neoapi.FeatureStack()</div><div class="line">    fs.Add(&quot;ExposureAuto&quot;, &quot;Off&quot;)   # enumeration feature values must provided as string</div><div class="line">    fs.Add(&quot;TriggerMode&quot;, &quot;On&quot;)</div><div class="line">    fs.Add(&quot;TriggerSource&quot;, &quot;Software&quot;)</div><div class="line">    fs.Add(&quot;ExposureTime&quot;, 20000)   # add features to the stack</div><div class="line">    fs.Add(&quot;Gain&quot;, 1.2)</div><div class="line">    fs.Add(&quot;Width&quot;, 300)</div><div class="line">    fs.Add(&quot;Height&quot;, 300)</div><div class="line">    fs.Add(&quot;OffsetX&quot;, 800)</div><div class="line">    fs.Add(&quot;OffsetY&quot;, 100)</div><div class="line"></div><div class="line">    camera.WriteFeatureStack(fs)    # write all features in one go to the camera</div></div><!-- fragment --> <div class="caption">Example: Using the feature stacking</div> </div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
