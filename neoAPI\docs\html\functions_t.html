<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_t"></a>- t -</h3><ul>
<li>TestEventGenerate()
: <a class="el" href="a00763.html#a29c8d028aabec2d1abe5eddd1f380bd0">neoapi.FeatureAccess</a>
</li>
<li>TestPattern()
: <a class="el" href="a00763.html#adc6b898943e75e3ec78f6bb5b1b3a5f1">neoapi.FeatureAccess</a>
</li>
<li>TestPatternGeneratorSelector()
: <a class="el" href="a00763.html#ab7825a5b8c140a8f6c92cf9798e71108">neoapi.FeatureAccess</a>
</li>
<li>TestPayloadFormatMode()
: <a class="el" href="a00763.html#a6ec8f5c5731b37a18d6294fb5b6fb34a">neoapi.FeatureAccess</a>
</li>
<li>TestPendingAck()
: <a class="el" href="a00763.html#a93cf8b870c9722c2067ca1a47e5922d5">neoapi.FeatureAccess</a>
</li>
<li>thisown
: <a class="el" href="a00823.html#abcc68ade07b0fc007aec9d75826894a1">neoapi.BaseFeature</a>
, <a class="el" href="a00839.html#a3aea12878d77a3a75f291606312c9015">neoapi.BoolFeature</a>
, <a class="el" href="a00879.html#abe1d87748dfad8ccb23e6212d2c7a317">neoapi.BufferBase</a>
, <a class="el" href="a00863.html#ae50022e50933bacdcc0438eb00ccedd1">neoapi.Cam</a>
, <a class="el" href="a00859.html#a95029eaabd281118ff8ac42556f056aa">neoapi.CamBase</a>
, <a class="el" href="a00883.html#a58c833eb8d0490277872e0ed78e29534">neoapi.CamInfo</a>
, <a class="el" href="a00891.html#a25550ab6a6f4ba63a32edcc933f79f84">neoapi.CamInfoList</a>
, <a class="el" href="a00887.html#aef55df7d9561f35e7c1a62f219281af5">neoapi.CamInfoListIterator</a>
, <a class="el" href="a00795.html#aec5adc25ece28bd03a426c7cbe6afdc3">neoapi.ColorMatrix</a>
, <a class="el" href="a00843.html#a12a23a6052d3db77cf8a595f2808d0a6">neoapi.CommandFeature</a>
, <a class="el" href="a00799.html#a23439d8bb5e3d744ef10cd429a4d5386">neoapi.ConverterSettings</a>
, <a class="el" href="a00827.html#a3b70f3e1fcf20a96821fb7fc402d9739">neoapi.DoubleFeature</a>
, <a class="el" href="a00847.html#a8c0a659fcc6f0de09b7d42530b8aaad8">neoapi.EnumerationFeature</a>
, <a class="el" href="a00811.html#a7433fbf6a07e587ba3dc7bedff7e420f">neoapi.Feature</a>
, <a class="el" href="a00779.html#a678614581e88470152a17278b5cd2f2c">neoapi.FeatureAccessException</a>
, <a class="el" href="a00815.html#a278ea658fcdca5c884842bb3a9aa1a1e">neoapi.FeatureList</a>
, <a class="el" href="a00819.html#ab69f38e9151262e4e5c502c71c1f0785">neoapi.FeatureListIterator</a>
, <a class="el" href="a00867.html#a16d9bc334da35fa5b8b19cb356d2490c">neoapi.FeatureStack</a>
, <a class="el" href="a00787.html#a2001de07a6fb41f5c56762b88b07ceb7">neoapi.FileAccessException</a>
, <a class="el" href="a00855.html#aea66aea381f903a8ae7cc7d4f6685822">neoapi.Image</a>
, <a class="el" href="a00803.html#a27ecc4d388b740e2749c167c4565dd79">neoapi.ImageInfo</a>
, <a class="el" href="a00831.html#a3d0d2071e26d6630ce6858238b7f1a31">neoapi.IntegerFeature</a>
, <a class="el" href="a00791.html#a4a4cd6f73dbf9c425966b3a4b70911ff">neoapi.InvalidArgumentException</a>
, <a class="el" href="a00807.html#a3ffddffba67360a348acc72c67975b38">neoapi.NeoEvent</a>
, <a class="el" href="a00875.html#a82cbdf3cbd7793ce09c678b92f0d56e5">neoapi.NeoEventCallback</a>
, <a class="el" href="a00767.html#a738404193c4bc1ddb62513c2a089cda3">neoapi.NeoException</a>
, <a class="el" href="a00871.html#aad52fa4dcae410558454f375dadbb59c">neoapi.NeoImageCallback</a>
, <a class="el" href="a00899.html#ab6337840e87e1ffc6d4db4358ccb476e">neoapi.NeoTrace</a>
, <a class="el" href="a00895.html#a0a74b8ac7d2342c855383c57f827f9e1">neoapi.NeoTraceCallback</a>
, <a class="el" href="a00775.html#a25223830ec605bb73ad9a4be5c14df2e">neoapi.NoAccessException</a>
, <a class="el" href="a00783.html#a2bcfdb13c5e54769019926e1600910aa">neoapi.NoImageBufferException</a>
, <a class="el" href="a00771.html#a4f0dfe38dc0301c36bdc59b26efc7364">neoapi.NotConnectedException</a>
, <a class="el" href="a00851.html#a748adab3c3d3ad1cad6b9b6181d4a44d">neoapi.RegisterFeature</a>
, <a class="el" href="a00835.html#a9c4fef880e95ee8aad4ca09340cdf77e">neoapi.StringFeature</a>
</li>
<li>TimerDelay()
: <a class="el" href="a00763.html#a706058e0066b87686315f2e6b1720591">neoapi.FeatureAccess</a>
</li>
<li>TimerDuration()
: <a class="el" href="a00763.html#a2a5508796fd37491c23a5e3d61b298ee">neoapi.FeatureAccess</a>
</li>
<li>TimerSelector()
: <a class="el" href="a00763.html#a140cdf6847ac58392bb647f0a5f9b965">neoapi.FeatureAccess</a>
</li>
<li>TimerTriggerActivation()
: <a class="el" href="a00763.html#ad75b2994a0ec53aeb424f8f3c6422035">neoapi.FeatureAccess</a>
</li>
<li>TimerTriggerSource()
: <a class="el" href="a00763.html#ae276cbaa6c626e0427693636ac0a85e4">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatch()
: <a class="el" href="a00763.html#a016da032da455b4431107f46a1d838a4">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValue()
: <a class="el" href="a00763.html#a4d70222e7e48c16a259a193be8f9af75">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpDays()
: <a class="el" href="a00763.html#a8f0819127784a6a155a2333f14558161">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpHours()
: <a class="el" href="a00763.html#a092ef6bf1653c54743359bb9a7054182">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpMinutes()
: <a class="el" href="a00763.html#ac4ebe952008e94e52810d8b0d557e10e">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpNanoseconds()
: <a class="el" href="a00763.html#a073790309da81f57e6234e9f4d42f77a">neoapi.FeatureAccess</a>
</li>
<li>TimestampLatchValuePtpSeconds()
: <a class="el" href="a00763.html#a712c59b3c84f41de58cd8998509e8ca3">neoapi.FeatureAccess</a>
</li>
<li>TimestampReset()
: <a class="el" href="a00763.html#a1c9935183470d6a040e17e52d695c8e7">neoapi.FeatureAccess</a>
</li>
<li>TLParamsLocked()
: <a class="el" href="a00763.html#a65e9eab9274311fadcba56d71c91ee30">neoapi.FeatureAccess</a>
</li>
<li>TransferControlMode()
: <a class="el" href="a00763.html#a49adeb4384f64015911e6201f30c9c63">neoapi.FeatureAccess</a>
</li>
<li>TransferOperationMode()
: <a class="el" href="a00763.html#a26a409268242d8ff3d564321e6d02c66">neoapi.FeatureAccess</a>
</li>
<li>TransferSelector()
: <a class="el" href="a00763.html#a56cef45ce54a3f9922af90ca9f27b5c9">neoapi.FeatureAccess</a>
</li>
<li>TransferStart()
: <a class="el" href="a00763.html#ab9d87bfa98941cf7f541edbac41212c9">neoapi.FeatureAccess</a>
</li>
<li>TransferStatus()
: <a class="el" href="a00763.html#ae1dfce4b6ae0a3495d140e8693639082">neoapi.FeatureAccess</a>
</li>
<li>TransferStatusSelector()
: <a class="el" href="a00763.html#a7be19377191b72d0b521a9f34cddb7ec">neoapi.FeatureAccess</a>
</li>
<li>TransferStop()
: <a class="el" href="a00763.html#a7bf751ad1f852f415e0ffa00f970d584">neoapi.FeatureAccess</a>
</li>
<li>TriggerActivation()
: <a class="el" href="a00763.html#a4fe0ebb6a3deddfd8a3b6bd511d8b6bf">neoapi.FeatureAccess</a>
</li>
<li>TriggerCounterLatch()
: <a class="el" href="a00763.html#a3d7ed37032c84f413d1f529946f26131">neoapi.FeatureAccess</a>
</li>
<li>TriggerCounterLatchValue()
: <a class="el" href="a00763.html#a0a5755c95ce0e0b3b6ebf121613b3684">neoapi.FeatureAccess</a>
</li>
<li>TriggerCounterReset()
: <a class="el" href="a00763.html#af8789c9c36b14d196d0659037e92c041">neoapi.FeatureAccess</a>
</li>
<li>TriggerDelay()
: <a class="el" href="a00763.html#af8b488294009d5dee67aebb01f8df73b">neoapi.FeatureAccess</a>
</li>
<li>TriggerEventTest()
: <a class="el" href="a00763.html#a6283f00329c2e6f780486366af8140cf">neoapi.FeatureAccess</a>
</li>
<li>TriggerMode()
: <a class="el" href="a00763.html#a4bd16b2979ce53224fdbc4f207e63916">neoapi.FeatureAccess</a>
</li>
<li>TriggerOverlap()
: <a class="el" href="a00763.html#aaf308978b92e00608ee338ba063ca832">neoapi.FeatureAccess</a>
</li>
<li>TriggerSelector()
: <a class="el" href="a00763.html#a577bbd602d63d7b8af8b76d79350bef8">neoapi.FeatureAccess</a>
</li>
<li>TriggerSoftware()
: <a class="el" href="a00763.html#a5b43d9d90f277e7b915123a6962794ff">neoapi.FeatureAccess</a>
</li>
<li>TriggerSource()
: <a class="el" href="a00763.html#a46fba3e81121fb46d0a6ffa30f57e155">neoapi.FeatureAccess</a>
</li>
<li>TxByteDelay()
: <a class="el" href="a00763.html#aebf5a7be0d96f8487b1b266c7d2a80cc">neoapi.FeatureAccess</a>
</li>
<li>TxByteDelayNormalized()
: <a class="el" href="a00763.html#afb5518132bcc02a835b394960458ed24">neoapi.FeatureAccess</a>
</li>
<li>TxCommandoLength()
: <a class="el" href="a00763.html#a7c94a038e12a4fad5df5dd96af204ed1">neoapi.FeatureAccess</a>
</li>
<li>TxDiscardedMessages()
: <a class="el" href="a00763.html#aea972219521528e7bd904ec350f981b7">neoapi.FeatureAccess</a>
</li>
<li>TxFiFo()
: <a class="el" href="a00763.html#a520b61e2ab6a0b94d8db1834ae8cbea5">neoapi.FeatureAccess</a>
</li>
<li>TxFiFoFreeBufferCount()
: <a class="el" href="a00763.html#ad0109ec42fea7929e6f5c3425d3db589">neoapi.FeatureAccess</a>
</li>
<li>TxMessageDelay()
: <a class="el" href="a00763.html#a7bd404c0e9fb5fbf20624a8f17e05832">neoapi.FeatureAccess</a>
</li>
<li>TxMessageDelayNormalized()
: <a class="el" href="a00763.html#abed07b9837cd31ff77c8d95b2809ae73">neoapi.FeatureAccess</a>
</li>
<li>TxRetryCount()
: <a class="el" href="a00763.html#a160a52ff4552a7cfee378fc1c0f4a386">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
