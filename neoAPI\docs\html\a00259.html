<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.CDeviceCharacterSet Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00259.html">CDeviceCharacterSet</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00256.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.CDeviceCharacterSet Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00087.html">GenICam Feature Interface</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Character set used by the strings of the device.  
 <a href="a00259.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.CDeviceCharacterSet:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00259.png" usemap="#neoapi.CDeviceCharacterSet_map" alt=""/>
  <map id="neoapi.CDeviceCharacterSet_map" name="neoapi.CDeviceCharacterSet_map">
<area href="a00847.html" title="Base class providing the &#39;IEnumeration&#39; interface." alt="neoapi.EnumerationFeature" shape="rect" coords="0,112,178,136"/>
<area href="a00823.html" title="Base Feature class providing the interface to be used independent of Feature data-type." alt="neoapi.BaseFeature" shape="rect" coords="0,56,178,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab788ecf67e5d1382147d4866b2190358"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00259.html#ab788ecf67e5d1382147d4866b2190358">__init__</a> (self, *args)</td></tr>
<tr class="memdesc:ab788ecf67e5d1382147d4866b2190358"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#ab788ecf67e5d1382147d4866b2190358">More...</a><br /></td></tr>
<tr class="separator:ab788ecf67e5d1382147d4866b2190358"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b5ebdf7fa97f2908efdb3cd24ab209f"><td class="memItemLeft" align="right" valign="top">&quot;CDeviceCharacterSet&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00259.html#a0b5ebdf7fa97f2908efdb3cd24ab209f">Set</a> (self, 'DeviceCharacterSet' <a class="el" href="a00259.html#ac3ab7c8661ab9ae29355bef96d28f4f9">value</a>)</td></tr>
<tr class="memdesc:a0b5ebdf7fa97f2908efdb3cd24ab209f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the current value.  <a href="#a0b5ebdf7fa97f2908efdb3cd24ab209f">More...</a><br /></td></tr>
<tr class="separator:a0b5ebdf7fa97f2908efdb3cd24ab209f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a120b125f26edab4712a3b6665105c75c"><td class="memItemLeft" align="right" valign="top">&quot;DeviceCharacterSet&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00259.html#a120b125f26edab4712a3b6665105c75c">Get</a> (self)</td></tr>
<tr class="memdesc:a120b125f26edab4712a3b6665105c75c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the current value.  <a href="#a120b125f26edab4712a3b6665105c75c">More...</a><br /></td></tr>
<tr class="separator:a120b125f26edab4712a3b6665105c75c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb4392642486f932de046b64d6114df6"><td class="memItemLeft" align="right" valign="top">&quot;FeatureList&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#aeb4392642486f932de046b64d6114df6">GetEnumValueList</a> (self)</td></tr>
<tr class="memdesc:aeb4392642486f932de046b64d6114df6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a list of all possible values of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object Only valid for interface type 'IEnumeration'.  <a href="#aeb4392642486f932de046b64d6114df6">More...</a><br /></td></tr>
<tr class="separator:aeb4392642486f932de046b64d6114df6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a452fdbc062b37f3915e0bcfb0aebf3b0"><td class="memItemLeft" align="right" valign="top">&quot;int&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a452fdbc062b37f3915e0bcfb0aebf3b0">GetInt</a> (self)</td></tr>
<tr class="memdesc:a452fdbc062b37f3915e0bcfb0aebf3b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object as integer.  <a href="#a452fdbc062b37f3915e0bcfb0aebf3b0">More...</a><br /></td></tr>
<tr class="separator:a452fdbc062b37f3915e0bcfb0aebf3b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bcb8c3b80260c8d0cde108eaf365be4"><td class="memItemLeft" align="right" valign="top">&quot;EnumerationFeature&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a0bcb8c3b80260c8d0cde108eaf365be4">SetInt</a> (self, &quot;int&quot; value)</td></tr>
<tr class="memdesc:a0bcb8c3b80260c8d0cde108eaf365be4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Writes an integer value to the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#a0bcb8c3b80260c8d0cde108eaf365be4">More...</a><br /></td></tr>
<tr class="separator:a0bcb8c3b80260c8d0cde108eaf365be4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4a3e7aba4519e49f8f32afe4ba29869"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#ac4a3e7aba4519e49f8f32afe4ba29869">GetString</a> (self)</td></tr>
<tr class="memdesc:ac4a3e7aba4519e49f8f32afe4ba29869"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> as a string.  <a href="#ac4a3e7aba4519e49f8f32afe4ba29869">More...</a><br /></td></tr>
<tr class="separator:ac4a3e7aba4519e49f8f32afe4ba29869"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82a850e2d931254434f9a8e454fcb536"><td class="memItemLeft" align="right" valign="top">&quot;EnumerationFeature&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a82a850e2d931254434f9a8e454fcb536">SetString</a> (self, &quot;str&quot; value)</td></tr>
<tr class="memdesc:a82a850e2d931254434f9a8e454fcb536"><td class="mdescLeft">&#160;</td><td class="mdescRight">Write the value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> as a string.  <a href="#a82a850e2d931254434f9a8e454fcb536">More...</a><br /></td></tr>
<tr class="separator:a82a850e2d931254434f9a8e454fcb536"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab78a7830a2eeff9e0c82484d8f538962"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#ab78a7830a2eeff9e0c82484d8f538962">IsSelector</a> (self)</td></tr>
<tr class="memdesc:ab78a7830a2eeff9e0c82484d8f538962"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicates whether the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is a selector A selector is a possibility to define feature dependencies.  <a href="#ab78a7830a2eeff9e0c82484d8f538962">More...</a><br /></td></tr>
<tr class="separator:ab78a7830a2eeff9e0c82484d8f538962"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2302f54f1139c38b3de67ae9b8606afb"><td class="memItemLeft" align="right" valign="top">&quot;FeatureList&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a2302f54f1139c38b3de67ae9b8606afb">GetSelectedFeatureList</a> (self)</td></tr>
<tr class="memdesc:a2302f54f1139c38b3de67ae9b8606afb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a list of features that depend on this selector feature.  <a href="#a2302f54f1139c38b3de67ae9b8606afb">More...</a><br /></td></tr>
<tr class="separator:a2302f54f1139c38b3de67ae9b8606afb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affff6bd927a38fc199f92c32fd031490"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#affff6bd927a38fc199f92c32fd031490">GetInterface</a> (self)</td></tr>
<tr class="memdesc:affff6bd927a38fc199f92c32fd031490"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the GenICam interface type of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object Depending on the GenICam interface type, different feature access methods are provided.  <a href="#affff6bd927a38fc199f92c32fd031490">More...</a><br /></td></tr>
<tr class="separator:affff6bd927a38fc199f92c32fd031490"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1aa8c9d453a386b2f9f91cd3b3615c3"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#ad1aa8c9d453a386b2f9f91cd3b3615c3">GetToolTip</a> (self)</td></tr>
<tr class="memdesc:ad1aa8c9d453a386b2f9f91cd3b3615c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get a short description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#ad1aa8c9d453a386b2f9f91cd3b3615c3">More...</a><br /></td></tr>
<tr class="separator:ad1aa8c9d453a386b2f9f91cd3b3615c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6830b31068e48ae5db294d32f2def11c"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#a6830b31068e48ae5db294d32f2def11c">GetDescription</a> (self)</td></tr>
<tr class="memdesc:a6830b31068e48ae5db294d32f2def11c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#a6830b31068e48ae5db294d32f2def11c">More...</a><br /></td></tr>
<tr class="separator:a6830b31068e48ae5db294d32f2def11c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae28b09dddd7447d487a04a8258590633"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#ae28b09dddd7447d487a04a8258590633">GetName</a> (self)</td></tr>
<tr class="memdesc:ae28b09dddd7447d487a04a8258590633"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#ae28b09dddd7447d487a04a8258590633">More...</a><br /></td></tr>
<tr class="separator:ae28b09dddd7447d487a04a8258590633"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa79f26165f8083861522250888e0cee3"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#aa79f26165f8083861522250888e0cee3">GetDisplayName</a> (self)</td></tr>
<tr class="memdesc:aa79f26165f8083861522250888e0cee3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the display name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#aa79f26165f8083861522250888e0cee3">More...</a><br /></td></tr>
<tr class="separator:aa79f26165f8083861522250888e0cee3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4bacabd7d5e68d9e54e88314660e2e9a"><td class="memItemLeft" align="right" valign="top">&quot;str&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#a4bacabd7d5e68d9e54e88314660e2e9a">GetVisibility</a> (self)</td></tr>
<tr class="memdesc:a4bacabd7d5e68d9e54e88314660e2e9a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the recommended visibility (Beginner/Expert/Guru or Invisible) of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object.  <a href="#a4bacabd7d5e68d9e54e88314660e2e9a">More...</a><br /></td></tr>
<tr class="separator:a4bacabd7d5e68d9e54e88314660e2e9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04f73633d9a8b93b7865bf91d2d36b67"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#a04f73633d9a8b93b7865bf91d2d36b67">IsReadable</a> (self)</td></tr>
<tr class="memdesc:a04f73633d9a8b93b7865bf91d2d36b67"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicates that the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is readable.  <a href="#a04f73633d9a8b93b7865bf91d2d36b67">More...</a><br /></td></tr>
<tr class="separator:a04f73633d9a8b93b7865bf91d2d36b67"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac1faf49fc350a92ddf2ce11d35ba85c"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#aac1faf49fc350a92ddf2ce11d35ba85c">IsWritable</a> (self)</td></tr>
<tr class="memdesc:aac1faf49fc350a92ddf2ce11d35ba85c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicates if a <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is writeable.  <a href="#aac1faf49fc350a92ddf2ce11d35ba85c">More...</a><br /></td></tr>
<tr class="separator:aac1faf49fc350a92ddf2ce11d35ba85c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada3c4ad870b561c575714a16f70d19db"><td class="memItemLeft" align="right" valign="top">&quot;bool&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00823.html#ada3c4ad870b561c575714a16f70d19db">IsAvailable</a> (self)</td></tr>
<tr class="memdesc:ada3c4ad870b561c575714a16f70d19db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Indicates whether the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is available "Not available" or False is equivalent to the access mode 'NA'.  <a href="#ada3c4ad870b561c575714a16f70d19db">More...</a><br /></td></tr>
<tr class="separator:ada3c4ad870b561c575714a16f70d19db"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:ac3ab7c8661ab9ae29355bef96d28f4f9"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00259.html#ac3ab7c8661ab9ae29355bef96d28f4f9">value</a> = property(<a class="el" href="a00259.html#a120b125f26edab4712a3b6665105c75c">Get</a>, <a class="el" href="a00259.html#a0b5ebdf7fa97f2908efdb3cd24ab209f">Set</a>)</td></tr>
<tr class="memdesc:ac3ab7c8661ab9ae29355bef96d28f4f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">property to get and set the value.  <a href="#ac3ab7c8661ab9ae29355bef96d28f4f9">More...</a><br /></td></tr>
<tr class="separator:ac3ab7c8661ab9ae29355bef96d28f4f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c0a659fcc6f0de09b7d42530b8aaad8"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00847.html#a8c0a659fcc6f0de09b7d42530b8aaad8">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:a8c0a659fcc6f0de09b7d42530b8aaad8"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#a8c0a659fcc6f0de09b7d42530b8aaad8">More...</a><br /></td></tr>
<tr class="separator:a8c0a659fcc6f0de09b7d42530b8aaad8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Character set used by the strings of the device. </p>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="ab788ecf67e5d1382147d4866b2190358"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab788ecf67e5d1382147d4866b2190358">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.CDeviceCharacterSet.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Constructor. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>object</b> C-pointer for a feature </td></tr>
  </table>
  </dd>
</dl>

<p>Reimplemented from <a class="el" href="a00847.html#a4a787ada7168db2610fec341f811a6fc">neoapi.EnumerationFeature</a>.</p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a0b5ebdf7fa97f2908efdb3cd24ab209f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0b5ebdf7fa97f2908efdb3cd24ab209f">&#9670;&nbsp;</a></span>Set()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;CDeviceCharacterSet&quot; neoapi.CDeviceCharacterSet.Set </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">'DeviceCharacterSet'&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the current value. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>The target value to set. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00259.html" title="Character set used by the strings of the device.">CDeviceCharacterSet</a> object with the value set. </dd></dl>

</div>
</div>
<a id="a120b125f26edab4712a3b6665105c75c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a120b125f26edab4712a3b6665105c75c">&#9670;&nbsp;</a></span>Get()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;DeviceCharacterSet&quot; neoapi.CDeviceCharacterSet.Get </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the current value. </p>
<dl class="section return"><dt>Returns</dt><dd>The DeviceCharacterSet value. </dd></dl>

</div>
</div>
<a id="aeb4392642486f932de046b64d6114df6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aeb4392642486f932de046b64d6114df6">&#9670;&nbsp;</a></span>GetEnumValueList()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;FeatureList&quot; neoapi.EnumerationFeature.GetEnumValueList </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get a list of all possible values of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object Only valid for interface type 'IEnumeration'. </p>
<dl class="section return"><dt>Returns</dt><dd>The list of all possible values of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a452fdbc062b37f3915e0bcfb0aebf3b0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a452fdbc062b37f3915e0bcfb0aebf3b0">&#9670;&nbsp;</a></span>GetInt()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;int&quot; neoapi.EnumerationFeature.GetInt </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object as integer. </p>
<dl class="section return"><dt>Returns</dt><dd>The current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a0bcb8c3b80260c8d0cde108eaf365be4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0bcb8c3b80260c8d0cde108eaf365be4">&#9670;&nbsp;</a></span>SetInt()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;EnumerationFeature&quot; neoapi.EnumerationFeature.SetInt </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Writes an integer value to the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>An integer value to be written </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00847.html" title="Base class providing the &#39;IEnumeration&#39; interface.">EnumerationFeature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac4a3e7aba4519e49f8f32afe4ba29869"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac4a3e7aba4519e49f8f32afe4ba29869">&#9670;&nbsp;</a></span>GetString()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.EnumerationFeature.GetString </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> as a string. </p>
<dl class="section return"><dt>Returns</dt><dd>The current value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a82a850e2d931254434f9a8e454fcb536"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82a850e2d931254434f9a8e454fcb536">&#9670;&nbsp;</a></span>SetString()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;EnumerationFeature&quot; neoapi.EnumerationFeature.SetString </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;str&quot;&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Write the value of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> as a string. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>A string value to be written </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00847.html" title="Base class providing the &#39;IEnumeration&#39; interface.">EnumerationFeature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ab78a7830a2eeff9e0c82484d8f538962"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab78a7830a2eeff9e0c82484d8f538962">&#9670;&nbsp;</a></span>IsSelector()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.EnumerationFeature.IsSelector </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Indicates whether the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is a selector A selector is a possibility to define feature dependencies. </p>
<p>The current value of a selector feature has an impact on the value of another <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </p><dl class="section return"><dt>Returns</dt><dd>True if the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is a selector, otherwise false </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a2302f54f1139c38b3de67ae9b8606afb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2302f54f1139c38b3de67ae9b8606afb">&#9670;&nbsp;</a></span>GetSelectedFeatureList()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;FeatureList&quot; neoapi.EnumerationFeature.GetSelectedFeatureList </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get a list of features that depend on this selector feature. </p>
<dl class="section return"><dt>Returns</dt><dd>The list of all features that depend on this selector <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="affff6bd927a38fc199f92c32fd031490"></a>
<h2 class="memtitle"><span class="permalink"><a href="#affff6bd927a38fc199f92c32fd031490">&#9670;&nbsp;</a></span>GetInterface()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetInterface </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the GenICam interface type of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object Depending on the GenICam interface type, different feature access methods are provided. </p>
<p>The available interface types are defined in header file bgapi2_def.h. See definitions BGAPI2_NODEINTERFACE_xxx </p><dl class="section return"><dt>Returns</dt><dd>The interface type of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ad1aa8c9d453a386b2f9f91cd3b3615c3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad1aa8c9d453a386b2f9f91cd3b3615c3">&#9670;&nbsp;</a></span>GetToolTip()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetToolTip </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get a short description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The short description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a6830b31068e48ae5db294d32f2def11c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6830b31068e48ae5db294d32f2def11c">&#9670;&nbsp;</a></span>GetDescription()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetDescription </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the description of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The description text of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ae28b09dddd7447d487a04a8258590633"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae28b09dddd7447d487a04a8258590633">&#9670;&nbsp;</a></span>GetName()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetName </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa79f26165f8083861522250888e0cee3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa79f26165f8083861522250888e0cee3">&#9670;&nbsp;</a></span>GetDisplayName()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetDisplayName </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the display name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>The display name of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a4bacabd7d5e68d9e54e88314660e2e9a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4bacabd7d5e68d9e54e88314660e2e9a">&#9670;&nbsp;</a></span>GetVisibility()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;str&quot; neoapi.BaseFeature.GetVisibility </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Get the recommended visibility (Beginner/Expert/Guru or Invisible) of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object. </p>
<dl class="section return"><dt>Returns</dt><dd>A string representing the visibility of the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a04f73633d9a8b93b7865bf91d2d36b67"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a04f73633d9a8b93b7865bf91d2d36b67">&#9670;&nbsp;</a></span>IsReadable()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.BaseFeature.IsReadable </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Indicates that the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is readable. </p>
<dl class="section return"><dt>Returns</dt><dd>True if the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is readable, otherwise false </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aac1faf49fc350a92ddf2ce11d35ba85c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aac1faf49fc350a92ddf2ce11d35ba85c">&#9670;&nbsp;</a></span>IsWritable()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.BaseFeature.IsWritable </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Indicates if a <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is writeable. </p>
<dl class="section return"><dt>Returns</dt><dd>True if the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is writable, otherwise false </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ada3c4ad870b561c575714a16f70d19db"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada3c4ad870b561c575714a16f70d19db">&#9670;&nbsp;</a></span>IsAvailable()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;bool&quot; neoapi.BaseFeature.IsAvailable </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Indicates whether the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is available "Not available" or False is equivalent to the access mode 'NA'. </p>
<dl class="section return"><dt>Returns</dt><dd>True if the <a class="el" href="a00811.html" title="Provides access to camera features This class provides an easy way to work with camera features.">Feature</a> object is available to work with it, otherwise false </dd></dl>
<dl class="exception"><dt>Exceptions</dt><dd>
  <table class="exception">
    <tr><td class="paramname"><a class="el" href="a00779.html" title="Feature not accessible Exception.">FeatureAccessException</a></td><td>The calling object is not valid </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="ac3ab7c8661ab9ae29355bef96d28f4f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac3ab7c8661ab9ae29355bef96d28f4f9">&#9670;&nbsp;</a></span>value</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.CDeviceCharacterSet.value = property(<a class="el" href="a00259.html#a120b125f26edab4712a3b6665105c75c">Get</a>, <a class="el" href="a00259.html#a0b5ebdf7fa97f2908efdb3cd24ab209f">Set</a>)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>property to get and set the value. </p>

</div>
</div>
<a id="a8c0a659fcc6f0de09b7d42530b8aaad8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8c0a659fcc6f0de09b7d42530b8aaad8">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.EnumerationFeature.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>generated/neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
