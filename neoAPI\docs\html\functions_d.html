<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_d"></a>- d -</h3><ul>
<li>DecimationHorizontal()
: <a class="el" href="a00763.html#aa04613efdcf8cdd10a85ac67abce64fb">neoapi.FeatureAccess</a>
</li>
<li>DecimationHorizontalMode()
: <a class="el" href="a00763.html#afb9221f4e233fbd5054a27a68a6a9a85">neoapi.FeatureAccess</a>
</li>
<li>DecimationVertical()
: <a class="el" href="a00763.html#a909dd094192d0056684a7097607ffb79">neoapi.FeatureAccess</a>
</li>
<li>DecimationVerticalMode()
: <a class="el" href="a00763.html#a972537aaf59dbcaf110cdc58aa2d4453">neoapi.FeatureAccess</a>
</li>
<li>DefectPixelCorrection()
: <a class="el" href="a00763.html#abfc1ece568499b07130091762550b5a4">neoapi.FeatureAccess</a>
</li>
<li>DefectPixelListEntryActive()
: <a class="el" href="a00763.html#a719d65b2773bec1c127f69dd09e78ecc">neoapi.FeatureAccess</a>
</li>
<li>DefectPixelListEntryPosX()
: <a class="el" href="a00763.html#a072bdd34fe8bbf9e180bed038466de3e">neoapi.FeatureAccess</a>
</li>
<li>DefectPixelListEntryPosY()
: <a class="el" href="a00763.html#abbfa1733ddd847fc9954dbd0cd2fe182">neoapi.FeatureAccess</a>
</li>
<li>DefectPixelListIndex()
: <a class="el" href="a00763.html#aeaf8906e8541cabf996e4dd42c75a82e">neoapi.FeatureAccess</a>
</li>
<li>DefectPixelListSelector()
: <a class="el" href="a00763.html#a79704b3bae43fd5249d4031cfb264752">neoapi.FeatureAccess</a>
</li>
<li>DeviceCharacterSet()
: <a class="el" href="a00763.html#aef9e7a25dca718f5d7f58d3a862579da">neoapi.FeatureAccess</a>
</li>
<li>DeviceClockFrequency()
: <a class="el" href="a00763.html#a0b0d21e8053827f9c24ea2a8d6136a2d">neoapi.FeatureAccess</a>
</li>
<li>DeviceClockSelector()
: <a class="el" href="a00763.html#a1fb245945ffee50cd7806d58a19897f1">neoapi.FeatureAccess</a>
</li>
<li>DeviceEventChannelCount()
: <a class="el" href="a00763.html#a7afd8cbb1d473788e601fd20521b9014">neoapi.FeatureAccess</a>
</li>
<li>DeviceFamilyName()
: <a class="el" href="a00763.html#aad063331d513081f5105a8316e04dfe3">neoapi.FeatureAccess</a>
</li>
<li>DeviceFirmwareVersion()
: <a class="el" href="a00763.html#ad059a50d850deb7e145a313efc4b617a">neoapi.FeatureAccess</a>
</li>
<li>DeviceFrontUARTSource()
: <a class="el" href="a00763.html#a24b06a93d9268c95a5cb7f35a7c04101">neoapi.FeatureAccess</a>
</li>
<li>DeviceGenCPVersionMajor()
: <a class="el" href="a00763.html#a52df8f99a5f1d984d2a4f7c4c58c787e">neoapi.FeatureAccess</a>
</li>
<li>DeviceGenCPVersionMinor()
: <a class="el" href="a00763.html#af81dfd3361291ef05367b33b89c69198">neoapi.FeatureAccess</a>
</li>
<li>DeviceLicense()
: <a class="el" href="a00763.html#a87bdd2f8a917cba4ef6b7c57f7a55683">neoapi.FeatureAccess</a>
</li>
<li>DeviceLicenseTypeSelector()
: <a class="el" href="a00763.html#a77b95ee14bb1a2be58dc8efdbd84a5be">neoapi.FeatureAccess</a>
</li>
<li>DeviceLinkCommandTimeout()
: <a class="el" href="a00763.html#a4424e524111e7ccf98e75a0bfea6ee4f">neoapi.FeatureAccess</a>
</li>
<li>DeviceLinkHeartbeatMode()
: <a class="el" href="a00763.html#a1c1583cba919b55a9d606dc93f503134">neoapi.FeatureAccess</a>
</li>
<li>DeviceLinkHeartbeatTimeout()
: <a class="el" href="a00763.html#a5f3a01b604f3b84596884eec87ebe44f">neoapi.FeatureAccess</a>
</li>
<li>DeviceLinkSelector()
: <a class="el" href="a00763.html#ab70ef3766f0a34f390b714dff26126a1">neoapi.FeatureAccess</a>
</li>
<li>DeviceLinkSpeed()
: <a class="el" href="a00763.html#aa09615dee08342b1c64ac5ef50add152">neoapi.FeatureAccess</a>
</li>
<li>DeviceLinkThroughputLimit()
: <a class="el" href="a00763.html#a6c949cb10c6139c4ff3065cdc1343f9e">neoapi.FeatureAccess</a>
</li>
<li>DeviceLinkThroughputLimitMode()
: <a class="el" href="a00763.html#a237493b542130101daed74b28d9d2f44">neoapi.FeatureAccess</a>
</li>
<li>DeviceManufacturerInfo()
: <a class="el" href="a00763.html#a6685e1c56a1517736323f717a2275b2c">neoapi.FeatureAccess</a>
</li>
<li>DeviceManufacturerVersion()
: <a class="el" href="a00763.html#a2beb0b0ebeddd0da42890d30ac6e755c">neoapi.FeatureAccess</a>
</li>
<li>DeviceModelName()
: <a class="el" href="a00763.html#ae6cf1609058c5fff0fb3aaa755d16dd4">neoapi.FeatureAccess</a>
</li>
<li>DeviceRegistersEndianness()
: <a class="el" href="a00763.html#a3e359cd89c7f37cd67009b907db6c203">neoapi.FeatureAccess</a>
</li>
<li>DeviceReset()
: <a class="el" href="a00763.html#a858dfe8273fe720fb591c9cde929dc8a">neoapi.FeatureAccess</a>
</li>
<li>DeviceResetToDeliveryState()
: <a class="el" href="a00763.html#abb81d56f51404cf5287c16bb3c683477">neoapi.FeatureAccess</a>
</li>
<li>DeviceScanType()
: <a class="el" href="a00763.html#ab4ac2f1a3c64941a751666d58f6ea272">neoapi.FeatureAccess</a>
</li>
<li>DeviceSensorConnected()
: <a class="el" href="a00763.html#a539b2065c7e8627b8cdf627733c9b2eb">neoapi.FeatureAccess</a>
</li>
<li>DeviceSensorInitialized()
: <a class="el" href="a00763.html#acad78676d9cc4969030c3989c49a1df9">neoapi.FeatureAccess</a>
</li>
<li>DeviceSensorSelector()
: <a class="el" href="a00763.html#acff32aa93771e7a9f858b192bcee7e0c">neoapi.FeatureAccess</a>
</li>
<li>DeviceSensorType()
: <a class="el" href="a00763.html#a652a05ac5158e871f6a1f2f7d920ca39">neoapi.FeatureAccess</a>
</li>
<li>DeviceSensorVersion()
: <a class="el" href="a00763.html#a58f87c8a6027915b6698cbf496f69b1c">neoapi.FeatureAccess</a>
</li>
<li>DeviceSerialNumber()
: <a class="el" href="a00763.html#aaefda1932e5717b900879f8bfb5c09e1">neoapi.FeatureAccess</a>
</li>
<li>DeviceSerialPortBaudRate()
: <a class="el" href="a00763.html#a2a2307084fc9557990f92f330d2852ca">neoapi.FeatureAccess</a>
</li>
<li>DeviceSerialPortSelector()
: <a class="el" href="a00763.html#aa582295bab61a9989652beaa3142468c">neoapi.FeatureAccess</a>
</li>
<li>DeviceSFNCVersionMajor()
: <a class="el" href="a00763.html#a43bce0d9f0d8cd818c2210e7252d0380">neoapi.FeatureAccess</a>
</li>
<li>DeviceSFNCVersionMinor()
: <a class="el" href="a00763.html#a73a74a20568289d9258b891f5aa2a347">neoapi.FeatureAccess</a>
</li>
<li>DeviceSFNCVersionSubMinor()
: <a class="el" href="a00763.html#adccea069a7df48ba29eaa27c7e82c959">neoapi.FeatureAccess</a>
</li>
<li>DeviceStreamChannelCount()
: <a class="el" href="a00763.html#a9ae52d5adb18b1460e13498b33f31ac2">neoapi.FeatureAccess</a>
</li>
<li>DeviceStreamChannelEndianness()
: <a class="el" href="a00763.html#ae3a07e1b07afd923cb1315ec639c9bd2">neoapi.FeatureAccess</a>
</li>
<li>DeviceStreamChannelPacketSize()
: <a class="el" href="a00763.html#a62c1da8138625122db4e544c8c8cf640">neoapi.FeatureAccess</a>
</li>
<li>DeviceStreamChannelSelector()
: <a class="el" href="a00763.html#abab8041e217c152da40552ab80b5794d">neoapi.FeatureAccess</a>
</li>
<li>DeviceStreamChannelType()
: <a class="el" href="a00763.html#ab1ed24ac9a70c4c4e1a8a6c35f5ceab3">neoapi.FeatureAccess</a>
</li>
<li>DeviceTapGeometry()
: <a class="el" href="a00763.html#a5e352d8fb561d6c36b3def622cd52693">neoapi.FeatureAccess</a>
</li>
<li>DeviceTemperature()
: <a class="el" href="a00763.html#ad542fda81eba82753c01a56d3fa6527f">neoapi.FeatureAccess</a>
</li>
<li>DeviceTemperatureExceeded()
: <a class="el" href="a00763.html#a1923b6fc231c57d5006aaf924e7c8800">neoapi.FeatureAccess</a>
</li>
<li>DeviceTemperaturePeltierEnable()
: <a class="el" href="a00763.html#a1100720d8dea08e24ea6d8a85537cf2d">neoapi.FeatureAccess</a>
</li>
<li>DeviceTemperatureSelector()
: <a class="el" href="a00763.html#a9aee1bf7243520ff137b59bce9a950ec">neoapi.FeatureAccess</a>
</li>
<li>DeviceTemperatureStatus()
: <a class="el" href="a00763.html#a9c7a051ee73e6f2c93f66692c7546ce5">neoapi.FeatureAccess</a>
</li>
<li>DeviceTemperatureStatusTransition()
: <a class="el" href="a00763.html#a7deb69a527339f43cd1d2362e25c7788">neoapi.FeatureAccess</a>
</li>
<li>DeviceTemperatureStatusTransitionSelector()
: <a class="el" href="a00763.html#a24c5e075400f1d57aac10df12dfac0ae">neoapi.FeatureAccess</a>
</li>
<li>DeviceTemperatureUnderrun()
: <a class="el" href="a00763.html#acab33b8e0c4f8f0193d9d335a9679953">neoapi.FeatureAccess</a>
</li>
<li>DeviceTLType()
: <a class="el" href="a00763.html#a230750905aa5d75dec94cafbe21616e1">neoapi.FeatureAccess</a>
</li>
<li>DeviceTLVersionMajor()
: <a class="el" href="a00763.html#aff836b4f1ffd4b8de526acdce7471de6">neoapi.FeatureAccess</a>
</li>
<li>DeviceTLVersionMinor()
: <a class="el" href="a00763.html#a543349e020647d9fedc29d2a14aca9a3">neoapi.FeatureAccess</a>
</li>
<li>DeviceTLVersionSubMinor()
: <a class="el" href="a00763.html#adad5993592059d17b9e6088f5e0ea7c7">neoapi.FeatureAccess</a>
</li>
<li>DeviceType()
: <a class="el" href="a00763.html#a2dd5b69942a451215b6fb15be7c04361">neoapi.FeatureAccess</a>
</li>
<li>DeviceUSB3VisionGUID()
: <a class="el" href="a00763.html#acf83edbe47c758df1ecddb680202314b">neoapi.FeatureAccess</a>
</li>
<li>DeviceUserID()
: <a class="el" href="a00763.html#ace54cfeb450142aac2f1fee5a1ae2421">neoapi.FeatureAccess</a>
</li>
<li>DeviceVendorName()
: <a class="el" href="a00763.html#a6e0f28da4bc956ae169374f9a7099375">neoapi.FeatureAccess</a>
</li>
<li>DeviceVersion()
: <a class="el" href="a00763.html#a5dc3840195f203dafa2a4d521b2db6d9">neoapi.FeatureAccess</a>
</li>
<li>DeviceVersionControl()
: <a class="el" href="a00763.html#a14e5e3e665d2c5b3ed12092e3416949e">neoapi.FeatureAccess</a>
</li>
<li>DisableChunk()
: <a class="el" href="a00863.html#ad681192391a74de7e565c0d27eda715d">neoapi.Cam</a>
, <a class="el" href="a00859.html#a650bf056805ae3c08dc53a48e1625fba">neoapi.CamBase</a>
</li>
<li>DisableEvent()
: <a class="el" href="a00863.html#a9751caf0f47a47e38ea91ef8599ec2ad">neoapi.Cam</a>
, <a class="el" href="a00859.html#a8f6803711004617ffda5590e5e7f95b6">neoapi.CamBase</a>
</li>
<li>DisableEventCallback()
: <a class="el" href="a00863.html#af6a0fc58c51bf7579fc086280df4a3ad">neoapi.Cam</a>
, <a class="el" href="a00859.html#adb6e79938a65f05b9605b675956005ac">neoapi.CamBase</a>
</li>
<li>DisableImageCallback()
: <a class="el" href="a00863.html#a360b1e87ab0e0bd912199ef87539d23b">neoapi.Cam</a>
, <a class="el" href="a00859.html#a8fa517e4a1d9fe8ee897e83de308e512">neoapi.CamBase</a>
</li>
<li>DisableLogCallback()
: <a class="el" href="a00899.html#aa9fe4ab1821cfecda000f37802097614">neoapi.NeoTrace</a>
</li>
<li>DisableLogfile()
: <a class="el" href="a00899.html#a2bfbc8f0848cb91c577680ad03ddf5da">neoapi.NeoTrace</a>
</li>
<li>DisablePnPEventCallback()
: <a class="el" href="a00863.html#a8021221f0a6e3068c316445e215fbbe3">neoapi.Cam</a>
, <a class="el" href="a00859.html#ada7b8d016968405a436c425caa46ce41">neoapi.CamBase</a>
, <a class="el" href="a00891.html#a5ad4b079de5ee2d2745b4a0686b6d577">neoapi.CamInfoList</a>
</li>
<li>DiscardedEventCounter()
: <a class="el" href="a00763.html#a0dc7bc58a32945d29a00a513d3abeb95">neoapi.FeatureAccess</a>
</li>
<li>Disconnect()
: <a class="el" href="a00863.html#af6de2c6d720ddff06eab94d44f511719">neoapi.Cam</a>
, <a class="el" href="a00859.html#a1b81f0c2270ab38b0cff03219249b301">neoapi.CamBase</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
