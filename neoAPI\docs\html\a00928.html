<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Getting started with neoAPI for Python</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Getting started with neoAPI for Python </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><h1><a class="anchor" id="autotoc_md106"></a>
Welcome to the Baumer neoAPI for Python</h1>
<p>This package provides you with everything you need to program a Baumer camera with the Baumer neoAPI. If You are looking for a package for C++ or Microsoft C#, please download it separately from the <a href="https://www.baumer.com/c/333">Baumer web site</a></p>
<p>The Baumer neoAPI for Python is compatible to Python version 2.7 and 3.4 upwards. However, we strongly recommend using it with current Python version 3.9. Please be aware, that neoAPI does require a cPython implementation. So packages like winpython will not work.</p>
<h2><a class="anchor" id="autotoc_md107"></a>
Prerequisites</h2>
<ul>
<li>Download the package for your operating system and architecture <a href="https://www.baumer.com/c/333">here</a></li>
<li>Have a suitable host system (64 bit Windows or Linux) or ARM board (AArch64 Linux) with at least one Gigabit Ethernet or USB3 port ready</li>
<li>Have a Baumer GigE or USB camera including necessary cables and suitable power supply to play with</li>
<li>Have a suitable Python development environment of your choice set up</li>
<li>Optionally you can download and install the Baumer Camera Explorer, a graphical tool which will help you to understand and work with cameras</li>
<li>Some provided examples require OpenCV (at least Version 3), if you want to build them you need to install it using pip</li>
</ul>
<h2><a class="anchor" id="autotoc_md108"></a>
Install the required drivers</h2>
<ul>
<li>Windows<ul>
<li>If USB cameras are used it is necessary to install the USB-driver provided with the package (see <code>/drivers/</code>)</li>
<li>For GigE cameras the Baumer filter-driver reduces the system-load compared to the Windows socket driver. We recommend installing and using the filter-driver provided with the package (see <code>/drivers/</code>)</li>
<li>For further information how to get the most performance out a GigE connection on a Windows system please refer to the application notes <a href="https://www.baumer.com/a/gigabit-ethernet-adapter-settings">AN201622</a> and <a href="https://www.baumer.com/a/10-gige-and-baumer-gapi">AN201802</a>.</li>
</ul>
</li>
<li>Linux<ul>
<li>If you are using Linux no driver needs to be installed.</li>
<li>To provide non root users access to usb devices copy the provided udev-rules (see <code>/drivers/udev_rules/</code>) in the drivers folder to <code>/etc/udev/rules.d/</code>.</li>
<li>Restart the system for the changed udev-rules to take effect</li>
<li>As the Linux kernel usually sets a 16 MB memory limit for the USB system, it might be necessary to raise the memory limit, especially for multi-camera systems. Please see the application note <a href="https://www.baumer.com/a/bgapi-sdk-for-usb-multi-camera-systems-with-linux">AN201707</a> for details.</li>
<li>To prevent resend requests for lost pakets you should check udp and memory settings f.i. by <code>sysctl -a | grep mem</code>. To secure enough storage space add to /etc/sysctl.conf: <div class="fragment"><div class="line">### TUNING NETWORK PERFORMANCE ###</div><div class="line"># Maximum Socket Receive Buffer</div><div class="line">net.core.rmem_max = 67108864</div></div><!-- fragment --></li>
</ul>
</li>
</ul>
<blockquote class="doxtable">
<p><b>Attention</b></p>
<p>If you have trouble connecting to a Baumer GigE camera, it might be necessary to configure the network settings of the camera first. You'll find the network configuration tool <code>gevipconfig</code> in the tools folder of this package. </p>
</blockquote>
<h1><a class="anchor" id="autotoc_md109"></a>
Install the neoAPI Python wheel</h1>
<h2><a class="anchor" id="autotoc_md110"></a>
Step one — create virtual python environment (optional)</h2>
<p>Creating a virtual environment for Python is not strictly necessary but highly recommended. It ensures, that you don't mix requirements from different projects.</p>
<p>Windows</p>
<ul>
<li>Download and install a python version of your choice</li>
<li>Go to a folder where you want to create the virtual environment in</li>
<li>Create an environment with a chosen name <code>python -m venv &lt;venv_name&gt;</code>, it will be created in a folder with the chosen name</li>
<li>Activate the environment by calling <code>&lt;venv_name&gt;/Scripts/activate</code></li>
<li>Your command prompt should now be prefixed with <code>(&lt;venv_name&gt;)</code></li>
<li>You can deactivate the virtual environment again by calling <code>&lt;venv_name&gt;/Scripts/deactivate.bat</code></li>
</ul>
<p>Linux (Ubuntu)</p>
<ul>
<li>Install a python version of your choice using the package manager <code>sudo apt-get install python3</code></li>
<li>Install the virtualenv module <code>sudo apt-get install python3-virtualenv</code></li>
<li>Create an environment with a chosen name <code>virtualenv -p /usr/bin/python3 &lt;venv_name&gt;</code>, it will be created in a folder with the chosen name</li>
<li>Activate the environment by calling <code>source &lt;venv_name&gt;/bin/activate</code></li>
<li>Your command prompt should now be prefixed with <code>(&lt;venv_name&gt;)</code></li>
<li>You can deactivate the virtual environment again by calling <code>deactivate</code></li>
</ul>
<h2><a class="anchor" id="autotoc_md111"></a>
Step two — install the right wheel for your virtual python environment</h2>
<ul>
<li>Activate your created virtual Python environment (see step one)</li>
<li>Find out which Python version you have installed by running <code>python3 --version</code> in a command line window</li>
<li>Change to the folder where you unpacked the neoAPI wheel files (*.whl)</li>
<li>run <code>python3 -m pip install neoapi_&lt;neoapi-version&gt;-cp&lt;python-version&gt;-cp&lt;python-version&gt;m-&lt;platform&gt;.whl</code> to install the neoAPI</li>
</ul>
<blockquote class="doxtable">
<p><b>Attention</b></p>
<p>Don't use neoapi from PyPI! This is a different vendors API and not the Baumer package you downloaded! </p>
</blockquote>
<p>Now you are ready to start programming!</p>
<h1><a class="anchor" id="autotoc_md112"></a>
Try the examples</h1>
<p>Once you activated the virtual environment created above and installed the neoAPI wheel you can run the provided examples. Some examples might require further pip-packages to be installed.</p>
<h1><a class="anchor" id="autotoc_md113"></a>
License</h1>
<p>Please see the /License file for licensing information. </p>
</div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
