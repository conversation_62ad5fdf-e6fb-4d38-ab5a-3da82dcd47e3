<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Event and Plug and Play Programming Concepts of the neoAPI</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Event and Plug and Play Programming Concepts of the neoAPI </div>  </div>
</div><!--header-->
<div class="contents">
<div class="toc"><h3>Table of Contents</h3>
<ul><li class="level1"><a href="#autotoc_md67">Events overview</a></li>
<li class="level1"><a href="#autotoc_md68">Device Events</a><ul><li class="level2"><a href="#autotoc_md69">Discovery of Device Events</a></li>
<li class="level2"><a href="#autotoc_md70">Polling of Device Events</a></li>
<li class="level2"><a href="#autotoc_md71">Device Events by Callback</a></li>
</ul>
</li>
<li class="level1"><a href="#autotoc_md72">Plug and Play Events</a><ul><li class="level2"><a href="#autotoc_md73">Polling PnP Events</a></li>
<li class="level2"><a href="#autotoc_md74">PnP Events by Callback</a></li>
</ul>
</li>
<li class="level1"><a href="#autotoc_md75">Image events</a><ul><li class="level2"><a href="#autotoc_md76">Images by Callback</a></li>
</ul>
</li>
</ul>
</div>
<div class="textblock"><p>This document describes the concepts around Events and Plug and Play and how they can be used in the neoAPI.</p>
<h1><a class="anchor" id="autotoc_md67"></a>
Events overview</h1>
<p>Events can support you to discover the current state or changes in your camera system. It is a way to be alerted to changes which happen automatically or based of things outside of your programm.</p>
<p>There are 3 groups of events:</p>
<ul>
<li><b>Device Events:</b> those are generated by the camera to alert you about certain changes during operation. The GenICam Standard calls them remote device events. Which Device Events are available depends on the connected camera and it's capabilities. The available Device Events can be listed with <code><a class="el" href="a00859.html#a96e7621be71177bc2bd79bb266d03d3c" title="Get a list of event names supported by the camera A GenICam camera can support many events which aler...">neoapi.Cam.GetAvailableEvents()</a></code>.</li>
<li><b>Plug-and-Play Events:</b> those are generated by the interface (USB or GigE) to alert you about availability of the camera in general e.g. camera is connectable, unplugged or in a state where it is unreachable by the neoAPI. The neoAPI provides the <code>DeviceAdded</code>, <code>DeviceInAccessible</code> and <code>DeviceRemoved</code> events to help you observe the operation of your system.</li>
<li><b>Image Events:</b> those inform you about the availability of a new image from the camera which can be useful for example when the camera is triggered externally.</li>
</ul>
<p>There are two ways of working with events in the neoAPI:</p>
<ul>
<li>Working synchronous is possible using <code><a class="el" href="a00859.html#a457e6738d020688c89c4f76f6283b538" title="Get an event from the camera The GetEvent method is used to retrieve an event from the camera to work...">neoapi.Cam.GetEvent()</a></code> or <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code></li>
<li>Working asynchronous is supported via callback methods which can be registered to be called for arriving events.</li>
</ul>
<p>The Device and PnP-Events are instances of <code><a class="el" href="a00807.html" title="Provides access to events This class provides an easy way to work with events.">neoapi.NeoEvent</a></code> using its methods you can retrieve the identifier (ID), name and a timestamp for an event. Image events do not contain this information, instead they provide a reference to the actual image with its properties.</p>
<h1><a class="anchor" id="autotoc_md68"></a>
Device Events</h1>
<p>Device Events are generated by the camera. Typical Events include <code>ExposureStart/End</code>, <code>TriggerReady</code> or <code>Line[X]RisingEdge</code>. The available events depend on the specific camera. You can use those events to control the flow of your application. By default, all Device Events are disabled. You need to enable them first on your camera.</p>
<blockquote class="doxtable">
<p>Note:</p>
<p>Enabling many events can produce significant load as many are generated for each acquisition. This might impact the performance of the camera and your application. </p>
</blockquote>
<h2><a class="anchor" id="autotoc_md69"></a>
Discovery of Device Events</h2>
<p>The following code shows how to find out which events your camera supports:</p>
<div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line"></div><div class="line">camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line">camera.Connect()                                        <span class="comment"># connect to a camera</span></div><div class="line"></div><div class="line"><span class="keywordflow">for</span> event_name <span class="keywordflow">in</span> camera.GetAvailableEvents():          <span class="comment"># loop through the events</span></div><div class="line">    print(<span class="stringliteral">&quot;Event name: &quot;</span> + event_name)                  <span class="comment"># print the event names</span></div></div><!-- fragment --><h2><a class="anchor" id="autotoc_md70"></a>
Polling of Device Events</h2>
<p>The following example shows a camera being triggered using the SoftwareTrigger. The <code>ExposureStart</code> event is used to find out when exactly the image acquisition for the image was started. This time will differ from the time the Software Trigger was executed, as it takes time to transfer the trigger request to the camera.</p>
<div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line"></div><div class="line">camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line">camera.Connect()                                        <span class="comment"># connect to a camera</span></div><div class="line"></div><div class="line">camera.f.ExposureTime = 40                              <span class="comment"># set exposure time</span></div><div class="line">camera.f.TriggerMode = neoapi.TriggerMode_On            <span class="comment"># configure camera for SoftwareTrigger</span></div><div class="line">camera.f.TriggerSource = neoapi.TriggerSource_Software</div><div class="line"></div><div class="line">cam_event = <a class="code" href="a00807.html">neoapi.NeoEvent</a>()</div><div class="line">camera.ClearEvents()                                    <span class="comment"># clear any events which might be queued</span></div><div class="line">camera.EnableEvent(<span class="stringliteral">&quot;ExposureStart&quot;</span>)                     <span class="comment"># enable ExposureStart event</span></div><div class="line">camera.f.TriggerSoftware.Execute()                      <span class="comment"># send SoftwareTrigger</span></div><div class="line">cam_event = camera.GetEvent()                           <span class="comment"># get event, wait for default timeout</span></div><div class="line"></div><div class="line"><span class="keywordflow">if</span> <span class="keywordflow">not</span> cam_event.IsEmpty():                             <span class="comment"># check if an event was returned</span></div><div class="line">    print(<span class="stringliteral">&quot;received event: &quot;</span>, cam_event.GetName(), <span class="stringliteral">&quot; at: &quot;</span>,</div><div class="line">         cam_event.GetTimestamp(), <span class="stringliteral">&quot; id: 0x&quot;</span>, cam_event.GetId())</div><div class="line"></div><div class="line">camera.DisableEvent(<span class="stringliteral">&quot;ExposureStart&quot;</span>)                    <span class="comment"># Disable event</span></div></div><!-- fragment --> <div class="caption">Example: Polling of Device Events</div><p>The <code><a class="el" href="a00859.html#a457e6738d020688c89c4f76f6283b538" title="Get an event from the camera The GetEvent method is used to retrieve an event from the camera to work...">neoapi.Cam.GetEvent()</a></code> function will wait for a default timeout of 400 ms before it returns. It will always return one event or an empty event in case no event arrived during the timeout. You might need to call it several times until you received all queued events. If you only want to poll for a certain event, <code><a class="el" href="a00859.html#a457e6738d020688c89c4f76f6283b538" title="Get an event from the camera The GetEvent method is used to retrieve an event from the camera to work...">neoapi.Cam.GetEvent()</a></code> can be called with a string containing the event name you are interested in.</p>
<blockquote class="doxtable">
<p>Note:</p>
<p>Please refer to the user guide of your camera for details about the supported events and in which conditions they are sent. </p>
</blockquote>
<h2><a class="anchor" id="autotoc_md71"></a>
Device Events by Callback</h2>
<p>Device Events can also be received asynchronously. You need to write an event handler method to do something useful with the received event. Once you have written this method, you need to enable the Event Callback using the <code><a class="el" href="a00863.html#a9194d1a7f231b08aa3ae95e0e2956549" title="Enable event callback.">neoapi.Cam.EnableEventCallback()</a></code> method and register the Handler using <code>neoapi.Cam.DeviceEventCallback()</code></p>
<p>You can supply the event name as a string to restrict your handler to a specific event. However, you can only register one handler for each event name. Registring it with an empty string will receive all events.</p>
<blockquote class="doxtable">
<p>Note:</p>
<p>If you choose to mix the polling and callback methods a registered callback will receive the events first and it will not be available for polling. </p>
</blockquote>
<div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line"></div><div class="line"><span class="comment"># create an event handler</span></div><div class="line"><span class="keyword">class </span>TestEventCallback():</div><div class="line">    <span class="keyword">def </span>eventcallback(self, event):</div><div class="line">        print(<span class="stringliteral">&quot;received event: &quot;</span>, event.GetName(), <span class="stringliteral">&quot; at: &quot;</span>, event.GetTimestamp(), <span class="stringliteral">&quot; id: 0x&quot;</span>, event.GetId())</div><div class="line"></div><div class="line">camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line">camera.Connect()                                        <span class="comment"># connect to a camera</span></div><div class="line"></div><div class="line">cam_event = <a class="code" href="a00807.html">neoapi.NeoEvent</a>()</div><div class="line">freecallback = TestEventCallback()</div><div class="line">camera.f.ExposureTime = 40</div><div class="line">camera.EnableEventCallback(freecallback.eventcallback, <span class="stringliteral">&quot;ExposureStart&quot;</span>)  <span class="comment"># register method only for ExposureStart event</span></div><div class="line">camera.ClearEvents()                                    <span class="comment"># clear any events which might be queued</span></div><div class="line">camera.EnableEvent(<span class="stringliteral">&quot;ExposureStart&quot;</span>)                     <span class="comment"># enable events</span></div><div class="line">camera.EnableEvent(<span class="stringliteral">&quot;ExposureEnd&quot;</span>)</div><div class="line"></div><div class="line">camera.f.TriggerSoftware.Execute()</div><div class="line"></div><div class="line">camera.DisableEventCallback()</div><div class="line">camera.DisableEvent(<span class="stringliteral">&quot;ExposureStart&quot;</span>)                    <span class="comment"># disable event</span></div><div class="line">camera.DisableEvent(<span class="stringliteral">&quot;ExposureEnd&quot;</span>)</div></div><!-- fragment --> <div class="caption">Example: Device Events Using a Callback</div><h1><a class="anchor" id="autotoc_md72"></a>
Plug and Play Events</h1>
<p>The neoAPI supports Plug and Play for GigE and USB cameras. Once a camera is successfully connected using <code><a class="el" href="a00863.html#af9342490a56163f394c07abf61064880" title="Connect a GenICam camera device to work with it The Connect method is called to establish a connectio...">neoapi.Cam.Connect()</a></code> the neoAPI will try to keep this connection established even if the camera was unavailable in between. You can use the <code><a class="el" href="a00859.html#aaaf166323ef78a64f75e02be7a32d8fa" title="Checks if a camera is online A camera may be offline if a device reset was performed or as consequenc...">neoapi.Cam.IsOnline</a></code> method to see if the camera is currently connected, while the <code><a class="el" href="a00859.html#a4b3accbf3321c31a5fceb7fc0ab9de87" title="Get the number of times the camera was offline This counter will increase every time the camera goes ...">neoapi.Cam.GetOfflineCount()</a></code> method will count up for each time a disconnection has occurred. This will allow you to monitor instabilities in the system.</p>
<p>Plug and Play Events are similar to Device Events. There are just 3 different PnP Events each with a fixed ID, <code>DeviceAdded</code> (ID: 0x10000ADD), <code>DeviceRemoved</code> (ID: 0x100000FF) and <code>DeviceInAccessible</code> (ID: 0x1ACE5B7E). The associate timestamps are those of the host, not the camera.</p>
<p>The <code>DeviceInAccessible</code> Event will be thrown if the camera is found to be connected to the interface again but not available to the application. This can happen if another application connects to the camera first.</p>
<blockquote class="doxtable">
<p>Note:</p>
<p>A good quality system should not have connectivity issues, if they are not by design, even in 24/7 operations. You should strive to resolve all issues which are causing PnP Events you cannot explain. </p>
</blockquote>
<blockquote class="doxtable">
<p>Note</p>
<p>If a camera is disconnected, it loses all its settings (like features, enabled events), you need to ensure that the camera is reconfigured to keep working with it as expected </p>
</blockquote>
<h2><a class="anchor" id="autotoc_md73"></a>
Polling PnP Events</h2>
<blockquote class="doxtable">
<p>Note:</p>
<p>PnP Events require a much higher Timeout, especially for GigE systems it can take up to 30 seconds until an <code>DeviceRemoved</code> event is received. This is due to the ethernet network architecture and time-outs. </p>
</blockquote>
<div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line"></div><div class="line">camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line">camera.Connect()                                        <span class="comment"># connect to a camera</span></div><div class="line"></div><div class="line">pnp_event = <a class="code" href="a00807.html">neoapi.NeoEvent</a>()</div><div class="line">camera.f.DeviceReset.Execute()</div><div class="line">pnp_event = camera.GetPnPEvent(30000)                   <span class="comment"># wait up to 30 seconds for device remove event</span></div><div class="line"></div><div class="line"><span class="keywordflow">if</span> <span class="keywordflow">not</span> pnp_event.IsEmpty():                             <span class="comment"># check if event arrived and print it</span></div><div class="line">    print(<span class="stringliteral">&quot;received pnp event: &quot;</span>, pnp_event.GetName(), <span class="stringliteral">&quot; at: &quot;</span>,</div><div class="line">        pnp_event.GetTimestamp(), <span class="stringliteral">&quot; id: 0x&quot;</span>, pnp_event.GetId())</div><div class="line"></div><div class="line">pnp_event = camera.GetPnPEvent(30000)                   <span class="comment"># wait up to 30 seconds for device add event</span></div><div class="line"></div><div class="line"><span class="keywordflow">if</span> <span class="keywordflow">not</span> pnp_event.IsEmpty():                             <span class="comment"># check if event arrived and print it</span></div><div class="line">    print(<span class="stringliteral">&quot;received pnp event: &quot;</span>, pnp_event.GetName(), <span class="stringliteral">&quot; at: &quot;</span>,</div><div class="line">        pnp_event.GetTimestamp(), <span class="stringliteral">&quot; id: 0x&quot;</span>, pnp_event.GetId())</div></div><!-- fragment --> <div class="caption">Example: Polling of PnP Events</div><h2><a class="anchor" id="autotoc_md74"></a>
PnP Events by Callback</h2>
<p>Since the PnP events usually arrive very rarely and unpredictable, it makes more sense to use a callback to react on such events.</p>
<div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line"></div><div class="line"><span class="comment"># write the pnp event handler</span></div><div class="line"><span class="keyword">class </span>TestEventCallback():</div><div class="line">    <span class="keyword">def </span>eventcallback(self, event):</div><div class="line">        print(<span class="stringliteral">&quot;received event: &quot;</span>, event.GetName(), <span class="stringliteral">&quot; at: &quot;</span>, event.GetTimestamp(), <span class="stringliteral">&quot; id: 0x&quot;</span>, event.GetId())</div><div class="line"></div><div class="line">camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line">camera.Connect()                                        <span class="comment"># connect to a camera</span></div><div class="line"></div><div class="line">freecallback = TestEventCallback()</div><div class="line">camera.EnablePnPEventCallback(freecallback.eventcallback)  <span class="comment"># enable the callback</span></div><div class="line"></div><div class="line">camera.f.DeviceReset.Execute()                          <span class="comment"># the reset will force the camera offline</span></div><div class="line"></div><div class="line"><span class="keywordflow">while</span> camera.IsOnline():                                <span class="comment"># wait for camera to go offline</span></div><div class="line">    <span class="keywordflow">pass</span></div><div class="line"><span class="keywordflow">while</span> <span class="keywordflow">not</span> camera.IsOnline():                            <span class="comment"># wait for it to come back online</span></div><div class="line">    <span class="keywordflow">pass</span></div><div class="line">time.sleep(0.010)                                       <span class="comment"># short wait until the callback arrives</span></div><div class="line">camera.DisablePnPEventCallback()</div></div><!-- fragment --> <div class="caption">Example: PnP Events using a Callback</div><h1><a class="anchor" id="autotoc_md75"></a>
Image events</h1>
<p>Image can be retrieved synchronously (polled) using the <code><a class="el" href="a00859.html#ab03809a72ac8136b628597d502b4db51" title="Get an image from the camera The GetImage method is used to retrieve an image from the camera to work...">neoapi.Cam.GetImage()</a></code> method. Details of which can be found in the <a class="el" href="a00923.html">Images and Buffers with neoAPI</a> .</p>
<p>Especially in systems where the camera is triggered externally, Image Events can notify you, that a new image is available to be processed.</p>
<h2><a class="anchor" id="autotoc_md76"></a>
Images by Callback</h2>
<p>To retrieve images asynchronously a callback can be used. This works similar to the Device Events. In the callback you will receive a reference to the image which you can use further as required. The image reference will be valid only in the context of the callback methode. At the end of the callback the image buffer is usable again by the neoAPI to store a new image. To extend the lifetime of the image object you need to create a new object with the copy constructor. Please see details in the <a class="el" href="a00923.html">Images and Buffers with neoAPI</a> .</p>
<div class="fragment"><div class="line"><span class="keyword">import</span> neoapi</div><div class="line"></div><div class="line"><span class="comment"># write the image callback</span></div><div class="line"><span class="keyword">class  </span>TestImageCallback:</div><div class="line">    <span class="keyword">def </span>imgcallback(self, image):</div><div class="line">        print(<span class="stringliteral">&quot;received image: &quot;</span>, image.GetBufferID(), <span class="stringliteral">&quot;.&quot;</span>, image.GetImageIndex(),</div><div class="line">            <span class="stringliteral">&quot; size: &quot;</span>, image.GetSize(), <span class="stringliteral">&quot; height: &quot;</span>, image.GetHeight(),</div><div class="line">            <span class="stringliteral">&quot; width: &quot;</span>, image.GetWidth(), <span class="stringliteral">&quot; pixelformat: &quot;</span>, image.GetPixelFormat())</div><div class="line"></div><div class="line">camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line">camera.Connect()                                        <span class="comment"># connect to a camera</span></div><div class="line"></div><div class="line">camera.f.ExposureTime = 40</div><div class="line">camera.f.TriggerMode = neoapi.TriggerMode_On            <span class="comment"># bring camera in TriggerMode</span></div><div class="line">camera.f.TriggerSource = neoapi.TriggerSource_Software</div><div class="line"></div><div class="line">callback = TestImageCallback()</div><div class="line">camera.EnableImageCallback(callback.imgcallback)        <span class="comment"># enable the callback</span></div><div class="line"></div><div class="line"><span class="keywordflow">for</span> i <span class="keywordflow">in</span> range(5):                                      <span class="comment"># send 5 triggers to retrieve some image callbacks</span></div><div class="line">    camera.f.TriggerSoftware.Execute()</div><div class="line">    time.sleep(0.100)</div><div class="line"></div><div class="line">camera.DisableImageCallback()                           <span class="comment"># disable callback</span></div></div><!-- fragment --> <div class="caption">Example: Image Events using a Callback</div> </div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
