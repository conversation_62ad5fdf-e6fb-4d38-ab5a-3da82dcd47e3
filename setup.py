from setuptools import setup, find_packages

setup(
    name="baumer-temperature-monitor",
    version="1.0.0",
    description="Temperaturüberwachung für Baumer-Kameras im Netzwerk",
    author="Entwickler",
    packages=find_packages(),
    install_requires=[
        "PyQt5>=5.15.9",
        "matplotlib>=3.7.2",
        "numpy>=1.24.3",
        "requests>=2.31.0",
        "opencv-python>=********",
        "Pillow>=10.0.0",
        "pyqtgraph>=0.13.3"
    ],
    python_requires=">=3.8",
    entry_points={
        "console_scripts": [
            "baumer-temp-monitor=main:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)
