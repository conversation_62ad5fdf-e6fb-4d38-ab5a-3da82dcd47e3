/*
@ @licstart  The following is the entire license notice for the
JavaScript code in this file.

Copyright (C) 1997-2017 by <PERSON>

This program is free software; you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation; either version 2 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 GNU General Public License for more details.

You should have received a copy of the GNU General Public License along
with this program; if not, write to the Free Software Foundation, Inc.,
51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.

@licend  The above is the entire license notice
for the JavaScript code in this file
*/
var menudata={children:[
{text:"Home",url:"index.html"},
{text:"Topics",url:"usergroup0.html",children:[
{text:"Setup and Getting Started",url:"a00928.html"},
{text:"Connect to a Camera",url:"a00919.html"},
{text:"Working with Camera Features",url:"a00920.html"},
{text:"Images and Buffers Concepts",url:"a00923.html"},
{text:"Events and Plug and Play Concepts",url:"a00921.html"},
{text:"Trace Concepts",url:"a00927.html"},
{text:"Machine Vision Primer",url:"a00925.html"},
{text:"IP Configuration Tool",url:"a00918.html"},
{text:"Change Log",url:"a00917.html"}]},
{text:"Modules",url:"modules.html"},
{text:"Classes",url:"hierarchy.html"},
{text:"Examples",url:"examples.html"}]}
