VERSION = "1.6.0"

import sys
import os
import subprocess
import time
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox, QProgressBar)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal

class BaumerPracticalSolution(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"Baumer Temperatur-Monitor v{VERSION} - PRAKTISCHE LÖSUNG")
        self.setGeometry(100, 100, 1200, 800)
        
        # Status-Variablen
        self.python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        self.is_compatible = sys.version_info.major == 3 and 4 <= sys.version_info.minor <= 12
        
        # UI erstellen
        self.setup_ui()
        
        print(f"🚀 Baumer Praktische Lösung v{VERSION} gestartet")
        print(f"🐍 Python Version: {self.python_version}")
        print(f"✅ Kompatibel: {self.is_compatible}")

    def setup_ui(self):
        """Erstellt die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titel
        title = QLabel(f"🌡️ Baumer Temperatur-Monitor v{VERSION} - PRAKTISCHE LÖSUNG")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2E86AB; margin: 10px;")
        layout.addWidget(title)
        
        # Python-Version-Status
        python_info = QLabel(f"🐍 Python Version: {self.python_version}")
        if self.is_compatible:
            python_info.setStyleSheet("color: #28A745; font-weight: bold; margin: 5px;")
            status_text = "✅ Python-Version ist kompatibel mit Baumer neoAPI"
        else:
            python_info.setStyleSheet("color: #DC3545; font-weight: bold; margin: 5px;")
            status_text = "❌ Python 3.13 ist zu neu - Baumer neoAPI unterstützt nur 3.4-3.12"
        layout.addWidget(python_info)
        
        # Status-Info
        status = QLabel(status_text)
        status.setStyleSheet("padding: 10px; border-radius: 5px; margin: 5px;")
        layout.addWidget(status)
        
        # Tabs
        tabs = QTabWidget()
        layout.addWidget(tabs)
        
        # Tab 1: Sofort-Lösung
        immediate_tab = QWidget()
        tabs.addTab(immediate_tab, "🚀 Sofort-Lösung")
        immediate_layout = QVBoxLayout(immediate_tab)
        
        immediate_layout.addWidget(QLabel("SOFORTIGE LÖSUNGSOPTIONEN:"))
        
        # Option 1: Python 3.11 installieren
        option1_group = QGroupBox("Option 1: Python 3.11 installieren (EMPFOHLEN)")
        option1_layout = QVBoxLayout(option1_group)
        
        option1_text = QLabel("""
1. Python 3.11 von python.org herunterladen
2. Parallel zu Python 3.13 installieren
3. py -3.11 verwenden für Baumer-Projekte
4. Echte Baumer neoAPI installieren
5. Diese App mit Python 3.11 ausführen
""")
        option1_layout.addWidget(option1_text)
        
        self.download_python_btn = QPushButton("🌐 Python 3.11 Download-Seite öffnen")
        self.download_python_btn.clicked.connect(self.open_python_download)
        option1_layout.addWidget(self.download_python_btn)
        
        immediate_layout.addWidget(option1_group)
        
        # Option 2: Portable Python
        option2_group = QGroupBox("Option 2: Portable Python 3.11")
        option2_layout = QVBoxLayout(option2_group)
        
        option2_text = QLabel("""
1. WinPython 3.11 herunterladen (portable)
2. Entpacken in separaten Ordner
3. Baumer neoAPI in WinPython installieren
4. Nur für Baumer-Projekte verwenden
""")
        option2_layout.addWidget(option2_text)
        
        self.download_winpython_btn = QPushButton("🌐 WinPython Download-Seite öffnen")
        self.download_winpython_btn.clicked.connect(self.open_winpython_download)
        option2_layout.addWidget(self.download_winpython_btn)
        
        immediate_layout.addWidget(option2_group)
        
        # Option 3: Docker (für Fortgeschrittene)
        option3_group = QGroupBox("Option 3: Docker-Container (Fortgeschrittene)")
        option3_layout = QVBoxLayout(option3_group)
        
        option3_text = QLabel("""
1. Docker Desktop installieren
2. Python 3.11 Container erstellen
3. Baumer neoAPI im Container installieren
4. Kamera-Zugriff über Container
""")
        option3_layout.addWidget(option3_text)
        
        immediate_layout.addWidget(option3_group)
        
        # Tab 2: Schritt-für-Schritt
        steps_tab = QWidget()
        tabs.addTab(steps_tab, "📋 Schritt-für-Schritt")
        steps_layout = QVBoxLayout(steps_tab)
        
        steps_text = QTextEdit()
        steps_text.setReadOnly(True)
        steps_content = f"""
📋 DETAILLIERTE ANLEITUNG - PYTHON 3.11 INSTALLATION

SCHRITT 1: PYTHON 3.11 HERUNTERLADEN
- Gehen Sie zu: https://www.python.org/downloads/release/python-3118/
- Laden Sie herunter: "Windows installer (64-bit)"
- Datei: python-3.11.8-amd64.exe

SCHRITT 2: PYTHON 3.11 INSTALLIEREN
- Installer ausführen
- ✅ "Add Python 3.11 to PATH" aktivieren
- ✅ "Install for all users" aktivieren
- "Customize installation" wählen
- ✅ "py launcher" aktivieren
- Installation durchführen

SCHRITT 3: INSTALLATION TESTEN
Öffnen Sie neue Kommandozeile:
py -3.11 --version
(Sollte zeigen: Python 3.11.8)

SCHRITT 4: BAUMER NEOAPI INSTALLIEREN
py -3.11 -m pip install neoAPI/wheel/baumer_neoapi-1.5.0-cp34.cp35.cp36.cp37.cp38.cp39.cp310.cp311.cp312-none-win_amd64.whl

SCHRITT 5: INSTALLATION TESTEN
py -3.11 -c "import neoapi; print('Baumer neoAPI verfügbar:', hasattr(neoapi, 'Cam'))"

SCHRITT 6: DIESE APP MIT PYTHON 3.11 AUSFÜHREN
py -3.11 baumer_practical_solution.py

SCHRITT 7: TREIBER INSTALLIEREN
- USB-Kameras: neoAPI/drivers/USB/
- GigE-Kameras: neoAPI/drivers/filterdriver/
- Konfiguration: neoAPI/tools/gevipconfig.exe

AKTUELLE SITUATION:
Python Version: {self.python_version}
Kompatibel: {"✅ Ja" if self.is_compatible else "❌ Nein (zu neu)"}
Baumer neoAPI: {"✅ Installierbar" if self.is_compatible else "❌ Nicht kompatibel"}

WARUM PYTHON 3.13 NICHT FUNKTIONIERT:
- Baumer neoAPI wurde für Python 3.4-3.12 kompiliert
- Python 3.13 hat interne Änderungen
- Wheel-Dateien sind nicht kompatibel
- Baumer muss neue Version veröffentlichen
"""
        steps_text.setPlainText(steps_content)
        steps_layout.addWidget(steps_text)
        
        # Tab 3: Automatische Installation (wenn kompatibel)
        auto_tab = QWidget()
        tabs.addTab(auto_tab, "🤖 Auto-Installation")
        auto_layout = QVBoxLayout(auto_tab)
        
        if self.is_compatible:
            auto_layout.addWidget(QLabel("✅ Ihre Python-Version ist kompatibel!"))
            
            self.install_btn = QPushButton("🚀 Baumer neoAPI automatisch installieren")
            self.install_btn.clicked.connect(self.auto_install_neoapi)
            auto_layout.addWidget(self.install_btn)
            
            self.progress_bar = QProgressBar()
            auto_layout.addWidget(self.progress_bar)
            
            self.install_log = QTextEdit()
            self.install_log.setReadOnly(True)
            auto_layout.addWidget(self.install_log)
            
        else:
            auto_layout.addWidget(QLabel("❌ Python 3.13 ist nicht kompatibel"))
            auto_layout.addWidget(QLabel("Bitte installieren Sie Python 3.11 (siehe andere Tabs)"))
        
        # Tab 4: System-Info
        info_tab = QWidget()
        tabs.addTab(info_tab, "💻 System-Info")
        info_layout = QVBoxLayout(info_tab)
        
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        
        # System-Informationen sammeln
        import platform
        system_info = f"""
💻 SYSTEM-INFORMATIONEN

Python:
- Version: {self.python_version}
- Executable: {sys.executable}
- Platform: {platform.platform()}
- Architecture: {platform.architecture()[0]}

Baumer neoAPI Kompatibilität:
- Unterstützte Python-Versionen: 3.4, 3.5, 3.6, 3.7, 3.8, 3.9, 3.10, 3.11, 3.12
- Ihre Version: {self.python_version}
- Kompatibel: {"✅ Ja" if self.is_compatible else "❌ Nein"}

Verfügbare Wheel-Dateien:
- neoAPI/wheel/baumer_neoapi-1.5.0-cp27-none-win_amd64.whl (Python 2.7)
- neoAPI/wheel/baumer_neoapi-1.5.0-cp34.cp35.cp36.cp37.cp38.cp39.cp310.cp311.cp312-none-win_amd64.whl (Python 3.4-3.12)

Problem:
- Python 3.13 ist zu neu
- Keine Wheel-Datei für cp313 verfügbar
- Baumer muss neue Version veröffentlichen

Lösungen:
1. Python 3.11 parallel installieren (EMPFOHLEN)
2. WinPython 3.11 portable verwenden
3. Docker mit Python 3.11 verwenden
4. Warten auf Baumer neoAPI Update für Python 3.13
"""
        info_text.setPlainText(system_info)
        info_layout.addWidget(info_text)

    def open_python_download(self):
        """Öffnet Python 3.11 Download-Seite"""
        import webbrowser
        webbrowser.open("https://www.python.org/downloads/release/python-3118/")
        self.log_message("🌐 Python 3.11 Download-Seite geöffnet")

    def open_winpython_download(self):
        """Öffnet WinPython Download-Seite"""
        import webbrowser
        webbrowser.open("https://winpython.github.io/")
        self.log_message("🌐 WinPython Download-Seite geöffnet")

    def auto_install_neoapi(self):
        """Automatische Installation der Baumer neoAPI"""
        if not self.is_compatible:
            QMessageBox.warning(self, "Nicht kompatibel", "Python 3.13 ist nicht kompatibel!")
            return
            
        self.install_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.install_log.clear()
        
        try:
            self.install_log.append("🚀 Starte automatische Installation...")
            self.progress_bar.setValue(20)
            
            # Prüfe ob Wheel-Datei existiert
            wheel_path = "neoAPI/wheel/baumer_neoapi-1.5.0-cp34.cp35.cp36.cp37.cp38.cp39.cp310.cp311.cp312-none-win_amd64.whl"
            if not os.path.exists(wheel_path):
                self.install_log.append(f"❌ Wheel-Datei nicht gefunden: {wheel_path}")
                return
                
            self.install_log.append(f"✅ Wheel-Datei gefunden: {wheel_path}")
            self.progress_bar.setValue(40)
            
            # Installation ausführen
            self.install_log.append("📦 Installiere Baumer neoAPI...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", wheel_path, "--force-reinstall"
            ], capture_output=True, text=True)
            
            self.progress_bar.setValue(80)
            
            if result.returncode == 0:
                self.install_log.append("✅ Installation erfolgreich!")
                self.install_log.append(result.stdout)
                
                # Test der Installation
                self.install_log.append("🧪 Teste Installation...")
                try:
                    import neoapi
                    if hasattr(neoapi, 'Cam'):
                        self.install_log.append("✅ Baumer neoAPI erfolgreich installiert und getestet!")
                        QMessageBox.information(self, "Erfolg", "Baumer neoAPI erfolgreich installiert!")
                    else:
                        self.install_log.append("❌ Installation fehlerhaft - Cam-Klasse nicht verfügbar")
                except Exception as e:
                    self.install_log.append(f"❌ Test fehlgeschlagen: {e}")
            else:
                self.install_log.append("❌ Installation fehlgeschlagen!")
                self.install_log.append(result.stderr)
                
            self.progress_bar.setValue(100)
            
        except Exception as e:
            self.install_log.append(f"❌ Fehler: {e}")
        finally:
            self.install_btn.setEnabled(True)

    def log_message(self, message):
        """Fügt eine Nachricht zum Log hinzu"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = BaumerPracticalSolution()
    window.show()
    sys.exit(app.exec_())
