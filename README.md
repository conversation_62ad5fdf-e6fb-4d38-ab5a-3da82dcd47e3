# Baumer Kamera Temperatur Monitor

Ein Python-Programm zur Überwachung der Temperaturen von Baumer-Kameras (z.B. VCXG-13M) im Netzwerk.

## Features

- **Automatische Kamera-Erkennung** im Netzwerk via UPnP und IP-Scan
- **Minimale Belastung** der Kameras durch konfigurierbare Messintervalle
- **Grafische Temperaturverlauf-Anzeige** über mehrere Tage
- **Echtzeit-Monitoring** mit Warn- und Kritischen Schwellwerten
- **Datenbank-basierte Speicherung** für historische Auswertungen
- **Export-Funktion** für Temperatur-Daten
- **Benutzerfreundliche GUI** mit PyQt5

## Installation

### Voraussetzungen

- Python 3.8 oder höher
- Windows/Linux/macOS

### Abhängigkeiten installieren

```bash
pip install -r requirements.txt
```

Oder mit setup.py:

```bash
pip install -e .
```

## Verwendung

### Programm starten

```bash
python main.py
```

### Kamera-Konfiguration

1. **Automatische Erkennung**: Das Programm sucht automatisch nach Baumer-Kameras im Netzwerk
2. **Manuelle Suche**: Klicken Sie auf "Netzwerk scannen" für eine manuelle Suche
3. **Kamera auswählen**: Aktivieren Sie die Checkbox neben der gewünschten Kamera
4. **Temperatur-Monitoring**: Die Temperaturaufzeichnung startet automatisch

### GUI-Bereiche

#### Kameras-Tab
- Liste aller entdeckten Kameras
- Auswahl der zu überwachenden Kameras
- Live-Temperaturanzeige der verbundenen Kameras
- Status-Informationen

#### Temperaturverlauf-Tab
- Grafische Darstellung der Temperaturverläufe
- Zeitbereich-Auswahl (1 Stunde bis 30 Tage)
- Kamera-Filter für spezifische Anzeigen
- Warn- und Kritische Schwellwert-Linien

#### Einstellungen-Tab
- Messintervall konfigurieren (5-3600 Sekunden)
- Temperatur-Schwellwerte anpassen
- Netzwerk-Einstellungen
- Datenexport-Konfiguration

## Konfiguration

### Temperatur-Schwellwerte

- **Warnschwelle**: Standard 60°C (orange Anzeige)
- **Kritische Schwelle**: Standard 70°C (rote Anzeige)

### Messintervalle

- **Standard**: 30 Sekunden
- **Bereich**: 5 Sekunden bis 1 Stunde
- **Empfehlung**: Mindestens 30 Sekunden für minimale Kamera-Belastung

### Netzwerk-Konfiguration

- **Auto-Discovery**: Automatische Kamera-Suche alle 5 Minuten
- **IP-Bereich**: Standard 192.168.1.x
- **Ports**: 80, 8080, 554 (HTTP/RTSP)

## Datenbank

Das Programm verwendet SQLite für die Datenspeicherung:

- **Pfad**: `data/temperature_data.db`
- **Tabellen**: 
  - `cameras`: Kamera-Informationen
  - `temperature_readings`: Temperaturmessungen
- **Automatische Bereinigung**: Alte Daten werden nach 30 Tagen gelöscht

## API-Kompatibilität

Das Programm versucht verschiedene API-Endpunkte für Baumer-Kameras:

### Temperatur-Abfrage
- `/api/temperature`
- `/api/sensor/temperature`
- `/cgi-bin/temperature`
- `/temperature`

### Geräte-Informationen
- `/api/device/info`
- `/device_info`
- `/cgi-bin/device_info`

## Logging

- **Log-Dateien**: `logs/baumer_temp_YYYYMMDD.log`
- **Log-Level**: INFO (konfigurierbar)
- **Rotation**: Täglich neue Datei

## Fehlerbehebung

### Kameras werden nicht gefunden

1. Prüfen Sie die Netzwerk-Verbindung
2. Stellen Sie sicher, dass die Kameras HTTP-APIs aktiviert haben
3. Überprüfen Sie Firewall-Einstellungen
4. Versuchen Sie manuellen IP-Scan mit korrektem Netzwerk-Bereich

### Temperatur-Daten fehlen

1. Prüfen Sie die Kamera-API-Dokumentation
2. Testen Sie die Temperatur-Endpunkte manuell
3. Überprüfen Sie die Log-Dateien für Fehlermeldungen
4. Erhöhen Sie das Messintervall bei Verbindungsproblemen

### Performance-Probleme

1. Reduzieren Sie die Anzahl überwachter Kameras
2. Erhöhen Sie das Messintervall
3. Begrenzen Sie den Anzeige-Zeitbereich
4. Bereinigen Sie alte Daten in der Datenbank

## Entwicklung

### Projektstruktur

```
baumer-temperature-monitor/
├── main.py                 # Hauptprogramm
├── requirements.txt        # Python-Abhängigkeiten
├── setup.py               # Installation
├── src/
│   ├── core/              # Kern-Module
│   │   ├── config.py      # Konfiguration
│   │   └── database.py    # Datenbank-Interface
│   ├── camera/            # Kamera-Module
│   │   ├── discovery.py   # Kamera-Erkennung
│   │   └── baumer_camera.py # Kamera-Interface
│   └── gui/               # GUI-Module
│       ├── main_window.py # Hauptfenster
│       ├── camera_widget.py # Kamera-Widget
│       ├── temperature_plot.py # Plot-Widget
│       └── settings_widget.py # Einstellungen-Widget
├── data/                  # Datenbank-Dateien
└── logs/                  # Log-Dateien
```

### Tests ausführen

```bash
python -m pytest tests/
```

## Lizenz

Dieses Projekt steht unter der MIT-Lizenz.

## Support

Bei Fragen oder Problemen erstellen Sie bitte ein Issue im Repository oder kontaktieren Sie den Entwickler.
