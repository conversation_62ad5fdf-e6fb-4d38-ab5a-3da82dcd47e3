#!/usr/bin/env python3
"""
Test-Tool für spezifische Baumer-Kamera IP: ***************
"""

import sys
import os
import socket
import requests
import threading
import time
from datetime import datetime

# Füge src-Verzeichnis hinzu
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ip_connectivity(ip):
    """Testet grundlegende IP-Konnektivität"""
    print(f"\n=== TESTE IP: {ip} ===")
    
    # 1. Ping-Test (Socket-basiert)
    print("1. Socket-Test...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        
        # Teste verschiedene Ports
        ports = [80, 8080, 554, 443, 23, 21]
        open_ports = []
        
        for port in ports:
            try:
                result = sock.connect_ex((ip, port))
                if result == 0:
                    open_ports.append(port)
                    print(f"   ✓ Port {port} OFFEN")
                else:
                    print(f"   ✗ Port {port} geschlossen")
            except:
                print(f"   ✗ Port {port} Fehler")
                
        sock.close()
        
        if open_ports:
            print(f"   → Offene Ports gefunden: {open_ports}")
        else:
            print("   → Keine offenen Ports gefunden")
            
    except Exception as e:
        print(f"   ✗ Socket-Test Fehler: {e}")
    
    # 2. HTTP-Test
    print("\n2. HTTP-Test...")
    http_ports = [80, 8080]
    
    for port in http_ports:
        try:
            url = f"http://{ip}:{port}"
            print(f"   Teste {url}...")
            
            response = requests.get(url, timeout=3)
            print(f"   ✓ HTTP {port}: Status {response.status_code}")
            
            if response.status_code == 200:
                content = response.text[:200]
                print(f"   → Content: {content}...")
                
                # Suche nach Baumer-spezifischen Begriffen
                baumer_keywords = ['baumer', 'vcxg', 'camera', 'imaging']
                found_keywords = [kw for kw in baumer_keywords if kw.lower() in content.lower()]
                if found_keywords:
                    print(f"   ✓ Baumer-Keywords gefunden: {found_keywords}")
                    
        except requests.RequestException as e:
            print(f"   ✗ HTTP {port} Fehler: {e}")
    
    # 3. Baumer-spezifische Tests
    print("\n3. Baumer-spezifische API-Tests...")
    baumer_endpoints = [
        "/api/device",
        "/device_info", 
        "/cgi-bin/device_info",
        "/api/temperature",
        "/temperature",
        "/api/status"
    ]
    
    for endpoint in baumer_endpoints:
        try:
            url = f"http://{ip}{endpoint}"
            response = requests.get(url, timeout=2)
            
            if response.status_code == 200:
                print(f"   ✓ {endpoint}: OK")
                content = response.text[:100]
                print(f"     → {content}...")
            else:
                print(f"   ✗ {endpoint}: Status {response.status_code}")
                
        except requests.RequestException:
            print(f"   ✗ {endpoint}: Nicht erreichbar")

def scan_169_254_network():
    """Scannt das komplette 169.254.x.x Netzwerk"""
    print("\n=== NETZWERK-SCAN 169.254.x.x ===")
    
    found_devices = []
    
    def test_ip_range(start, end):
        for i in range(start, end + 1):
            ip = f"169.254.174.{i}"
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)
                result = sock.connect_ex((ip, 80))
                sock.close()
                
                if result == 0:
                    found_devices.append(ip)
                    print(f"✓ Gerät gefunden: {ip}")
                    
            except:
                pass
    
    print("Scanne *************-254...")
    
    # Verwende Threads für schnelleren Scan
    threads = []
    for start in range(1, 255, 50):
        end = min(start + 49, 254)
        thread = threading.Thread(target=test_ip_range, args=(start, end))
        threads.append(thread)
        thread.start()
    
    # Warte auf alle Threads
    for thread in threads:
        thread.join()
    
    print(f"\nScan abgeschlossen. Gefundene Geräte: {len(found_devices)}")
    for device in found_devices:
        print(f"  → {device}")
    
    return found_devices

def main():
    """Hauptfunktion"""
    print("Baumer-Kamera IP-Test Tool")
    print("=" * 40)
    print(f"Zeitstempel: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Teste spezifische IP
    specific_ip = "***************"
    test_ip_connectivity(specific_ip)
    
    # Scanne Netzwerk-Bereich
    print("\n" + "=" * 40)
    found_devices = scan_169_254_network()
    
    # Teste alle gefundenen Geräte
    if found_devices:
        print("\n=== TESTE ALLE GEFUNDENEN GERÄTE ===")
        for device in found_devices:
            test_ip_connectivity(device)
            print("-" * 30)
    
    print("\n" + "=" * 40)
    print("Test abgeschlossen")

if __name__ == "__main__":
    main()
