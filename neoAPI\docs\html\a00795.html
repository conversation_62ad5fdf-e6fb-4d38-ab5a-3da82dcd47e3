<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: neoapi.ColorMatrix Class Reference</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="a00091.html">neoapi</a></li><li class="navelem"><a class="el" href="a00795.html">ColorMatrix</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="a00792.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">neoapi.ColorMatrix Class Reference<div class="ingroups"><a class="el" href="a00090.html">All Cam Classes</a> &#124; <a class="el" href="a00086.html">Cam Interface Classes</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Class for the Color Transformation Matrix This class provides methods to configure the Color Matrix for color cameras.  
 <a href="a00795.html#details">More...</a></p>
<div id="dynsection-0" onclick="return toggleVisibility(this)" class="dynheader closed" style="cursor:pointer;">
  <img id="dynsection-0-trigger" src="closed.png" alt="+"/> Inheritance diagram for neoapi.ColorMatrix:</div>
<div id="dynsection-0-summary" class="dynsummary" style="display:block;">
</div>
<div id="dynsection-0-content" class="dyncontent" style="display:none;">
 <div class="center">
  <img src="a00795.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a77457432abbcb3cd3fab77bffbb548c6"><td class="memItemLeft" align="right" valign="top">def&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00795.html#a77457432abbcb3cd3fab77bffbb548c6">__init__</a> (self, *args)</td></tr>
<tr class="separator:a77457432abbcb3cd3fab77bffbb548c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad2c91d569c5ad07fa4b48464d6d27b1b"><td class="memItemLeft" align="right" valign="top">&quot;float&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00795.html#ad2c91d569c5ad07fa4b48464d6d27b1b">GetValue</a> (self, &quot;int&quot; gain_selector)</td></tr>
<tr class="memdesc:ad2c91d569c5ad07fa4b48464d6d27b1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the value of a matrix element.  <a href="#ad2c91d569c5ad07fa4b48464d6d27b1b">More...</a><br /></td></tr>
<tr class="separator:ad2c91d569c5ad07fa4b48464d6d27b1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37db09b0787505c9db9d383279fb54e2"><td class="memItemLeft" align="right" valign="top">&quot;ColorMatrix&quot;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00795.html#a37db09b0787505c9db9d383279fb54e2">SetValue</a> (self, &quot;int&quot; gain_selector, &quot;float&quot; value)</td></tr>
<tr class="memdesc:a37db09b0787505c9db9d383279fb54e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the value of a matrix element.  <a href="#a37db09b0787505c9db9d383279fb54e2">More...</a><br /></td></tr>
<tr class="separator:a37db09b0787505c9db9d383279fb54e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a4dc54f7673d75fb5d0560c7b93c910e0"><td class="memItemLeft" align="right" valign="top"><a id="a4dc54f7673d75fb5d0560c7b93c910e0"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Gain_Gain00</b> = _neoapi.ColorMatrix_Gain_Gain00</td></tr>
<tr class="separator:a4dc54f7673d75fb5d0560c7b93c910e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1b892990f72c8d33d0bc8da0fa16bd4"><td class="memItemLeft" align="right" valign="top"><a id="ab1b892990f72c8d33d0bc8da0fa16bd4"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Gain_Gain01</b> = _neoapi.ColorMatrix_Gain_Gain01</td></tr>
<tr class="separator:ab1b892990f72c8d33d0bc8da0fa16bd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3a3c971728a121a84496e51dfee1b584"><td class="memItemLeft" align="right" valign="top"><a id="a3a3c971728a121a84496e51dfee1b584"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Gain_Gain02</b> = _neoapi.ColorMatrix_Gain_Gain02</td></tr>
<tr class="separator:a3a3c971728a121a84496e51dfee1b584"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9b66b01c76530933eccfedce25cbb0b"><td class="memItemLeft" align="right" valign="top"><a id="ac9b66b01c76530933eccfedce25cbb0b"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Gain_Gain10</b> = _neoapi.ColorMatrix_Gain_Gain10</td></tr>
<tr class="separator:ac9b66b01c76530933eccfedce25cbb0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a912001ae39ff89ee86bfde29556c985d"><td class="memItemLeft" align="right" valign="top"><a id="a912001ae39ff89ee86bfde29556c985d"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Gain_Gain11</b> = _neoapi.ColorMatrix_Gain_Gain11</td></tr>
<tr class="separator:a912001ae39ff89ee86bfde29556c985d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa83b9bd7794e7f52e8983a70e2df2fd8"><td class="memItemLeft" align="right" valign="top"><a id="aa83b9bd7794e7f52e8983a70e2df2fd8"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Gain_Gain12</b> = _neoapi.ColorMatrix_Gain_Gain12</td></tr>
<tr class="separator:aa83b9bd7794e7f52e8983a70e2df2fd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c52daf7747be9b8eff08c085129fc85"><td class="memItemLeft" align="right" valign="top"><a id="a7c52daf7747be9b8eff08c085129fc85"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Gain_Gain20</b> = _neoapi.ColorMatrix_Gain_Gain20</td></tr>
<tr class="separator:a7c52daf7747be9b8eff08c085129fc85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcd5538d163f61ef64fb11256bbfe2c6"><td class="memItemLeft" align="right" valign="top"><a id="adcd5538d163f61ef64fb11256bbfe2c6"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Gain_Gain21</b> = _neoapi.ColorMatrix_Gain_Gain21</td></tr>
<tr class="separator:adcd5538d163f61ef64fb11256bbfe2c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7deaf969b726a22d1534fc4feb2757fe"><td class="memItemLeft" align="right" valign="top"><a id="a7deaf969b726a22d1534fc4feb2757fe"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>Gain_Gain22</b> = _neoapi.ColorMatrix_Gain_Gain22</td></tr>
<tr class="separator:a7deaf969b726a22d1534fc4feb2757fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:aec5adc25ece28bd03a426c7cbe6afdc3"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00795.html#aec5adc25ece28bd03a426c7cbe6afdc3">thisown</a> = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td></tr>
<tr class="memdesc:aec5adc25ece28bd03a426c7cbe6afdc3"><td class="mdescLeft">&#160;</td><td class="mdescRight">The membership flag.  <a href="#aec5adc25ece28bd03a426c7cbe6afdc3">More...</a><br /></td></tr>
<tr class="separator:aec5adc25ece28bd03a426c7cbe6afdc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Class for the Color Transformation Matrix This class provides methods to configure the Color Matrix for color cameras. </p>
<p>The Color Matrix can be used to achieve ideal color representation of an image during de-bayering. After setting suitable values for the color matrix elements, you can pass the instance to a <a class="el" href="a00799.html" title="Image post processing settings This class provides methods to configure image conversions.">neoapi.ConverterSettings</a> instance which can than be used with the <a class="el" href="a00855.html#af9fd5b5a5d619eb69e2c6c49e51c5e70">neoapi.Image.Convert</a> method to apply the settings to an image. </p><dl class="section note"><dt>Note</dt><dd>The elements are named according to the SFNC USAGE: <div class="fragment"><div class="line">matrix = <a class="code" href="a00795.html">neoapi.ColorMatrix</a>()                          <span class="comment"># Create a ColorMatrix object</span></div><div class="line">settings = <a class="code" href="a00799.html">neoapi.ConverterSettings</a>()                  <span class="comment"># Create a ConverterSettings object</span></div><div class="line">matrix.SetValue(neoapi.ColorMatrix.Gain_Gain00, -0.3)  <span class="comment"># Set a single value</span></div><div class="line">settings.SetColorTransformationMatrix(matrix)          <span class="comment"># Set the ColorMatrix to ConverterSettings</span></div></div><!-- fragment --> </dd></dl>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a77457432abbcb3cd3fab77bffbb548c6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77457432abbcb3cd3fab77bffbb548c6">&#9670;&nbsp;</a></span>__init__()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">def neoapi.ColorMatrix.__init__ </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">*&#160;</td>
          <td class="paramname"><em>args</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<pre class="fragment">   \brief     Constructor
</pre> <dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">*args</td><td>Arguments:<br />
 <b>object</b> (optional) A <a class="el" href="a00795.html" title="Class for the Color Transformation Matrix This class provides methods to configure the Color Matrix f...">ColorMatrix</a> object </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ad2c91d569c5ad07fa4b48464d6d27b1b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad2c91d569c5ad07fa4b48464d6d27b1b">&#9670;&nbsp;</a></span>GetValue()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;float&quot; neoapi.ColorMatrix.GetValue </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>gain_selector</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Get the value of a matrix element. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">gain_selector</td><td>Selects a component. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The value of the selected component </dd></dl>

</div>
</div>
<a id="a37db09b0787505c9db9d383279fb54e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a37db09b0787505c9db9d383279fb54e2">&#9670;&nbsp;</a></span>SetValue()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"> &quot;ColorMatrix&quot; neoapi.ColorMatrix.SetValue </td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>self</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;int&quot;&#160;</td>
          <td class="paramname"><em>gain_selector</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&quot;float&quot;&#160;</td>
          <td class="paramname"><em>value</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set the value of a matrix element. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramdir">[in]</td><td class="paramname">gain_selector</td><td>Selects a component. </td></tr>
    <tr><td class="paramdir">[in]</td><td class="paramname">value</td><td>The new value for this component. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The <a class="el" href="a00799.html" title="Image post processing settings This class provides methods to configure image conversions.">ConverterSettings</a> object </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="aec5adc25ece28bd03a426c7cbe6afdc3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aec5adc25ece28bd03a426c7cbe6afdc3">&#9670;&nbsp;</a></span>thisown</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">neoapi.ColorMatrix.thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc=&quot;The membership flag&quot;)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>The membership flag. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>neoapi.py</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
