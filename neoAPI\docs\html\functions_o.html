<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Class Members</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_o"></a>- o -</h3><ul>
<li>OffsetX()
: <a class="el" href="a00763.html#a43b9cc5cf1a3d122e0e74dee7d2f97fc">neoapi.FeatureAccess</a>
</li>
<li>OffsetY()
: <a class="el" href="a00763.html#a6d40697a3a89b0598528feb358a1aa1d">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerDisconnect()
: <a class="el" href="a00763.html#a2f97f442d92492b9905aab161039ddb2">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerFamilyName()
: <a class="el" href="a00763.html#a121de104e96db60a6914719f0f4dc6cd">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerFirmwareVersion()
: <a class="el" href="a00763.html#a6a4705dfde9e680c83460e59a71edba7">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerInitialize()
: <a class="el" href="a00763.html#ace9a092d8e082ef052eb71054296ff41">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerModelName()
: <a class="el" href="a00763.html#a1e4c563a62ea58b80ebff525c311446e">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerSelector()
: <a class="el" href="a00763.html#a1f5e472cf6f7ebe265cde03cb67c5cb5">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerSerialNumber()
: <a class="el" href="a00763.html#a11369f93de161e9e24902223a4c0f21f">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerStatus()
: <a class="el" href="a00763.html#a08a2e4ae72147ee16d8b70c3cbaae8ad">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerTemperature()
: <a class="el" href="a00763.html#a76d60448b60c6ae59b2465bac9a2d5d7">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerThermalCompensation()
: <a class="el" href="a00763.html#a598da9a9684abdb6c750c6b85af4fd58">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerVendorName()
: <a class="el" href="a00763.html#a612473ec481a796862d45ba64ec05aa4">neoapi.FeatureAccess</a>
</li>
<li>OpticControllerVersion()
: <a class="el" href="a00763.html#a4723614cde4613c6f4633fb24e2aaf5a">neoapi.FeatureAccess</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
