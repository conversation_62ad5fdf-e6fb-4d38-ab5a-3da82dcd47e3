<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented namespace members with links to the namespaces they belong to:</div>

<h3><a id="index_s"></a>- s -</h3><ul>
<li>SensorADDigitization_Bpp10
: <a class="el" href="a00091.html#a96eea6897be83fc3fd1ef9b53df3d4dc">neoapi</a>
</li>
<li>SensorADDigitization_Bpp12
: <a class="el" href="a00091.html#a3e2f745c4bc76533aba35982586389d1">neoapi</a>
</li>
<li>SensorADDigitization_Bpp8
: <a class="el" href="a00091.html#a45ee474aee73d7946dcafd69f233d5d5">neoapi</a>
</li>
<li>SensorCutConfigurationMode_Off
: <a class="el" href="a00091.html#a2e6fbad1dc4401041ef230d5da5ba18c">neoapi</a>
</li>
<li>SensorCutConfigurationMode_On
: <a class="el" href="a00091.html#a3578916007612f0905dc70da054a2d11">neoapi</a>
</li>
<li>SensorDigitizationTaps_Eight
: <a class="el" href="a00091.html#a5347bd9f65b4c51631b611e94e220f75">neoapi</a>
</li>
<li>SensorDigitizationTaps_Four
: <a class="el" href="a00091.html#a479285a9446800d4e2153c7fa44bb074">neoapi</a>
</li>
<li>SensorDigitizationTaps_One
: <a class="el" href="a00091.html#ab82b8ae65ef230d18385a5688b026b36">neoapi</a>
</li>
<li>SensorDigitizationTaps_Ten
: <a class="el" href="a00091.html#a3bf550d6d7cc947a917f60098b98f512">neoapi</a>
</li>
<li>SensorDigitizationTaps_Three
: <a class="el" href="a00091.html#a19d63efab9044afc7ebe779a9ce79a3f">neoapi</a>
</li>
<li>SensorDigitizationTaps_Two
: <a class="el" href="a00091.html#a60cf48ebeaf7ac414612e20c8cb9f005">neoapi</a>
</li>
<li>SensorShutterMode_Global
: <a class="el" href="a00091.html#a723a2d0520707ace0dc520e54d66aa97">neoapi</a>
</li>
<li>SensorShutterMode_GlobalReset
: <a class="el" href="a00091.html#a17c1de5eb611dd03f08181f1f04f454e">neoapi</a>
</li>
<li>SensorShutterMode_Rolling
: <a class="el" href="a00091.html#a0d7d7125beb8fde7706f8d8dbebc51eb">neoapi</a>
</li>
<li>SensorTaps_Eight
: <a class="el" href="a00091.html#a8f19a384bd6a7a3070350f869aa1ad21">neoapi</a>
</li>
<li>SensorTaps_Four
: <a class="el" href="a00091.html#a637ad535f809c896b32a7759cbdf89e0">neoapi</a>
</li>
<li>SensorTaps_One
: <a class="el" href="a00091.html#a429bbc902a4ce832173749a2171efa4a">neoapi</a>
</li>
<li>SensorTaps_Ten
: <a class="el" href="a00091.html#aa5cbe56297c248b4154abafbe47bdfab">neoapi</a>
</li>
<li>SensorTaps_Three
: <a class="el" href="a00091.html#a0d45481a13699317fa17f85735ae4dac">neoapi</a>
</li>
<li>SensorTaps_Two
: <a class="el" href="a00091.html#a74e54da362dfce9e94047dd3ebde7aa7">neoapi</a>
</li>
<li>SequencerConfigurationMode_Off
: <a class="el" href="a00091.html#a635ffb6a182765683d7e31eed2ad4992">neoapi</a>
</li>
<li>SequencerConfigurationMode_On
: <a class="el" href="a00091.html#a52187cd65907573ba1ddaa5244113da0">neoapi</a>
</li>
<li>SequencerFeatureSelector_CounterDuration
: <a class="el" href="a00091.html#a140f053a70a8483999ef23c7ce95d1c8">neoapi</a>
</li>
<li>SequencerFeatureSelector_CounterEventActivation
: <a class="el" href="a00091.html#a67d5bf7d5e0ff31503b05638d6b1920b">neoapi</a>
</li>
<li>SequencerFeatureSelector_CounterEventSource
: <a class="el" href="a00091.html#a6fae58453323ae1da5472c4ff55e4fd6">neoapi</a>
</li>
<li>SequencerFeatureSelector_CounterResetActivation
: <a class="el" href="a00091.html#a5e26ec43d503a5a038a4ad3f33dccf9b">neoapi</a>
</li>
<li>SequencerFeatureSelector_CounterResetSource
: <a class="el" href="a00091.html#a1c464f5371244b251c3069ac66e21746">neoapi</a>
</li>
<li>SequencerFeatureSelector_DeviceSpecificFeatureList
: <a class="el" href="a00091.html#a062897c1940d4f4e12c23e82074b6df9">neoapi</a>
</li>
<li>SequencerFeatureSelector_ExposureMode
: <a class="el" href="a00091.html#ae593a22c813cb15a7c9f53bc16ca6dce">neoapi</a>
</li>
<li>SequencerFeatureSelector_ExposureTime
: <a class="el" href="a00091.html#a9ee5ae77c991eb3acb8784a8c4185a26">neoapi</a>
</li>
<li>SequencerFeatureSelector_Gain
: <a class="el" href="a00091.html#aadc78be967a7f7d1f4a25947294c5e3f">neoapi</a>
</li>
<li>SequencerFeatureSelector_Height
: <a class="el" href="a00091.html#a89c888079544ba4e8446d6c0f57e5c22">neoapi</a>
</li>
<li>SequencerFeatureSelector_OffsetX
: <a class="el" href="a00091.html#adc7ac2636727de1f5774a8fd3ebd81f8">neoapi</a>
</li>
<li>SequencerFeatureSelector_OffsetY
: <a class="el" href="a00091.html#a60c34c921b030a87b7c24547a2a363d2">neoapi</a>
</li>
<li>SequencerFeatureSelector_TriggerMode
: <a class="el" href="a00091.html#aea183eae3b6a46df810b20348aaac873">neoapi</a>
</li>
<li>SequencerFeatureSelector_UserOutputValue
: <a class="el" href="a00091.html#a83b02aa07c9e4c5b276f37c68ac95fda">neoapi</a>
</li>
<li>SequencerFeatureSelector_UserOutputValueAll
: <a class="el" href="a00091.html#ab9ab36d8748d11db8018ffd8c8b75b46">neoapi</a>
</li>
<li>SequencerFeatureSelector_Width
: <a class="el" href="a00091.html#a82732e5a372d0fb7f2535ec90d1b0598">neoapi</a>
</li>
<li>SequencerMode_Off
: <a class="el" href="a00091.html#add024abc47e8668be997ca507617cfdf">neoapi</a>
</li>
<li>SequencerMode_On
: <a class="el" href="a00091.html#a56cd14dfad06d423b937df2df8e0640d">neoapi</a>
</li>
<li>SequencerTriggerActivation_AnyEdge
: <a class="el" href="a00091.html#ab0ea069e3538fe4f1cb8ff23751d5ba5">neoapi</a>
</li>
<li>SequencerTriggerActivation_FallingEdge
: <a class="el" href="a00091.html#a15039d9609a6b48775c84c5d47c5789e">neoapi</a>
</li>
<li>SequencerTriggerActivation_LevelHigh
: <a class="el" href="a00091.html#adb6ab4ba2e3d18ef082819c99fe6c5e7">neoapi</a>
</li>
<li>SequencerTriggerActivation_LevelLow
: <a class="el" href="a00091.html#a414dfe2198a4ac1739f80cc6e5fa6faf">neoapi</a>
</li>
<li>SequencerTriggerActivation_RisingEdge
: <a class="el" href="a00091.html#a7bb865edae07bdb652176ac0d78af64a">neoapi</a>
</li>
<li>SequencerTriggerSource_AcquisitionEnd
: <a class="el" href="a00091.html#a5a1821da45ac98a3c3b0a4f5d3c7130f">neoapi</a>
</li>
<li>SequencerTriggerSource_AcquisitionStart
: <a class="el" href="a00091.html#ae5291167a9e07b179baac4954bffdcea">neoapi</a>
</li>
<li>SequencerTriggerSource_AcquisitionTrigger
: <a class="el" href="a00091.html#a0c058b4940636447fab19ab195ea7e9f">neoapi</a>
</li>
<li>SequencerTriggerSource_AcquisitionTriggerMissed
: <a class="el" href="a00091.html#a04765885483bc451ed96dd7d06897fdd">neoapi</a>
</li>
<li>SequencerTriggerSource_Action0
: <a class="el" href="a00091.html#a1967a599c8cdb5d0441a5aa409b5f849">neoapi</a>
</li>
<li>SequencerTriggerSource_Action1
: <a class="el" href="a00091.html#aad547db17d90eeaed5310f64e6ef1e43">neoapi</a>
</li>
<li>SequencerTriggerSource_Action2
: <a class="el" href="a00091.html#aeeeff18356b60319b5b1b169709e7e3e">neoapi</a>
</li>
<li>SequencerTriggerSource_CC1
: <a class="el" href="a00091.html#acf0a50a9e9208382f775a8094bf85125">neoapi</a>
</li>
<li>SequencerTriggerSource_CC2
: <a class="el" href="a00091.html#a21c09297ff04cf24e906828e83291ec6">neoapi</a>
</li>
<li>SequencerTriggerSource_CC3
: <a class="el" href="a00091.html#a0ad68163bcac14dc2000e0b1addaf08b">neoapi</a>
</li>
<li>SequencerTriggerSource_CC4
: <a class="el" href="a00091.html#a02bfdee6ec0a86ae21ff18d89af9fc00">neoapi</a>
</li>
<li>SequencerTriggerSource_Counter0End
: <a class="el" href="a00091.html#ad253b448756ffad6e38c0a0791b0b711">neoapi</a>
</li>
<li>SequencerTriggerSource_Counter0Start
: <a class="el" href="a00091.html#a8637ada7ee3998ae5fec6d0c3dbf6c53">neoapi</a>
</li>
<li>SequencerTriggerSource_Counter1End
: <a class="el" href="a00091.html#aa979317d6792d51a0134a0154a1400f1">neoapi</a>
</li>
<li>SequencerTriggerSource_Counter1Start
: <a class="el" href="a00091.html#ac251c151fb26fc0df04e04db9645f6be">neoapi</a>
</li>
<li>SequencerTriggerSource_Counter2End
: <a class="el" href="a00091.html#a03a09e1c2642789836ab5d355b89fee6">neoapi</a>
</li>
<li>SequencerTriggerSource_Counter2Start
: <a class="el" href="a00091.html#a85265b7f09277cf0f0ba16fba22c338a">neoapi</a>
</li>
<li>SequencerTriggerSource_Encoder0
: <a class="el" href="a00091.html#a95c294e2f04552a7e486e4e6590233e8">neoapi</a>
</li>
<li>SequencerTriggerSource_Encoder1
: <a class="el" href="a00091.html#a31b33f9b890727fb9c4cbfae41259aca">neoapi</a>
</li>
<li>SequencerTriggerSource_Encoder2
: <a class="el" href="a00091.html#a0256ea72291733bd04912dfa42893ff3">neoapi</a>
</li>
<li>SequencerTriggerSource_ExposureActive
: <a class="el" href="a00091.html#ab21a510f196cbb82b3d1d522feda5e3d">neoapi</a>
</li>
<li>SequencerTriggerSource_ExposureEnd
: <a class="el" href="a00091.html#a66f583d3111633e0a6a434cc5450bdb1">neoapi</a>
</li>
<li>SequencerTriggerSource_ExposureStart
: <a class="el" href="a00091.html#a153ac0b73595687360f86d3988503167">neoapi</a>
</li>
<li>SequencerTriggerSource_FrameBurstEnd
: <a class="el" href="a00091.html#a0978c3f8389a3e1debe27eee6ad22143">neoapi</a>
</li>
<li>SequencerTriggerSource_FrameBurstStart
: <a class="el" href="a00091.html#aa825611d1d814caaa0f0e20aa5dab3d2">neoapi</a>
</li>
<li>SequencerTriggerSource_FrameEnd
: <a class="el" href="a00091.html#a4f4ca0602dfd3f1346e6cfc05485c923">neoapi</a>
</li>
<li>SequencerTriggerSource_FrameStart
: <a class="el" href="a00091.html#aef8dbff26c1ccd8e408d57b76702ddb8">neoapi</a>
</li>
<li>SequencerTriggerSource_FrameTrigger
: <a class="el" href="a00091.html#a6797b087cbfb0048a7781c51c1579048">neoapi</a>
</li>
<li>SequencerTriggerSource_FrameTriggerMissed
: <a class="el" href="a00091.html#a01542f74eaa995c6ad8c7c1c46352f88">neoapi</a>
</li>
<li>SequencerTriggerSource_Line0
: <a class="el" href="a00091.html#aa98824946e2eea104795fdcfb42da919">neoapi</a>
</li>
<li>SequencerTriggerSource_Line1
: <a class="el" href="a00091.html#a6817b322160b32394d4a1dd4880d871f">neoapi</a>
</li>
<li>SequencerTriggerSource_Line2
: <a class="el" href="a00091.html#acdc6c88128f7585464fbfae97d07ee53">neoapi</a>
</li>
<li>SequencerTriggerSource_LinkTrigger0
: <a class="el" href="a00091.html#a5e2cb36b25603c238055808459ea5d4d">neoapi</a>
</li>
<li>SequencerTriggerSource_LinkTrigger1
: <a class="el" href="a00091.html#a9f935e3d097a64e71c6391ce3623ca8c">neoapi</a>
</li>
<li>SequencerTriggerSource_LinkTrigger2
: <a class="el" href="a00091.html#a3ee1cb1eb768f3b3166ac8d4f54e77f6">neoapi</a>
</li>
<li>SequencerTriggerSource_LogicBlock0
: <a class="el" href="a00091.html#a7ccbd8ccf7930b2e3f21698d078ec2ab">neoapi</a>
</li>
<li>SequencerTriggerSource_LogicBlock1
: <a class="el" href="a00091.html#a758f182853c6285eabee0ca845f35a3d">neoapi</a>
</li>
<li>SequencerTriggerSource_LogicBlock2
: <a class="el" href="a00091.html#aab1a8cfe29d1a145daa79a9d7ab37015">neoapi</a>
</li>
<li>SequencerTriggerSource_Off
: <a class="el" href="a00091.html#acffb1f6b8915996f59b7ca3c46a6363a">neoapi</a>
</li>
<li>SequencerTriggerSource_ReadOutActive
: <a class="el" href="a00091.html#a810c22c23418b6be578c40b9e5f3610e">neoapi</a>
</li>
<li>SequencerTriggerSource_SoftwareSignal0
: <a class="el" href="a00091.html#aaa68689584a8bebedf06cb4ff9ef7ab0">neoapi</a>
</li>
<li>SequencerTriggerSource_SoftwareSignal1
: <a class="el" href="a00091.html#a1ac4b264a7684bd836f4f1d89923aaa9">neoapi</a>
</li>
<li>SequencerTriggerSource_SoftwareSignal2
: <a class="el" href="a00091.html#ae16f7b3afa38a5dd6ab08c61cd89ab56">neoapi</a>
</li>
<li>SequencerTriggerSource_Timer0End
: <a class="el" href="a00091.html#a8a8e63de34af4a621a7817e69ae006b0">neoapi</a>
</li>
<li>SequencerTriggerSource_Timer0Start
: <a class="el" href="a00091.html#aa6b77e0611e15b104a11ac871db22b44">neoapi</a>
</li>
<li>SequencerTriggerSource_Timer1End
: <a class="el" href="a00091.html#a2829d5a36bbd9fd9929450aad06ade24">neoapi</a>
</li>
<li>SequencerTriggerSource_Timer1Start
: <a class="el" href="a00091.html#a9127df0a11b098343c8701557eda6f9c">neoapi</a>
</li>
<li>SequencerTriggerSource_Timer2End
: <a class="el" href="a00091.html#a3c92f122d155539867658e2f7eea22e5">neoapi</a>
</li>
<li>SequencerTriggerSource_Timer2Start
: <a class="el" href="a00091.html#a08430c167e01ac4276929c80b3346520">neoapi</a>
</li>
<li>SequencerTriggerSource_UserOutput0
: <a class="el" href="a00091.html#a36ac4b073030f364634c9c2f2d993669">neoapi</a>
</li>
<li>SequencerTriggerSource_UserOutput1
: <a class="el" href="a00091.html#aa39f314b3d437f04421120c4fcb54dbe">neoapi</a>
</li>
<li>SequencerTriggerSource_UserOutput2
: <a class="el" href="a00091.html#a2ace0bf6e90688eb0950eee74216ffc9">neoapi</a>
</li>
<li>ShadingSelector_Pixel0
: <a class="el" href="a00091.html#a91bbbcd15a75bc7f0cb39017f8bbaa61">neoapi</a>
</li>
<li>ShadingSelector_Pixel1
: <a class="el" href="a00091.html#a4d1805ffe544ce8a5b5bd66b1720b849">neoapi</a>
</li>
<li>SharpeningMode_ActiveNoiseReduction
: <a class="el" href="a00091.html#aad02b48ff610ba050d8a26bd73bfc119">neoapi</a>
</li>
<li>SharpeningMode_AdaptiveSharpening
: <a class="el" href="a00091.html#a1c4a715442427e955518318037b59289">neoapi</a>
</li>
<li>SharpeningMode_GlobalSharpening
: <a class="el" href="a00091.html#a5ffb65e476f0bfb87272043ab7cbd589">neoapi</a>
</li>
<li>SharpeningMode_Off
: <a class="el" href="a00091.html#ab6931180bbd68a5ab25e9916b7559ca6">neoapi</a>
</li>
<li>SIControl_StreamDisabled
: <a class="el" href="a00091.html#a23e173f34f22c125eb5b5cf86245cf14">neoapi</a>
</li>
<li>SIControl_StreamEnabled
: <a class="el" href="a00091.html#a2e5dd5fff737b7ac765d6c7e2fc223ff">neoapi</a>
</li>
<li>SourceID_Sensor1
: <a class="el" href="a00091.html#a2b3c0aa7a34e98451d2f92a903bbcd87">neoapi</a>
</li>
<li>SourceID_Sensor2
: <a class="el" href="a00091.html#a650b542c0967c8effc84ac30a0860e73">neoapi</a>
</li>
<li>SourceSelector_All
: <a class="el" href="a00091.html#acbb60f8fc553087dbc5cace4fab68d15">neoapi</a>
</li>
<li>SourceSelector_Source0
: <a class="el" href="a00091.html#a4570d86b3f1c95cafe174e856bea1939">neoapi</a>
</li>
<li>SourceSelector_Source1
: <a class="el" href="a00091.html#a76f8d445b20a19d57e2c071f663514c5">neoapi</a>
</li>
<li>SourceSelector_Source2
: <a class="el" href="a00091.html#a81436bebe43622816970499b69df25e0">neoapi</a>
</li>
<li>SwitchPortSelector_Port0
: <a class="el" href="a00091.html#a8c5ccf7ddf2ee9e69535db522dc3a04c">neoapi</a>
</li>
<li>SwitchPortSelector_Port1
: <a class="el" href="a00091.html#abf93a161d5ccc59166810de0572041cd">neoapi</a>
</li>
<li>SwitchPortSelector_Port2
: <a class="el" href="a00091.html#a24c9e9b450f59ee5ae041ebd85746d6b">neoapi</a>
</li>
<li>SwitchPortSelector_Port3
: <a class="el" href="a00091.html#ab206c11fc32ef7dc463fd488b498c7ef">neoapi</a>
</li>
<li>SwitchPortSelector_Port4
: <a class="el" href="a00091.html#a4c70709992f11a079f109a78fefff772">neoapi</a>
</li>
<li>SwitchPortSelector_Port5
: <a class="el" href="a00091.html#a52ef6ecd465f9a848640b781ce6d64fc">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
