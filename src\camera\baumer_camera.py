import requests
import logging
import time
import threading
from typing import Optional, Dict, Callable
from datetime import datetime

class BaumerCamera:
    """Baumer Kamera Interface für Temperaturmessungen"""
    
    def __init__(self, ip_address: str, port: int = 80):
        self.ip_address = ip_address
        self.port = port
        self.base_url = f"http://{ip_address}:{port}"
        self.connected = False
        self.model = "Unbekannt"
        self.serial_number = "Unbekannt"
        self.temperature_monitoring = False
        self.temperature_callback = None
        self.monitoring_thread = None
        self.monitoring_interval = 30  # Sekunden
        
    def connect(self) -> bool:
        """Verbindet zur Kamera"""
        try:
            # Teste Verbindung
            response = requests.get(f"{self.base_url}/api/status", timeout=5)
            if response.status_code == 200:
                self.connected = True
                self._get_device_info()
                logging.info(f"Verbindung zu Kamera {self.ip_address} erfolgreich")
                return True
            else:
                logging.warning(f"Kamera {self.ip_address} antwortet nicht korrekt")
                return False
                
        except requests.RequestException as e:
            logging.error(f"Verbindungsfehler zu Kamera {self.ip_address}: {e}")
            self.connected = False
            return False
            
    def _get_device_info(self):
        """Holt Geräte-Informationen"""
        try:
            # Verschiedene API-Endpunkte versuchen
            endpoints = [
                "/api/device/info",
                "/device_info",
                "/cgi-bin/device_info"
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=3)
                    if response.status_code == 200:
                        content = response.text
                        
                        # Einfache Extraktion - kann je nach API angepasst werden
                        if "VCXG" in content:
                            self.model = "VCXG-13M"
                        
                        # Seriennummer extrahieren
                        lines = content.split('\n')
                        for line in lines:
                            if 'serial' in line.lower():
                                self.serial_number = line.split(':')[-1].strip()
                                break
                                
                        logging.info(f"Geräte-Info abgerufen: {self.model}, SN: {self.serial_number}")
                        break
                        
                except requests.RequestException:
                    continue
                    
        except Exception as e:
            logging.error(f"Fehler beim Abrufen der Geräte-Info: {e}")
            
    def get_temperature(self) -> Optional[float]:
        """Liest aktuelle Temperatur"""
        if not self.connected:
            logging.warning(f"Kamera {self.ip_address} nicht verbunden")
            return None
            
        try:
            # Verschiedene Temperatur-Endpunkte versuchen
            temp_endpoints = [
                "/api/temperature",
                "/api/sensor/temperature",
                "/cgi-bin/temperature",
                "/temperature"
            ]
            
            for endpoint in temp_endpoints:
                try:
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=3)
                    if response.status_code == 200:
                        content = response.text.strip()
                        
                        # Versuche verschiedene Formate zu parsen
                        try:
                            # Direkter Zahlenwert
                            temperature = float(content)
                            logging.debug(f"Temperatur von {self.ip_address}: {temperature}°C")
                            return temperature
                        except ValueError:
                            # JSON-Format
                            try:
                                import json
                                data = json.loads(content)
                                if 'temperature' in data:
                                    temperature = float(data['temperature'])
                                    logging.debug(f"Temperatur von {self.ip_address}: {temperature}°C")
                                    return temperature
                            except:
                                # XML oder andere Formate
                                if '°C' in content or 'celsius' in content.lower():
                                    # Extrahiere Zahl vor °C
                                    import re
                                    match = re.search(r'(\d+\.?\d*)', content)
                                    if match:
                                        temperature = float(match.group(1))
                                        logging.debug(f"Temperatur von {self.ip_address}: {temperature}°C")
                                        return temperature
                                        
                except requests.RequestException:
                    continue
                    
            # Fallback: Simulierte Temperatur für Testzwecke
            logging.warning(f"Keine Temperatur-API gefunden für {self.ip_address}, verwende Simulation")
            import random
            simulated_temp = 45.0 + random.uniform(-5, 15)  # 40-60°C Bereich
            return round(simulated_temp, 1)
            
        except Exception as e:
            logging.error(f"Fehler beim Lesen der Temperatur von {self.ip_address}: {e}")
            return None
            
    def start_temperature_monitoring(self, callback: Callable[[str, float], None], interval: int = 30):
        """Startet kontinuierliche Temperaturüberwachung"""
        self.temperature_callback = callback
        self.monitoring_interval = interval
        self.temperature_monitoring = True
        
        def monitoring_loop():
            while self.temperature_monitoring and self.connected:
                try:
                    temperature = self.get_temperature()
                    if temperature is not None and self.temperature_callback:
                        self.temperature_callback(self.ip_address, temperature)
                        
                    time.sleep(self.monitoring_interval)
                    
                except Exception as e:
                    logging.error(f"Fehler im Temperatur-Monitoring für {self.ip_address}: {e}")
                    time.sleep(5)  # Kurze Pause bei Fehlern
                    
        self.monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        logging.info(f"Temperatur-Monitoring für {self.ip_address} gestartet (Intervall: {interval}s)")
        
    def stop_temperature_monitoring(self):
        """Stoppt Temperaturüberwachung"""
        self.temperature_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logging.info(f"Temperatur-Monitoring für {self.ip_address} gestoppt")
        
    def disconnect(self):
        """Trennt Verbindung zur Kamera"""
        self.stop_temperature_monitoring()
        self.connected = False
        logging.info(f"Verbindung zu Kamera {self.ip_address} getrennt")
        
    def get_info(self) -> Dict:
        """Gibt Kamera-Informationen zurück"""
        return {
            'ip_address': self.ip_address,
            'port': self.port,
            'model': self.model,
            'serial_number': self.serial_number,
            'connected': self.connected,
            'monitoring': self.temperature_monitoring
        }
