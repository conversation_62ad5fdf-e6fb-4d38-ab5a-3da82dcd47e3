<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: Package Functions</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="contents">
<div class="textblock">Here is a list of all documented namespace members with links to the namespaces they belong to:</div>

<h3><a id="index_f"></a>- f -</h3><ul>
<li>FileOpenMode_Read
: <a class="el" href="a00091.html#a8df2ad1b09dec577edda0ea835c3f18f">neoapi</a>
</li>
<li>FileOpenMode_ReadWrite
: <a class="el" href="a00091.html#aa88bb18dafdf77989ef1fa47482ff294">neoapi</a>
</li>
<li>FileOpenMode_Write
: <a class="el" href="a00091.html#afae3cc5c61b9b24c3cc73686993f0420">neoapi</a>
</li>
<li>FileOperationSelector_Close
: <a class="el" href="a00091.html#a5fdf632cd70cc03b5d0c6df68a8b63a5">neoapi</a>
</li>
<li>FileOperationSelector_Delete
: <a class="el" href="a00091.html#a5a853f43e352104eb95c049e89f0ae3d">neoapi</a>
</li>
<li>FileOperationSelector_Open
: <a class="el" href="a00091.html#a6b4b38c19d992b4a2da3f39ff1532945">neoapi</a>
</li>
<li>FileOperationSelector_Read
: <a class="el" href="a00091.html#abf55c4e3563d3695bd348e4a6a0f9daf">neoapi</a>
</li>
<li>FileOperationSelector_Write
: <a class="el" href="a00091.html#a7ff603e76ccd020303fa6d911b9c8099">neoapi</a>
</li>
<li>FileOperationStatus_Failure
: <a class="el" href="a00091.html#a74e72864624c2aa13963fd853b9c0c16">neoapi</a>
</li>
<li>FileOperationStatus_Success
: <a class="el" href="a00091.html#a9096c2309c57e2d8e0d5c00cbcd370fa">neoapi</a>
</li>
<li>FileSelector_LUTBlue
: <a class="el" href="a00091.html#a32587d423c5c12924fe4ee67739f1d72">neoapi</a>
</li>
<li>FileSelector_LUTGreen
: <a class="el" href="a00091.html#ac25e9f26c88e5c18eb9c6458d15be929">neoapi</a>
</li>
<li>FileSelector_LUTLuminance
: <a class="el" href="a00091.html#a7c1b6478066e1a194eec2e3f809a8343">neoapi</a>
</li>
<li>FileSelector_LUTRed
: <a class="el" href="a00091.html#ac1182784391f1d039cc165de1e22ea91">neoapi</a>
</li>
<li>FileSelector_UserSet1
: <a class="el" href="a00091.html#a7872ebdcd9be5e636c7c8f380e3d4b49">neoapi</a>
</li>
<li>FileSelector_UserSet2
: <a class="el" href="a00091.html#ad91cffd6eee0e9a5a824a9436e25b22c">neoapi</a>
</li>
<li>FileSelector_UserSet3
: <a class="el" href="a00091.html#a107963b3ca077da9d47d6cf45bb18fa9">neoapi</a>
</li>
<li>FileSelector_UserSetDefault
: <a class="el" href="a00091.html#a3a6104d72b44ede4a154f7336d506420">neoapi</a>
</li>
<li>FocalLengthStatus_Busy
: <a class="el" href="a00091.html#abf9625bd0a693f8ae200b3a0a92bea82">neoapi</a>
</li>
<li>FocalLengthStatus_Error
: <a class="el" href="a00091.html#a31837c6549ebcb17d0647aaf57896c4d">neoapi</a>
</li>
<li>FocalLengthStatus_NotConnected
: <a class="el" href="a00091.html#aff8bbfac9ddc6e285baf383553108032">neoapi</a>
</li>
<li>FocalLengthStatus_NotInitialized
: <a class="el" href="a00091.html#aace5dab4308ae26b1b2ad867064e6e3f">neoapi</a>
</li>
<li>FocalLengthStatus_NotSupported
: <a class="el" href="a00091.html#a9d344ad9e4af0bae755ff99b503590c2">neoapi</a>
</li>
<li>FocalLengthStatus_Ready
: <a class="el" href="a00091.html#a989115c0c7111529ab1632be33024ea0">neoapi</a>
</li>
<li>FocusStatus_Busy
: <a class="el" href="a00091.html#a90fef263db819050ef6f660af267709b">neoapi</a>
</li>
<li>FocusStatus_Error
: <a class="el" href="a00091.html#a3cea6c311cdf111c7b26d2d6ad307078">neoapi</a>
</li>
<li>FocusStatus_NotConnected
: <a class="el" href="a00091.html#ae6bc06f8dbe47c89d5b2f7bc70931e43">neoapi</a>
</li>
<li>FocusStatus_NotInitialized
: <a class="el" href="a00091.html#a008ebc7a46455100acfb75048ff1a957">neoapi</a>
</li>
<li>FocusStatus_NotSupported
: <a class="el" href="a00091.html#afb8e6a9e4df92667923390352f347feb">neoapi</a>
</li>
<li>FocusStatus_Ready
: <a class="el" href="a00091.html#a955f337eb6265264e314a4cdc444f7cb">neoapi</a>
</li>
</ul>
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
