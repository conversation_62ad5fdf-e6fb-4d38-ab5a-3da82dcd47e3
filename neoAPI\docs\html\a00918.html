<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: The gevipconfig-tool</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">The gevipconfig-tool </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><h1><a class="anchor" id="autotoc_md26"></a>
The gevipconfig-tool</h1>
<p>the gevipconfig-tool can be used to configure the networking of your camera it can also be used as part of your setup or in a script to automate the process </p>
<h2><a class="anchor" id="autotoc_md27"></a>
Introduction</h2>
<p>GigE Vision devices need a valid IP address to operate as expected. A valid IP address has to be in the subnet of the network interface card, the device is connected to and has to be unique.</p>
<p>For example:</p>
<h3><a class="anchor" id="autotoc_md28"></a>
Example 1</h3>
<p>NIC: IP *********** Subnet ************* Camera: IP *********** Subnet *************</p>
<p>-&gt; invalid: different subnet</p>
<h3><a class="anchor" id="autotoc_md29"></a>
Example 2</h3>
<p>NIC: IP *********** Subnet ************* Camera: IP *********** Subnet *************</p>
<p>-&gt; invalid: same subnet but also same ip address</p>
<h3><a class="anchor" id="autotoc_md30"></a>
Example 3</h3>
<p>NIC: IP *********** Subnet ************* Camera: IP *********** Subnet *************</p>
<p>-&gt; valid: same subnet and different ip address</p>
<h2><a class="anchor" id="autotoc_md31"></a>
Basic Usage</h2>
<p><b>To work properly on linux, super user privileges are required!</b></p>
<p>Running <code>gevipconfig</code>without any parameter will list all detected GigE Vision devices and their connection settings.</p>
<p>Running <code>gevipconfig -a</code> will issue a forceip command to all devices which are not in the same subnet as the NIC they are connected to. This will (temporarily, until the camera is restarted) enable you to connect to the devices.</p>
<p>If you require a permanent network setting for the camera, you can use the "persistent IP" feature or the DHCP option</p>
<p>If you want to change specific devices, call <code>gevipconfig -c "SERIAL NUMBER"</code> and replace "SERIAL NUMBER" with the serial number of the device to modify. eg. <code>gevipconfig -c 700001817369</code>.</p>
<p>For a detailed description of all supported command line parameters call <code>gevipconfig -h</code>.</p>
<h2><a class="anchor" id="autotoc_md32"></a>
Command line parameter</h2>
<h3><a class="anchor" id="autotoc_md33"></a>
-h [ –help ]</h3>
<p>Show all valid command line parameter with their description.</p>
<h3><a class="anchor" id="autotoc_md34"></a>
–version</h3>
<p>Show the version information.</p>
<h3><a class="anchor" id="autotoc_md35"></a>
-v [ –verbose ]</h3>
<p>Show additional information. (e.g. all network adapters with the connected gev devices)</p>
<h3><a class="anchor" id="autotoc_md36"></a>
–silent</h3>
<p>Show only fatal errors (overrides verbose)</p>
<h3><a class="anchor" id="autotoc_md37"></a>
-t [ –timeout ] arg</h3>
<p>Timeout to wait for cameras to answer discovery broadcast message.</p>
<h3><a class="anchor" id="autotoc_md38"></a>
-a [ –all ]</h3>
<p>Set an ip address matching the subnet of connected network interface for all detected gev devices. Additional device specific options will be ignored.</p>
<h3><a class="anchor" id="autotoc_md39"></a>
-c [ –camera ] arg</h3>
<p>Set an ip address matching to the subnet of the network interface for the selected device (by serial number or MAC address)</p>
<h3><a class="anchor" id="autotoc_md40"></a>
-i [ –ip ] arg</h3>
<p>Set the target ip address (eg. *************). Works only in combination with camera option!</p>
<h3><a class="anchor" id="autotoc_md41"></a>
-s [ –subnet ] arg</h3>
<p>Set the target subnet (eg. ***********). Works only in combination with ip option!</p>
<h3><a class="anchor" id="autotoc_md42"></a>
-p [ –persistent ]</h3>
<p>Configure persistent ip address.</p>
<h3><a class="anchor" id="autotoc_md43"></a>
–no-persistent</h3>
<p>Disable persistent ip address.</p>
<h3><a class="anchor" id="autotoc_md44"></a>
–dhcp</h3>
<p>Enable dhcp mode of device</p>
<h3><a class="anchor" id="autotoc_md45"></a>
–no-dhcp</h3>
<p>Disable dhcp mode of device</p>
<h3><a class="anchor" id="autotoc_md46"></a>
–arp</h3>
<p>Add static ARP entry to avoid packet loss. (Windows only + requires administrator privilege)</p>
<h3><a class="anchor" id="autotoc_md47"></a>
–no-rp-filter</h3>
<p>Disable reverse path filtering. (Linux only)</p>
<h3><a class="anchor" id="autotoc_md48"></a>
–rp-filter</h3>
<p>Restore reverse path filtering. (Linux only) </p>
</div></div><!-- PageDoc -->
</div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
