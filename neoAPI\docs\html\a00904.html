<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: edgedetect_opencl.py</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">edgedetect_opencl.py</div>  </div>
</div><!--header-->
<div class="contents">
<p>This example show the pro and cons of gpu usage for image processing by acquire images with a camera and run an edge detection on this images.</p>
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment"># \example edgedetect_opencl.py</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"># This example describes the use of buffers allocated by the user or other frameworks.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"># The given source code applies to handle one camera and image acquisition.</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">#</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;</div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="keyword">import</span> sys</div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="keyword">import</span> threading</div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="keyword">import</span> time</div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="keyword">import</span> neoapi</div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="keyword">import</span> cv2</div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="keyword">import</span> numpy</div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;</div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="keyword">class </span>MemoryMode:</div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;    cpu = cv2.USAGE_ALLOCATE_HOST_MEMORY</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;    gpu = cv2.USAGE_ALLOCATE_DEVICE_MEMORY</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;    shared = cv2.USAGE_ALLOCATE_SHARED_MEMORY</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;</div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="keyword">class </span>CamBuffer(<a class="code" href="a00879.html">neoapi.BufferBase</a>):</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;    <span class="stringliteral">&quot;&quot;&quot;implementation for user buffer mode&quot;&quot;&quot;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;    <span class="keyword">def </span>__init__(self, width, height, memtype):</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;        <a class="code" href="a00879.html#a452d20e5d9f465a672f3ef50c90d59bc">neoapi.BufferBase.__init__</a>(self)</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;        self.memtype = memtype</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;        <span class="keywordflow">if</span> memtype == MemoryMode.cpu:</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;            self.cpu_mat = numpy.ndarray((height, width, 1), numpy.uint8)</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;        <span class="keywordflow">else</span>:</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;            self.gpu_mat = cv2.UMat(height, width, cv2.CV_8UC1)</div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;            self.cpu_mat = self.gpu_mat.get()</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;        self.RegisterSegment(self.cpu_mat, width * height * 1)</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;    <span class="keyword">def </span>__del__(self):</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;        self.UnregisterMemory()</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="keyword">class </span>EdgeDetector:</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    <span class="keyword">def </span>__init__(self, serialnumber):</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;        self._camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;        self._camera.Connect(serialnumber)</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        self._camera.f.ExposureTime.Set(2500)</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        self._camera.DisableChunk()</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;        <span class="keywordflow">try</span>:</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;            self._camera.f.PixelFormat.Set(neoapi.PixelFormat_BayerRG8)</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;        <span class="keywordflow">except</span> <a class="code" href="a00779.html">neoapi.FeatureAccessException</a>:</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;            self._camera.f.PixelFormat.Set(neoapi.PixelFormat_Mono8)</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;        self._pixelformat = self._camera.f.PixelFormat.Get()</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;        self._identifier = serialnumber</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;        self._buffers = []</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        self._grey_mat = cv2.UMat()</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        self._gauss_mat = cv2.UMat()</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        self._sobel_mat = cv2.UMat()</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;        self._detect_thread = threading.Thread()</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        self._frames = 0</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;        self._run = <span class="keyword">False</span></div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;    <span class="keyword">def </span>Setup(self, memtype):</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;        cv2.ocl.setUseOpenCL(MemoryMode.cpu != memtype)</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <span class="comment"># if cv2.ocl.Device.getDefault().hostUnifiedMemory():</span></div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        <span class="comment">#     cv2.ocl.Context.getDefault().setUseSVM(MemoryMode.shared != memtype)</span></div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        self._SetupBuffers(3, memtype)</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        self._camera.SetUserBufferMode()</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    <span class="keyword">def </span>Detect(self, image, show_image):</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;        cambuf = image.GetUserBuffer()</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;        img_mat = cambuf.cpu_mat <span class="keywordflow">if</span> cambuf.memtype == MemoryMode.cpu <span class="keywordflow">else</span> cv2.UMat(cambuf.cpu_mat)</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        <span class="keywordflow">if</span> neoapi.PixelFormat_BayerRG8 == self._pixelformat:</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;            self._grey_mat = cv2.cvtColor(img_mat, cv2.COLOR_BayerRG2GRAY)</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        <span class="keywordflow">else</span>:</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;            self._grey_mat = img_mat</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        self._gauss_mat = cv2.GaussianBlur(self._grey_mat, (5, 5), 0)</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        self._sobel_mat = cv2.Sobel(self._gauss_mat, 0, 1, 1, ksize=5)</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        <span class="keywordflow">if</span> show_image:</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;            cv2.imshow(self._identifier, self._sobel_mat)</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;            cv2.pollKey()</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        self._frames += 1</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;    <span class="keyword">def </span>ProcessedFrames(self):</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;        frames = self._frames</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        self._frames = 0</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        <span class="keywordflow">return</span> frames</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    <span class="keyword">def </span>GetIdentifier(self):</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <span class="keywordflow">return</span> self._identifier</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;    <span class="keyword">def </span>Start(self, show_images):</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;        self._run = <span class="keyword">True</span></div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        self._detect_thread = threading.Thread(target=self._Detect, args=(show_images,))</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;        self._detect_thread.start()</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    <span class="keyword">def </span>Stop(self):</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        self._run = <span class="keyword">False</span></div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        <span class="keywordflow">if</span> self._detect_thread.is_alive():</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;            self._detect_thread.join()</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    <span class="keyword">def </span>FreeCamBuffers(self):</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <span class="keywordflow">while</span> self._buffers:</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;            self._camera.RevokeUserBuffer(self._buffers.pop())</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;    <span class="keyword">def </span>_SetupBuffers(self, count, memtype):</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        width = self._camera.f.Width.Get()</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        height = self._camera.f.Height.Get()</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        self.FreeCamBuffers()</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        <span class="keywordflow">for</span> _ <span class="keywordflow">in</span> range(count):</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;            self._buffers.append(CamBuffer(width, height, memtype))</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;            self._camera.AddUserBuffer(self._buffers[-1])</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;        self._grey_mat = cv2.UMat(height, width, cv2.CV_8UC1, memtype)</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        self._gauss_mat = cv2.UMat(height, width, cv2.CV_8UC1, memtype)</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        self._sobel_mat = cv2.UMat(height, width, cv2.CV_8UC1, memtype)</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;    <span class="keyword">def </span>_Detect(self, show_images):</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        <span class="keywordflow">try</span>:</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;            <span class="keywordflow">if</span> show_images:</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;                cv2.namedWindow(self._identifier, cv2.WINDOW_NORMAL)</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;            <span class="keywordflow">while</span> self._run:</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;                image = self._camera.GetImage()</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;                <span class="keywordflow">if</span> image.IsEmpty():</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;                    print(<span class="stringliteral">&quot;%s Error during acquisition!&quot;</span> % self._identifier)</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;                    <span class="keywordflow">break</span></div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;                self.Detect(image, show_images)</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;            <span class="keywordflow">if</span> show_images:</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;                cv2.destroyWindow(self._identifier)</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        <span class="keywordflow">except</span> <a class="code" href="a00767.html">neoapi.NeoException</a> <span class="keyword">as</span> exc:</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;            print(<span class="stringliteral">&quot;%s error %s&quot;</span> % (self._identifier, exc.GetDescription()))</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;        <span class="keywordflow">except</span> cv2.error <span class="keyword">as</span> exc:</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;            print(<span class="stringliteral">&quot;%s cv error %s&quot;</span> % (self._identifier, exc))</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="keyword">def </span>GetGpuCapalities():</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;    memtypes = {MemoryMode.cpu: <span class="stringliteral">&quot;cpu&quot;</span>}</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    <span class="keywordflow">if</span> cv2.ocl.haveOpenCL():</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;        memtypes[MemoryMode.gpu] = <span class="stringliteral">&quot;gpu&quot;</span></div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    <span class="comment">#     if cv2.ocl.Device.getDefault().hostUnifiedMemory():</span></div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;    <span class="comment">#         memtypes[MemoryMode.shared] = &quot;shared&quot;</span></div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <span class="keywordflow">return</span> memtypes</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="keyword">def </span>FindDevices():</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    devices = []</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    <span class="keywordflow">for</span> device <span class="keywordflow">in</span> <a class="code" href="a00091.html#ac82ef0c34247353b96764b69b8142556">neoapi.CamInfoList_Get</a>():</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;        <span class="keywordflow">try</span>:</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;            devices.append(EdgeDetector(device.GetSerialNumber()))</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        <span class="keywordflow">except</span> <a class="code" href="a00767.html">neoapi.NeoException</a> <span class="keyword">as</span> exc:</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;            print(<span class="stringliteral">&#39;error: &#39;</span>, exc)</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    print(len(devices), <span class="stringliteral">&quot;device(s) connected!&quot;</span>)</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <span class="keywordflow">return</span> devices</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;<span class="keyword">def </span>PrintMetrics(devices, duration):</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    <span class="keywordflow">for</span> _ <span class="keywordflow">in</span> range(duration):</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        time.sleep(1.0)</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        <span class="keywordflow">for</span> device <span class="keywordflow">in</span> devices:</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;            print(<span class="stringliteral">&quot;%s fps: %s&quot;</span> % (device.GetIdentifier(), device.ProcessedFrames()))</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;<span class="keyword">def </span>RunDetection(devices, memtypes, show_images):</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    <span class="keywordflow">if</span> devices:</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;        <span class="keywordflow">for</span> val, text  <span class="keywordflow">in</span> memtypes.items():</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;            print(<span class="stringliteral">&quot;Next run will be processed on &quot;</span>, text)</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;            <span class="keywordflow">for</span> device <span class="keywordflow">in</span> devices:</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;                device.Setup(val)</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;            <span class="keywordflow">for</span> device <span class="keywordflow">in</span> devices:</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;                device.Start(show_images)</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;            PrintMetrics(devices, 5)</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;            <span class="keywordflow">for</span> device <span class="keywordflow">in</span> devices:</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;                device.Stop()</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="keyword">def </span>FreeDevices(devices):</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    <span class="keywordflow">while</span> devices:</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;        devices.pop().FreeCamBuffers()</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;result = 0</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;<span class="comment"># Showing the images have a high impact on processing speed.</span></div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;<span class="comment"># For better comparision show_images should be disabled.</span></div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;show_images = len(sys.argv) &gt; 1</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;<span class="comment"># look if the gpu supports opencl and shared memory</span></div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;memtypes = GetGpuCapalities()</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;<span class="comment"># find all connected cameras</span></div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;devices = FindDevices()</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;<span class="comment"># edge detection processing on all connected cameras</span></div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;RunDetection(devices, memtypes, show_images)</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;<span class="comment"># cleanup</span></div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;FreeDevices(devices)</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;sys.exit(result)</div></div><!-- fragment --> </div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
