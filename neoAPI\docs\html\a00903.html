<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <!-- For Mobile Devices -->
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8" />
    <meta name="generator" content="Doxygen 1.8.15" />
    <script type="text/javascript" src="jquery-2.1.1.min.js"></script>
    <title>neoAPI Python Documentation: user_buffer.py</title>
    <link rel="shortcut icon" type="image/x-icon" media="all" href="favicon.ico" />
    <script type="text/javascript" src="dynsections.js"></script>
    <link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/search.js"></script>
<link rel="search" href="search_opensearch.php?v=opensearch.xml" type="application/opensearchdescription+xml" title="neoAPI Python Documentation"/>
    <link href="doxygen.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="bootstrap.min.css" />
    <script src="bootstrap.min.js"></script>
    <link href="jquery.smartmenus.bootstrap.css" rel="stylesheet" />
    <script type="text/javascript" src="jquery.smartmenus.js"></script>
    <!-- SmartMenus jQuery Bootstrap Addon -->
    <script type="text/javascript" src="jquery.smartmenus.bootstrap.js"></script>
    <!-- SmartMenus jQuery plugin -->
    <link href="customdoxygen.css" rel="stylesheet" type="text/css"/>
</head>
<body>
    <nav class="navbar navbar-default" role="navigation">
        <div class="container">
            <div class="navbar-header">
                <img id="logo" src="BaumerLogo.png" /><span>neoAPI Python Documentation</span>
            </div>
        </div>
    </nav>
    <div id="top">
        <!-- do not remove this div, it is closed by doxygen! -->
        <div class="content" id="content">
            <div class="container">
                <div class="row">
                    <div class="col-sm-12 panel " style="padding-bottom: 15px;">
                        <div style="margin-bottom: 15px;">
                            <!-- end header part -->
<!-- Generated by Doxygen 1.8.15 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,true,'search.html','Search');
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
  $(document).ready(function() {
    if ($('.searchresults').length > 0) { searchBox.DOMSearchField().focus(); }
  });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">user_buffer.py</div>  </div>
</div><!--header-->
<div class="contents">
<p>This example describes the use of buffers allocated by the user or other frameworks.</p>
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="stringliteral">&#39;&#39;&#39; \example user_buffer.py</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="stringliteral">This example describes the use of buffers allocated by the user or other frameworks.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="stringliteral">The given source code applies to handle one camera and image acquisition.</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="stringliteral">&#39;&#39;&#39;</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;</div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="keyword">import</span> sys</div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;use_numpy = <span class="keyword">False</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="keywordflow">try</span>:</div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;    <span class="keyword">import</span> numpy</div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;    use_numpy = <span class="keyword">True</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="keywordflow">except</span> ImportError:</div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;    <span class="keywordflow">pass</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="keyword">import</span> neoapi</div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="keyword">import</span> ctypes</div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="keyword">class </span>MyBuffer(<a class="code" href="a00879.html">neoapi.BufferBase</a>):</div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;    <span class="stringliteral">&quot;&quot;&quot;implementation for user buffer mode&quot;&quot;&quot;</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;    <span class="keyword">def </span>__init__(self, size):</div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;        <a class="code" href="a00879.html#a452d20e5d9f465a672f3ef50c90d59bc">neoapi.BufferBase.__init__</a>(self)</div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;        self._buf = numpy.zeros(size, numpy.uint8) <span class="keywordflow">if</span> use_numpy <span class="keywordflow">else</span> bytearray(size)</div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;        self._size = size</div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;        self.RegisterMemory(self._buf, self._size)</div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;</div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;    <span class="keyword">def </span>CalcVar(self):</div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;        var = -1</div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;        <span class="keywordflow">if</span> (use_numpy): <span class="comment"># buf contains the image data and can be used with numpy</span></div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;            var = self._buf.var()</div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;        <span class="keywordflow">return</span> var</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;result = 0</div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keywordflow">try</span>:</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;    camera = <a class="code" href="a00863.html">neoapi.Cam</a>()</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    camera.Connect()</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    payloadsize = camera.f.PayloadSize.Get()</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    buf = MyBuffer(payloadsize)</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;    buf.myContent = 42</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;    camera.AddUserBuffer(buf)</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;    camera.f.ExposureTime.Set(10000)</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;    camera.SetUserBufferMode(<span class="keyword">True</span>)</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;    image = camera.GetImage()</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;    print(<span class="stringliteral">&quot;Image variance:&quot;</span>, image.GetUserBuffer().CalcVar())</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;    <span class="comment"># the same object will be returned (myContent is set)</span></div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;    print(<span class="stringliteral">&quot;Buffers Content:&quot;</span>, image.GetUserBuffer().myContent)</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="keywordflow">except</span> (<a class="code" href="a00767.html">neoapi.NeoException</a>, Exception) <span class="keyword">as</span> exc:</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;    print(<span class="stringliteral">&#39;error: &#39;</span>, exc)</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    result = 1</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;sys.exit(result)</div></div><!-- fragment --> </div><!-- contents -->
<!-- HTML footer for doxygen 1.8.8-->
<!-- start footer part -->
</div>
</div>
</div>
</div>
</div>
<hr class="footer" /><address class="footer">
    <small>
        neoAPI Python Documentation ver. 1.5.0,
        generated by <a href="http://www.doxygen.org/index.html">
            Doxygen
        </a>
    </small>
</address>
<script type="text/javascript" src="doxy-boot.js"></script>
</body>
</html>
