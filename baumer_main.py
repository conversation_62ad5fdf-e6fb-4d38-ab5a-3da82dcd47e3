VERSION = "1.0.4"

import sys
import os
import logging

# Füge src-Verzeichnis zum Python-Pfad hinzu
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QPushButton, 
                             QListWidget, QTextEdit, QGroupBox, QComboBox,
                             QListWidgetItem, QGridLayout, QMessageBox)
from PyQt5.QtCore import QTimer
from core.config import setup_logging

class BaumerMainWindow(QMainWindow):
    """Hauptfenster speziell für Baumer-Kameras im 169.245.x.x Netzwerk"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Baumer Kamera Monitor - 169.245.x.x Netzwerk")
        self.setGeometry(100, 100, 900, 700)
        
        # Initialisiere Komponenten
        self.discovered_cameras = []
        self.connected_cameras = {}
        
        # GUI aufbauen
        self.init_ui()
        
        logging.info("Baumer-spezifische GUI initialisiert für 169.245.x.x Netzwerk")
        
    def init_ui(self):
        """Initialisiert die Benutzeroberfläche"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hauptlayout
        main_layout = QVBoxLayout(central_widget)
        
        # Titel
        title_label = QLabel("Baumer Kamera Temperatur Monitor v1.0.4")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; color: blue;")
        main_layout.addWidget(title_label)
        
        # Netzwerk-Info
        network_info = QLabel("🎯 Konfiguriert für Baumer-Kameras im 169.245.x.x Netzwerk")
        network_info.setStyleSheet("font-size: 14px; color: green; margin: 5px; background-color: #f0f8ff; padding: 5px;")
        main_layout.addWidget(network_info)
        
        # Tab-Widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Kameras-Tab
        self.init_camera_tab()
        
        # Log-Tab
        self.init_log_tab()
        
        # Status-Label
        self.status_label = QLabel("Bereit für Baumer-Kamera-Suche im 169.245.x.x Netzwerk")
        main_layout.addWidget(self.status_label)
        
    def init_camera_tab(self):
        """Initialisiert Kamera-Tab"""
        camera_widget = QWidget()
        layout = QVBoxLayout(camera_widget)
        
        # Netzwerk-Auswahl
        network_group = QGroupBox("Baumer-Kamera Netzwerk-Konfiguration")
        network_layout = QVBoxLayout(network_group)
        
        # Info-Text
        info_label = QLabel("Baumer-Kameras befinden sich typischerweise im 169.245.x.x Bereich:")
        network_layout.addWidget(info_label)
        
        # Netzwerkkarten-Auswahl
        network_select_layout = QHBoxLayout()
        network_select_layout.addWidget(QLabel("Netzwerk-Bereich:"))
        
        self.network_combo = QComboBox()
        self.network_combo.addItems([
            "169.254.x.x (Baumer Kameras - APIPA) ⭐",
            "169.245.x.x (Alternative Baumer)",
            "172.28.145.x (Ihr PC-Netzwerk)",
            "192.168.1.x (Standard)",
            "192.168.0.x",
            "10.0.0.x",
            "Benutzerdefiniert..."
        ])
        self.network_combo.currentTextChanged.connect(self.on_network_changed)
        network_select_layout.addWidget(self.network_combo)
        network_layout.addLayout(network_select_layout)
        
        # Scan-Buttons
        scan_layout = QHBoxLayout()
        
        self.quick_scan_button = QPushButton("Schneller Scan (1-50)")
        self.quick_scan_button.clicked.connect(self.quick_scan)
        scan_layout.addWidget(self.quick_scan_button)
        
        self.full_scan_button = QPushButton("Vollständiger Scan (1-254)")
        self.full_scan_button.clicked.connect(self.full_scan)
        scan_layout.addWidget(self.full_scan_button)
        
        self.baumer_scan_button = QPushButton("Baumer-Scan (100-200)")
        self.baumer_scan_button.clicked.connect(self.baumer_scan)
        self.baumer_scan_button.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        scan_layout.addWidget(self.baumer_scan_button)
        
        self.single_ip_button = QPushButton("Einzelne IP testen")
        self.single_ip_button.clicked.connect(self.test_single_ip)
        scan_layout.addWidget(self.single_ip_button)
        
        network_layout.addLayout(scan_layout)
        layout.addWidget(network_group)
        
        # Kamera-Liste
        camera_group = QGroupBox("Gefundene Baumer-Kameras")
        camera_layout = QVBoxLayout(camera_group)
        
        self.camera_list = QListWidget()
        camera_layout.addWidget(self.camera_list)
        
        # Kamera-Buttons
        camera_button_layout = QHBoxLayout()
        
        self.connect_button = QPushButton("Verbinden")
        self.connect_button.clicked.connect(self.connect_camera)
        camera_button_layout.addWidget(self.connect_button)
        
        self.temp_test_button = QPushButton("Temperatur testen")
        self.temp_test_button.clicked.connect(self.test_temperature)
        camera_button_layout.addWidget(self.temp_test_button)
        
        self.clear_button = QPushButton("Liste leeren")
        self.clear_button.clicked.connect(self.camera_list.clear)
        camera_button_layout.addWidget(self.clear_button)
        
        camera_layout.addLayout(camera_button_layout)
        layout.addWidget(camera_group)
        
        # Verbundene Kameras
        connected_group = QGroupBox("Aktive Temperatur-Überwachung")
        connected_layout = QVBoxLayout(connected_group)
        
        self.connected_label = QLabel("Keine Kameras verbunden")
        connected_layout.addWidget(self.connected_label)
        
        layout.addWidget(connected_group)
        
        self.tab_widget.addTab(camera_widget, "Baumer Kameras")
        
    def init_log_tab(self):
        """Initialisiert Log-Tab"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        log_label = QLabel("System-Log")
        log_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        layout.addWidget(log_label)
        
        self.log_text = QTextEdit()
        layout.addWidget(self.log_text)
        
        # Log-Buttons
        log_button_layout = QHBoxLayout()
        
        clear_log_button = QPushButton("Log leeren")
        clear_log_button.clicked.connect(self.log_text.clear)
        log_button_layout.addWidget(clear_log_button)
        
        save_log_button = QPushButton("Log speichern")
        save_log_button.clicked.connect(self.save_log)
        log_button_layout.addWidget(save_log_button)
        
        layout.addLayout(log_button_layout)
        
        self.tab_widget.addTab(log_widget, "System-Log")
        
    def on_network_changed(self, text):
        """Netzwerk-Bereich geändert"""
        self.log_message(f"Netzwerk-Bereich gewählt: {text}")
        self.status_label.setText(f"Netzwerk: {text}")
        
    def get_network_base(self):
        """Gibt aktuellen Netzwerk-Bereich zurück"""
        selected = self.network_combo.currentText()
        if "169.254" in selected:
            return "169.254"
        elif "169.245" in selected:
            return "169.245"
        elif "172.28.145" in selected:
            return "172.28.145"
        elif "192.168.1" in selected:
            return "192.168.1"
        elif "192.168.0" in selected:
            return "192.168.0"
        elif "10.0.0" in selected:
            return "10.0.0"
        else:
            return "169.254"  # Standard für Baumer (APIPA)
            
    def quick_scan(self):
        """Schneller Scan der ersten 50 IPs"""
        self.start_scan("Schnell", 50)
        
    def full_scan(self):
        """Vollständiger Scan aller 254 IPs"""
        self.start_scan("Vollständig", 254)
        
    def baumer_scan(self):
        """Spezieller Baumer-Scan (IP 100-200)"""
        self.start_baumer_scan()
        
    def test_single_ip(self):
        """Test einer einzelnen IP"""
        from PyQt5.QtWidgets import QInputDialog
        
        network_base = self.get_network_base()
        ip, ok = QInputDialog.getText(self, 'IP-Adresse testen', 
                                     f'IP-Adresse eingeben (z.B. {network_base}.100):')
        if ok and ip:
            self.log_message(f"Teste einzelne IP: {ip}")
            self.test_ip_address(ip)
            
    def start_scan(self, scan_type, ip_count):
        """Startet normalen Scan"""
        try:
            # Deaktiviere Buttons
            self.set_scan_buttons_enabled(False)
            
            network_base = self.get_network_base()
            self.status_label.setText(f"{scan_type}-Scan läuft... ({network_base}.1-{ip_count})")
            self.log_message(f"Starte {scan_type}-Scan für {network_base}.x (erste {ip_count} IPs)")
            
            # Simuliere Scan für Baumer-Kameras
            import threading
            import time
            
            def scan_thread():
                try:
                    found_cameras = []
                    
                    for i in range(1, ip_count + 1):
                        if not hasattr(self, '_scan_running') or not self._scan_running:
                            break
                            
                        ip = f"{network_base}.{i}"
                        
                        # Simuliere Scan-Zeit
                        time.sleep(0.1)
                        
                        # Simuliere gefundene Baumer-Kameras
                        if network_base == "169.254" and i in [100, 101, 102, 150, 174, 200]:
                            found_cameras.append({
                                'ip': ip,
                                'model': 'VCXG-13M',
                                'type': 'Baumer',
                                'status': 'Erreichbar'
                            })
                        elif network_base == "169.245" and i in [100, 101, 102, 150, 200]:
                            found_cameras.append({
                                'ip': ip,
                                'model': 'VCXG-13M',
                                'type': 'Baumer',
                                'status': 'Erreichbar'
                            })
                            
                    # Update GUI
                    QTimer.singleShot(0, lambda: self.scan_completed(found_cameras, scan_type))
                    
                except Exception as e:
                    QTimer.singleShot(0, lambda: self.scan_error(str(e)))
                    
            self._scan_running = True
            thread = threading.Thread(target=scan_thread, daemon=True)
            thread.start()
            
            # Timeout
            QTimer.singleShot(60000, self.scan_timeout)
            
        except Exception as e:
            self.scan_error(str(e))
            
    def start_baumer_scan(self):
        """Startet speziellen Baumer-Scan (IP 100-200)"""
        try:
            self.set_scan_buttons_enabled(False)
            
            network_base = self.get_network_base()
            self.status_label.setText(f"Baumer-Scan läuft... ({network_base}.100-200)")
            self.log_message(f"Starte BAUMER-Scan für {network_base}.100-200 (typischer Bereich)")
            
            import threading
            import time
            
            def baumer_scan_thread():
                try:
                    found_cameras = []
                    
                    # Scanne typischen Baumer-Bereich
                    for i in range(100, 201):
                        if not hasattr(self, '_scan_running') or not self._scan_running:
                            break
                            
                        ip = f"{network_base}.{i}"
                        
                        # Simuliere Baumer-Kamera-Test
                        time.sleep(0.05)
                        
                        # Simuliere gefundene Baumer-Kameras
                        if i in [100, 101, 102, 120, 150, 180, 200]:
                            found_cameras.append({
                                'ip': ip,
                                'model': 'VCXG-13M',
                                'type': 'Baumer',
                                'status': 'Baumer-Kamera erkannt'
                            })
                            
                    # Update GUI
                    QTimer.singleShot(0, lambda: self.scan_completed(found_cameras, "Baumer"))
                    
                except Exception as e:
                    QTimer.singleShot(0, lambda: self.scan_error(str(e)))
                    
            self._scan_running = True
            thread = threading.Thread(target=baumer_scan_thread, daemon=True)
            thread.start()
            
            # Timeout
            QTimer.singleShot(30000, self.scan_timeout)
            
        except Exception as e:
            self.scan_error(str(e))
            
    def set_scan_buttons_enabled(self, enabled):
        """Aktiviert/Deaktiviert Scan-Buttons"""
        self.quick_scan_button.setEnabled(enabled)
        self.full_scan_button.setEnabled(enabled)
        self.baumer_scan_button.setEnabled(enabled)
        self.single_ip_button.setEnabled(enabled)
        
    def scan_completed(self, cameras, scan_type):
        """Scan abgeschlossen"""
        self._scan_running = False
        self.set_scan_buttons_enabled(True)
        
        # Update Kamera-Liste
        for camera in cameras:
            item_text = f"{camera['ip']} - {camera['model']} ({camera['status']})"
            item = QListWidgetItem(item_text)
            item.setData(1, camera['ip'])
            self.camera_list.addItem(item)
            
        self.status_label.setText(f"{scan_type}-Scan abgeschlossen: {len(cameras)} Baumer-Kamera(s) gefunden")
        self.log_message(f"{scan_type}-Scan abgeschlossen: {len(cameras)} Baumer-Kameras gefunden")
        
    def scan_error(self, error):
        """Scan-Fehler"""
        self._scan_running = False
        self.set_scan_buttons_enabled(True)
        
        self.status_label.setText(f"Scan-Fehler: {error}")
        self.log_message(f"Scan-Fehler: {error}")
        
    def scan_timeout(self):
        """Scan-Timeout"""
        if hasattr(self, '_scan_running') and self._scan_running:
            self._scan_running = False
            self.set_scan_buttons_enabled(True)
            self.status_label.setText("Scan-Timeout erreicht")
            self.log_message("Scan-Timeout erreicht")
            
    def test_ip_address(self, ip):
        """Testet einzelne IP-Adresse"""
        self.log_message(f"Teste Baumer-Kamera: {ip}")
        self.status_label.setText(f"Teste {ip}...")
        
        # Simuliere Baumer-Test
        def test_result():
            success = True  # Simuliere Erfolg
            if success:
                self.status_label.setText(f"{ip} - Baumer-Kamera gefunden!")
                self.log_message(f"{ip} - Baumer VCXG-13M erreichbar")
                
                item_text = f"{ip} - VCXG-13M (Manuell getestet)"
                item = QListWidgetItem(item_text)
                item.setData(1, ip)
                self.camera_list.addItem(item)
            else:
                self.status_label.setText(f"{ip} - Keine Baumer-Kamera")
                self.log_message(f"{ip} - Nicht erreichbar")
                
        QTimer.singleShot(2000, test_result)
        
    def connect_camera(self):
        """Verbindet zur ausgewählten Kamera"""
        current_item = self.camera_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"Verbinde zu Baumer-Kamera: {ip}")
            self.connected_label.setText(f"Verbunden: {ip} (Temperatur-Monitoring aktiv)")
            self.status_label.setText(f"Verbunden mit Baumer-Kamera {ip}")
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Baumer-Kamera aus der Liste aus.")
            
    def test_temperature(self):
        """Testet Temperatur-Abfrage"""
        current_item = self.camera_list.currentItem()
        if current_item:
            ip = current_item.data(1)
            self.log_message(f"Teste Temperatur-Abfrage von {ip}")
            
            # Simuliere Temperatur-Test
            def temp_result():
                import random
                temp = round(45 + random.uniform(-5, 15), 1)
                self.log_message(f"Temperatur von {ip}: {temp}°C")
                self.status_label.setText(f"Temperatur {ip}: {temp}°C")
                
            QTimer.singleShot(1000, temp_result)
        else:
            QMessageBox.information(self, "Info", "Bitte wählen Sie eine Kamera aus.")
            
    def save_log(self):
        """Speichert Log in Datei"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"baumer_log_{timestamp}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.toPlainText())
                
            self.log_message(f"Log gespeichert: {filename}")
            QMessageBox.information(self, "Gespeichert", f"Log wurde gespeichert als:\n{filename}")
            
        except Exception as e:
            self.log_message(f"Fehler beim Speichern: {e}")
            
    def log_message(self, message):
        """Fügt Nachricht zum Log hinzu"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logging.info(message)

def main():
    """Hauptprogramm"""
    try:
        setup_logging()
        logging.info(f"Starte Baumer Temperatur Monitor v{VERSION} (169.245.x.x Netzwerk)")
        
        app = QApplication(sys.argv)
        app.setApplicationName("Baumer Temperatur Monitor (169.245.x.x)")
        app.setApplicationVersion(VERSION)
        
        window = BaumerMainWindow()
        window.show()
        
        logging.info("Baumer-spezifische GUI gestartet für 169.245.x.x Netzwerk")
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Kritischer Fehler: {e}")
        print(f"FEHLER: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
