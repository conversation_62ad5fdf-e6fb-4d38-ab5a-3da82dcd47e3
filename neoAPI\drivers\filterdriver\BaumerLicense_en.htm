<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=us-ascii">
<TITLE></TITLE>
<META NAME="GENERATOR" CONTENT="OpenOffice 4.1.1  (FreeBSD/amd64)">
<META NAME="CREATED" CONTENT="20180212;8005200">
<META NAME="CHANGED" CONTENT="0;0">
<STYLE TYPE="text/css">
	<!--
		@page { margin: 1in }
		P { margin-bottom: 0.08in; line-height: 115%; text-align: left }
		P.western { font-family: "Calibri", sans-serif; font-size: 11pt; so-language: de-DE }
		P.cjk { font-family: "Times New Roman", serif; font-size: 11pt; so-language: ja-<PERSON> }
		P.ctl { font-family: "Calibri", sans-serif; font-size: 11pt; so-language: ar-SA }
		A:link { color: #0000ff }
		A.ctl:link { font-family: "Times New Roman", serif }
	-->
	</STYLE>
</HEAD>
<BODY  LINK="#0000ff" DIR="LTR" STYLE="border: none; padding: 0in">

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>LICENSE
AGREEMENT (&ldquo;EULA&rdquo; End User License Agreement)</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
IMPORTANT,
PLEASE READ CAREFULLY: This is a legally binding agreement between
the person, company, or organization which has obtained the software
(hereinafter: &ldquo;You&rdquo;) and Baumer Optronic GmbH
(hereinafter: &ldquo;Baumer&rdquo;) governing the use of the software
&ldquo;Baumer GAPI SDK&rdquo; (hereinafter: the &ldquo;Software&rdquo;).</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Your
use of the Software constitutes your agreement with the terms and
conditions of this agreement (EULA).</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
The
Software comprises the Software which can be executed on a computer,
any enclosed media, and the user documentation in both its printed
and electronic forms.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Baumer
retains sole proprietary title to the Software, which is protected by
the copyright laws of Germany and international treaty provisions.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Any
and all rights to the Software and its intellectual property belong
to and are retained by Baumer. You are licensing, not purchasing the
Software.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>SCOPE AND RESTRICTIONS OF LICENSE.</B></P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
1. Permission
is hereby granted, free of charge, to any person obtaining a copy of
this Software to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the software, and to
permit persons to whom the Software is furnished to do so, subject
to the following conditions:</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
The above copyright notice and this permission notice shall be included
in all copies or substantial portions of the Software.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
2. Term and termination: this agreement (&ldquo;EULA&rdquo;) concerning
the utilization of the Software provided to You is valid for an
indefinite term, provided that there is no infringement on other
rights. Baumer reserves the right to terminate this EULA if and when
you are in breach of the provisions contained therein. In this case,
any and all copies of the Software and its individual components must
be destroyed.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>OTHER
RIGHTS AND RESTRICTIONS</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
1. Intellectual property: any and all rights, including, but not limited
to, copyrights, business secrets, patents, and trademarks related to
the Software and to any and all images which may be integrated in the
Software, accompanying printed material, and any and all copies of
the Software, are retained by Baumer as the sole and exclusive owner
of the intellectual property.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
2. No guarantee and warranty: despite all of the efforts which have been
made to assure the correctness and the usability of the Software and
its conformity with the documentation, Baumer does not assume any
guarantees or warranties, either express or implicit, including, but
not limited to, market capability or suitability for specific
purposes, related to the Software and the relevant written material
to the full extent permissible in accordance with applicable law.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
3. No liability for subsequent damage or loss: under no circumstances
can Baumer be made liable for any loss or damage, regardless of its
nature, suffered because of the use of, or the inability to use, the
Software, even if and when Baumer has been notified of the possible
occurrence of such loss or damage. The above provision includes
without exception loss or damage from lost profit, operational
interruption, loss of business information, or other financial loss.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
4. No liability with respect to third parties: you hereby indemnify and
hold harmless Baumer from and against any and all third-party claims
which third parties may assert against you pursuant to the use of the
Software. Moreover, You shall bear any and all expenditures required
for the defense against such claims.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
5. Baumer grants the right to reverse-engineer, debug, and decompile
proprietary components of the Software for the only purpose of and
only to the extent necessary to achieve the interoperability of the
Software with another independently created computer program. The
distribution of any results (e. g. modified version of components of
the Software) is prohibited. The information gained by
reverse-engineering shall be held by you in strict confidence and
shall not be used for the development, production or marketing of a
computer program substantially similar in its expression, or for any
other act which infringes copyright. Baumer&rsquo;s liability for any
damages or losss occurring in conjunction with reverse-engineering or
modification of the Software or otherwise is excluded.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
6. Modifications: this agreement or EULA may not be modified in favor of
one party, whether by addendum or by deletion or amendment of the
text in any way, unless both parties agree to such modifications in
writing.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
7. Partial validity: should any part of this EULA be declared invalid,
illegal, unenforceable, or void by a recognized court, the remaining
parts of this EULA shall remain valid in full. However, such partial
validity shall not have any effect if and when it is detrimental to
the commercial benefits of the EULA for Baumer.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
8. Power of representation for execution and acceptance: by accepting
this EULA, You expressly confirm that You have read and understood
this EULA in full and that you accept the obligations pursuant
thereto with respect to Baumer. Furthermore, you hereby declare that
the person accepting this EULA is in possession of the appropriate
power of representation.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>Open Source Software</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Parts of the Baumer GAPI Software utilizes Open-Source Software. 
</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
These Open Source Software are owned by their respective owners, Baumer
does not claim any copyright for these Software.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Where required Baumer provides the source-code as well as any Baumer
specific changes to the source-code for the Open-Source Software. You
can request the source-code from the Baumer Support (<EMAIL>)
for a minimum of 3 years after the Baumer GAPI SDK was released.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Following you can find the list of utilized Open-Source Software as well as
Copyright Notices and Licenses in no particular order.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>GenICam
Runtime Version</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
http://www.emva.org/</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2005-2018, EMVA and contributors (see source files). All rights
reserved.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Redistribution
and use in source and binary forms, without modification, are
permitted provided that the following conditions are met:</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
1.
Redistributions of source code must retain the above copyright
notice, this list of conditions and the following disclaimer. 
</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
2.
Redistributions in binary form must reproduce the above
copyright notice, this list of conditions and the following
disclaimer in the documentation and/or other materials provided with
the distribution. 
</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
3.
Neither the name of the GenICam standard group nor the
names of its contributors may be used to endorse or promote
products derived from this software without specific prior written
permission. 
</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
THIS
SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS
IS&quot; AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>Math
Parser</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
http://kirya.narod.ru/mathparser.html</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) Kirill Zaborski, <a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="7d07160b3d101c1411530f0853">[email&#160;protected]</a> 
</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
This
Software is distributed under the LGPL v.3. A copy of this license is
available below.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>Log4cpp</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
http://log4cpp.sourceforge.net</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) 2000-2002 LifeLine Networks bv, Copyright (C) 2000-2002 Bastiaan
Bakker Portions Copyright others, see file THANKS and source code. 
</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
This
Software is distributed under the LGPL v.3. A copy of this license is
available below.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>QT</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
The
following parts of the software use Qt:</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
- Baumer Camera Explorer</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
- Baumer Driver Manager</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
- Baumer Update Tool</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
- Baumer IP Config Tool</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://www.qt.io/</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright (C) 2015 The QT Company Ltd.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
This Software is distributed under the LGPL v.3. A copy of this license is
available below.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>QTPropertyBrowser</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://github.com/intbots/QtPropertyBrowser</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) 2010 Nokia Corporation and/or its subsidiary(-ies). All rights
reserved.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
This
Software is distributed under the 3-clause BSD Licence. A copy of
this license is available below.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>Json
Spirit</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://github.com/sirikata/json-spirit</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2007 - 2010 John W. Wilkinson. 
</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
This
Software is distributed under the MIT License. A copy of this license
is available below.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>Md5
Message Digest</B></P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >https://www.rsa.com</SPAN></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) 1991-2, RSA Data Security, Inc. Created 1991. All rights
reserved.</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >License
to copy and use this software is granted provided that it is
identified as the &quot;RSA Data Security, Inc. MD5 Message-Digest
Algorithm&quot; in all material mentioning or referencing this
software or this function.</SPAN></P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >License
is also granted to make and use derivative works provided that such
works are identified as &quot;derived from the RSA Data Security,
Inc. MD5 Message-Digest Algorithm&quot; in all material mentioning or
referencing the derived work.</SPAN></P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >RSA
Data Security, Inc. makes no representations concerning either the
merchantability of this software or the suitability of this software
for any particular purpose. It is provided &quot;as is&quot; without
express or implied warranty of any kind.</SPAN></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
These
notices must be retained in any copies of any part of this
documentation and/or software.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>OpenJpeg</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://github.com/uclouvain/openjpeg</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >The
copyright in this software is being made available under the
2-clauses BSD License. A copy of this license is available below.
This software may be subject to other third party and contributor
rights, including patent rights, and no such rights are granted under
this license.</SPAN></P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2002-2014, Universite catholique de Louvain (UCL), Belgium</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2002-2014, Professor Benoit Macq</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2003-2014, Antonin Descampe</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2003-2009, Francois-Olivier Devaux</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >Copyright
(c) 2005, Herve Drolon, FreeImage Team</SPAN></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2002-2003, Yannick Verschueren</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2001-2003, David Janssens</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2011-2012, Centre National d'Etudes Spatiales (CNES), France 
</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) 2012, CS Systemes d'Information, France</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >All
rights reserved.</SPAN></P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>OpenCV</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://github.com/opencv/</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) 2000-2016, Intel Corporation, all rights reserved.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) 2009-2011, Willow Garage Inc., all rights reserved.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) 2009-2016, NVIDIA Corporation, all rights reserved.</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >Copyright
(C) 2010-2013, Advanced Micro Devices, Inc., all rights reserved.</SPAN></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) 2015-2016, OpenCV Foundation, all rights reserved.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) 2015-2016, Itseez Inc., all rights reserved.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Third
party copyrights are property of their respective owners.</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >This
Software is distributed under the 3-clause BSD License. A copy of
this license is available below.</SPAN></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>FFmpeg</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://www.ffmpeg.org</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
This
Software is distributed under the LGPL v.3. A copy of this license is
available below.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>Cmake</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://cmake.org</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
2000-2017 Kitware, Inc. and Contributors, All rights reserved. 
</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
This
Software is distributed under the 3-clause BSD License. A copy of
this license is available below. 
</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>Open Source Licenses</B></P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>LGPL
v.3</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://opensource.org/licenses/LGPL-3.0</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
GNU LESSER GENERAL PUBLIC LICENSE</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Version 3, 29 June 2007</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(C) 2007 Free Software Foundation, Inc. &lt;https://fsf.org/&gt;</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Everyone
is permitted to copy and distribute verbatim copies</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
of
this license document, but changing it is not allowed.</P>


<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
This
version of the GNU Lesser General Public License incorporates</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
the
terms and conditions of version 3 of the GNU General Public</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
License,
supplemented by the additional permissions listed below.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
0.
Additional Definitions.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
As
used herein, &quot;this License&quot; refers to version 3 of the GNU
Lesser</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
General
Public License, and the &quot;GNU GPL&quot; refers to version 3 of
the GNU</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
General
Public License.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
&quot;The
Library&quot; refers to a covered work governed by this License,</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
other
than an Application or a Combined Work as defined below.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
An
&quot;Application&quot; is any work that makes use of an interface
provided</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
by
the Library, but which is not otherwise based on the Library.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Defining
a subclass of a class defined by the Library is deemed a mode</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
of
using an interface provided by the Library.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
A
&quot;Combined Work&quot; is a work produced by combining or linking
an</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Application
with the Library. The particular version of the Library</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
with
which the Combined Work was made is also called the &quot;Linked</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Version&quot;.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
The
&quot;Minimal Corresponding Source&quot; for a Combined Work means
the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Corresponding
Source for the Combined Work, excluding any source code</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
for
portions of the Combined Work that, considered in isolation, are</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
based
on the Application, and not on the Linked Version.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
The
&quot;Corresponding Application Code&quot; for a Combined Work means
the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
object
code and/or source code for the Application, including any data</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
and
utility programs needed for reproducing the Combined Work from the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Application,
but excluding the System Libraries of the Combined Work.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
1.
Exception to Section 3 of the GNU GPL.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
You
may convey a covered work under sections 3 and 4 of this License</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
without
being bound by section 3 of the GNU GPL.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
2.
Conveying Modified Versions.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
If
you modify a copy of the Library, and, in your modifications, a</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
facility
refers to a function or data to be supplied by an Application</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
that
uses the facility (other than as an argument passed when the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
facility
is invoked), then you may convey a copy of the modified</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
version:</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
a)
under this License, provided that you make a good faith effort to</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
ensure
that, in the event an Application does not supply the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
function
or data, the facility still operates, and performs</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
whatever
part of its purpose remains meaningful, or</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
b)
under the GNU GPL, with none of the additional permissions of</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
this
License applicable to that copy.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
3.
Object Code Incorporating Material from Library Header Files.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
The
object code form of an Application may incorporate material from</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
a
header file that is part of the Library. You may convey such object</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
code
under terms of your choice, provided that, if the incorporated</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
material
is not limited to numerical parameters, data structure</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
layouts
and accessors, or small macros, inline functions and templates</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
(ten
or fewer lines in length), you do both of the following:</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
a)
Give prominent notice with each copy of the object code that the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Library
is used in it and that the Library and its use are</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
covered
by this License.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
b)
Accompany the object code with a copy of the GNU GPL and this license</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
document.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
4.
Combined Works.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
You
may convey a Combined Work under terms of your choice that,</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
taken
together, effectively do not restrict modification of the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
portions
of the Library contained in the Combined Work and reverse</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
engineering
for debugging such modifications, if you also do each of</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
the
following:</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
a)
Give prominent notice with each copy of the Combined Work that</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
the
Library is used in it and that the Library and its use are</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
covered
by this License.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
b)
Accompany the Combined Work with a copy of the GNU GPL and this
license</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
document.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
c)
For a Combined Work that displays copyright notices during</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
execution,
include the copyright notice for the Library among</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
these
notices, as well as a reference directing the user to the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
copies
of the GNU GPL and this license document.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
d)
Do one of the following:</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
0)
Convey the Minimal Corresponding Source under the terms of this</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
License,
and the Corresponding Application Code in a form</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
suitable
for, and under terms that permit, the user to</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
recombine
or relink the Application with a modified version of</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
the
Linked Version to produce a modified Combined Work, in the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
manner
specified by section 6 of the GNU GPL for conveying</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Corresponding
Source.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
1)
Use a suitable shared library mechanism for linking with the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Library.
A suitable mechanism is one that (a) uses at run time</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
a
copy of the Library already present on the user's computer</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
system,
and (b) will operate properly with a modified version</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
of
the Library that is interface-compatible with the Linked</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Version.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
e)
Provide Installation Information, but only if you would otherwise</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
be
required to provide such information under section 6 of the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
GNU
GPL, and only to the extent that such information is</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
necessary
to install and execute a modified version of the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Combined
Work produced by recombining or relinking the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Application
with a modified version of the Linked Version. (If</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
you
use option 4d0, the Installation Information must accompany</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
the
Minimal Corresponding Source and Corresponding Application</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Code.
If you use option 4d1, you must provide the Installation</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Information
in the manner specified by section 6 of the GNU GPL</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
for
conveying Corresponding Source.)</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
5.
Combined Libraries.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
You
may place library facilities that are a work based on the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Library
side by side in a single library together with other library</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
facilities
that are not Applications and are not covered by this</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
License,
and convey such a combined library under terms of your</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
choice,
if you do both of the following:</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
a)
Accompany the combined library with a copy of the same work based</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
on
the Library, uncombined with any other library facilities,</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
conveyed
under the terms of this License.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
b)
Give prominent notice with the combined library that part of it</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
is
a work based on the Library, and explaining where to find the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
accompanying
uncombined form of the same work.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
6.
Revised Versions of the GNU Lesser General Public License.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
The
Free Software Foundation may publish revised and/or new versions</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
of
the GNU Lesser General Public License from time to time. Such new</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
versions
will be similar in spirit to the present version, but may</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
differ
in detail to address new problems or concerns.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Each
version is given a distinguishing version number. If the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Library
as you received it specifies that a certain numbered version</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
of
the GNU Lesser General Public License &quot;or any later version&quot;</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
applies
to it, you have the option of following the terms and</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
conditions
either of that published version or of any later version</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
published
by the Free Software Foundation. If the Library as you</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
received
it does not specify a version number of the GNU Lesser</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
General
Public License, you may choose any version of the GNU Lesser</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
General
Public License ever published by the Free Software Foundation.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
If
the Library as you received it specifies that a proxy can decide</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
whether
future versions of the GNU Lesser General Public License shall</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
apply,
that proxy's public statement of acceptance of any version is</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
permanent
authorization for you to choose that version for the Library.</P>


<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>BSD
2-Clause</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://opensource.org/licenses/BSD-2-Clause</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) &lt;year&gt;, &lt;copyright holder&gt;</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
All
rights reserved.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Redistribution
and use in source and binary forms, with or without</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
modification,
are permitted provided that the following conditions are met:</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
1.
Redistributions of source code must retain the above copyright
notice, this</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
list
of conditions and the following disclaimer.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
2.
Redistributions in binary form must reproduce the above copyright
notice,</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
this
list of conditions and the following disclaimer in the documentation</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
and/or
other materials provided with the distribution.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
THIS
SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS
IS&quot; AND</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
ANY
EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
WARRANTIES
OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
DISCLAIMED.
IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
ANY
DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
(INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
LOSS
OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
AND</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR
TORT</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
(INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
SOFTWARE,
EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
The
views and conclusions contained in the software and documentation are
those</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
of
the authors and should not be interpreted as representing official
policies,</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
either
expressed or implied, of the FreeBSD Project.</P>


<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>BSD
3-Clause</B></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://opensource.org/licenses/BSD-3-Clause</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) &lt;year&gt;, &lt;copyright holder&gt;</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
All
rights reserved.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Redistribution
and use in source and binary forms, with or without</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
modification,
are permitted provided that the following conditions are met:</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
*
Redistributions of source code must retain the above copyright</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
notice,
this list of conditions and the following disclaimer.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
*
Redistributions in binary form must reproduce the above copyright</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
notice,
this list of conditions and the following disclaimer in the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
documentation
and/or other materials provided with the distribution.</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
*
Neither the name of the &lt;organization&gt; nor the</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
names
of its contributors may be used to endorse or promote products</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >derived
from this software without specific prior written permission.</SPAN></P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
THIS
SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS
IS&quot; AND</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
ANY
EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
WARRANTIES
OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >DISCLAIMED.
IN NO EVENT SHALL &lt;COPYRIGHT HOLDER&gt; BE LIABLE FOR ANY</SPAN></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
(INCLUDING,
BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >LOSS
OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
AND</SPAN></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
ON
ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR
TORT</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
(INCLUDING
NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
SOFTWARE,
EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</P>


<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<BR>
<B>MIT
Licence</B></P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
https://opensource.org/licenses/MIT</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Copyright
(c) &lt;year&gt; &lt;copyright holders&gt;</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
Permission
is hereby granted, free of charge, to any person obtaining a copy</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
of
this software and associated documentation files (the &quot;Software&quot;),
to deal</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >in
the Software without restriction, including without limitation the
rights</SPAN></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
to
use, copy, modify, merge, publish, distribute, sublicense, and/or
sell</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
copies
of the Software, and to permit persons to whom the Software is</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >furnished
to do so, subject to the following conditions:</SPAN></P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
The
above copyright notice and this permission notice shall be included
in all</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
copies
or substantial portions of the Software.</P>

<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
THE
SOFTWARE IS PROVIDED &quot;AS IS&quot;, WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR</P>
<P  CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
<SPAN >IMPLIED,
INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,</SPAN></P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
AUTHORS
OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
LIABILITY,
WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
OUT
OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE</P>
<P CLASS="western" STYLE="margin-left: 0.2in; margin-bottom: 0in; line-height: 100%">
SOFTWARE.</P>

<P CLASS="western" STYLE="margin-left: 0.1in; margin-bottom: 0in; line-height: 100%">
</P>
<script data-cfasync="false" src="/cdn-cgi/scripts/d07b1474/cloudflare-static/email-decode.min.js"></script></BODY>
</HTML>